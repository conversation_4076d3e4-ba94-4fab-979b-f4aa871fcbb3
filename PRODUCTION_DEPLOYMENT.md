# DeepDocX Production Deployment Guide

## Overview

This guide covers deploying DeepDocX to production using the `api.lightray-technologies.com` backend endpoint.

## Production URLs

- **Frontend**: `https://deepdocx.lightray-technologies.com`
- **Backend API**: `https://api.lightray-technologies.com`

## Frontend Configuration

### Environment Variables

The frontend is already configured to use the production API endpoint. The following files have been updated:

- `.env.local`: `NEXT_PUBLIC_API_URL=https://api.lightray-technologies.com/api`
- `.env.example`: Updated with production URL
- `lib/api/client.ts`: Default fallback URL updated

### Deployment Steps

1. **Build the frontend**:
   ```bash
   npm run build
   ```

2. **Deploy to your hosting platform** (Vercel, Netlify, etc.)

3. **Set environment variables** in your hosting platform:
   ```
   NEXT_PUBLIC_API_URL=https://api.lightray-technologies.com/api
   ```

## Backend Configuration

### Environment Setup

1. **Copy production environment file**:
   ```bash
   cd backend-deepdocx
   cp .env.production .env
   ```

2. **Update environment variables** in `.env`:
   ```env
   # Required - Update these with your actual values
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   OPENAI_API_KEY=your-openai-api-key
   SECRET_KEY=your-super-secret-key-change-this-in-production
   JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
   
   # OAuth (if using)
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   GITHUB_CLIENT_ID=your-github-client-id
   GITHUB_CLIENT_SECRET=your-github-client-secret
   ```

### OAuth Configuration

#### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Update OAuth 2.0 credentials
3. Add redirect URI: `https://api.lightray-technologies.com/api/auth/google/callback`

#### GitHub OAuth
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Update OAuth App settings
3. Set Authorization callback URL: `https://api.lightray-technologies.com/api/auth/github/callback`

### Deployment Options

#### Option 1: Docker Deployment
```bash
# Build Docker image
docker build -t deepdocx-backend .

# Run container
docker run -d \
  --name deepdocx-backend \
  -p 5000:5000 \
  --env-file .env \
  deepdocx-backend
```

#### Option 2: Direct Deployment
```bash
# Install dependencies
pip install -r requirements.txt

# Install LaTeX packages
chmod +x install_latex_packages.sh
./install_latex_packages.sh

# Run with gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### Option 3: Systemd Service
Create `/etc/systemd/system/deepdocx.service`:
```ini
[Unit]
Description=DeepDocX Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/backend-deepdocx
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/gunicorn -w 4 -b 0.0.0.0:5000 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable deepdocx
sudo systemctl start deepdocx
```

## SSL/HTTPS Configuration

### Using Nginx as Reverse Proxy

Create `/etc/nginx/sites-available/api.lightray-technologies.com`:
```nginx
server {
    listen 80;
    server_name api.lightray-technologies.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name api.lightray-technologies.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/api.lightray-technologies.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Database Setup

1. **Create Supabase project** at [supabase.com](https://supabase.com)
2. **Run database schema**:
   ```sql
   -- Copy and run the contents of backend-deepdocx/database_schema.sql
   ```
3. **Configure Row Level Security** (RLS) policies
4. **Update environment variables** with Supabase credentials

## Monitoring and Logging

### Application Logs
```bash
# View logs
tail -f logs/app.log

# Or with systemd
sudo journalctl -u deepdocx -f
```

### Health Check Endpoint
The backend provides a health check at:
```
GET https://api.lightray-technologies.com/health
```

## Security Checklist

- [ ] HTTPS enabled with valid SSL certificate
- [ ] Strong secret keys generated
- [ ] OAuth redirect URIs updated
- [ ] Supabase RLS policies configured
- [ ] File upload restrictions in place
- [ ] CORS properly configured
- [ ] Environment variables secured
- [ ] Regular security updates applied

## Testing Production Deployment

1. **Test API endpoints**:
   ```bash
   curl https://api.lightray-technologies.com/health
   ```

2. **Test authentication**:
   - Visit frontend and try signing up/in
   - Test OAuth flows

3. **Test document creation**:
   - Create a new document
   - Test LaTeX compilation
   - Test file uploads

4. **Test all major features**:
   - Document versioning
   - File management
   - Sharing functionality

## Troubleshooting

### Common Issues

1. **CORS errors**: Check `FRONTEND_URL` in backend environment
2. **OAuth failures**: Verify redirect URIs match exactly
3. **Database connection**: Check Supabase credentials
4. **LaTeX compilation**: Ensure LaTeX packages are installed
5. **File uploads**: Check upload directory permissions

### Debug Mode

For debugging in production (temporarily):
```env
FLASK_DEBUG=True
LOG_LEVEL=DEBUG
```

**Remember to disable debug mode after troubleshooting!**

## Backup and Recovery

1. **Database backups**: Use Supabase automatic backups
2. **File backups**: Backup upload directories
3. **Environment backups**: Securely store environment configurations

## Updates and Maintenance

1. **Application updates**:
   ```bash
   git pull origin main
   pip install -r requirements.txt
   sudo systemctl restart deepdocx
   ```

2. **LaTeX package updates**:
   ```bash
   sudo apt update && sudo apt upgrade
   ```

3. **Security updates**:
   ```bash
   sudo apt update && sudo apt upgrade
   ```

This deployment guide ensures your DeepDocX application is properly configured for production use with the `api.lightray-technologies.com` endpoint.
