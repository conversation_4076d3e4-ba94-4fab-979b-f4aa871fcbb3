import { apiClient } from "@/lib/api/client"
import type { Document } from "@/types/document"
import type { SharedDocument } from "@/lib/types/database"

// Document sharing service
export class SharingService {
  async shareDocument(
    documentId: string,
    options: {
      email?: string
      permissions: "view" | "comment" | "edit"
      expiresIn?: number // hours
    },
  ): Promise<{ shareUrl: string; shareToken: string }> {
    const expiresAt = options.expiresIn
      ? new Date(Date.now() + options.expiresIn * 60 * 60 * 1000).toISOString()
      : undefined

    const shareData = await apiClient.share.create(documentId, {
      shared_with: options.email,
      permissions: options.permissions,
      expires_at: expiresAt,
    })

    const shareUrl = `${window.location.origin}/shared/${shareData.share_token}`

    return {
      shareUrl,
      shareToken: shareData.share_token!,
    }
  }

  async getSharedDocument(token: string): Promise<{
    document: Document
    permissions: string
  }> {
    return apiClient.share.getByToken(token)
  }

  async revokeShare(shareId: string): Promise<void> {
    await apiClient.share.revoke(shareId)
  }

  async getDocumentShares(documentId: string): Promise<SharedDocument[]> {
    return apiClient.share.list(documentId)
  }

  generatePublicLink(documentId: string): string {
    return `${window.location.origin}/public/${documentId}`
  }

  async copyToClipboard(text: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(text)
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement("textarea")
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand("copy")
      document.body.removeChild(textArea)
    }
  }
}

export const sharingService = new SharingService()
