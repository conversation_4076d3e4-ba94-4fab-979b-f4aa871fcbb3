// Frontend LaTeX to HTML compilation service using KaTeX
import katex from 'katex'

export class LatexService {
  private static instance: LatexService
  private compilationQueue: Map<string, Promise<any>> = new Map()

  static getInstance(): LatexService {
    if (!LatexService.instance) {
      LatexService.instance = new LatexService()
    }
    return LatexService.instance
  }

  async compileLatex(
    documentId: string,
    latexCode: string,
  ): Promise<{
    success: boolean
    htmlContent?: string
    errors?: string[]
  }> {
    // Prevent duplicate compilations
    if (this.compilationQueue.has(documentId)) {
      return this.compilationQueue.get(documentId)!
    }

    const compilationPromise = this.performCompilation(documentId, latexCode)
    this.compilationQueue.set(documentId, compilationPromise)

    try {
      const result = await compilationPromise
      return result
    } finally {
      this.compilationQueue.delete(documentId)
    }
  }

  private async performCompilation(
    documentId: string,
    latexCode: string,
  ): Promise<{
    success: boolean
    htmlContent?: string
    errors?: string[]
  }> {
    try {
      // Parse LaTeX and convert to HTML
      const parsedContent = this.parseLatexToStructure(latexCode)

      // Generate beautiful HTML
      const htmlContent = this.generateHTML(parsedContent)

      return {
        success: true,
        htmlContent: htmlContent,
        errors: [],
      }
    } catch (error) {
      console.error("LaTeX compilation failed:", error)
      return {
        success: false,
        errors: [error instanceof Error ? error.message : "LaTeX compilation failed"],
      }
    }
  }

  private parseLatexToStructure(latexCode: string): {
    documentClass?: string
    packages: Array<{ name: string; options?: string }>
    customCommands: Array<{ name: string; definition: string }>
    colors: Map<string, string>
    geometry?: string
    title?: string
    author?: string
    date?: string
    abstract?: string
    sections: Array<{ title: string; content: string; level: number; id: string }>
    bibliography?: Array<{ key: string; entry: string }>
    customStyles: Map<string, any>
  } {
    const lines = latexCode.split('\n')
    const result = {
      documentClass: '',
      packages: [] as Array<{ name: string; options?: string }>,
      customCommands: [] as Array<{ name: string; definition: string }>,
      colors: new Map<string, string>(),
      geometry: '',
      title: '',
      author: '',
      date: '',
      abstract: '',
      sections: [] as Array<{ title: string; content: string; level: number; id: string }>,
      bibliography: [] as Array<{ key: string; entry: string }>,
      customStyles: new Map<string, any>()
    }

    let currentSection = { title: '', content: '', level: 0, id: '' }
    let inDocument = false
    let inAbstract = false
    let inPreamble = true
    let braceLevel = 0
    let currentEnvironment = ''

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const trimmedLine = line.trim()

      // Parse document class
      const docClassMatch = trimmedLine.match(/\\documentclass(?:\[([^\]]+)\])?\{([^}]+)\}/)
      if (docClassMatch) {
        result.documentClass = docClassMatch[2]
        continue
      }

      // Parse packages
      const packageMatch = trimmedLine.match(/\\usepackage(?:\[([^\]]+)\])?\{([^}]+)\}/)
      if (packageMatch) {
        result.packages.push({
          name: packageMatch[2],
          options: packageMatch[1]
        })
        continue
      }

      // Parse geometry
      const geometryMatch = trimmedLine.match(/\\geometry\{([^}]+)\}/)
      if (geometryMatch) {
        result.geometry = geometryMatch[1]
        continue
      }

      // Parse color definitions
      const colorMatch = trimmedLine.match(/\\definecolor\{([^}]+)\}\{([^}]+)\}\{([^}]+)\}/)
      if (colorMatch) {
        const colorName = colorMatch[1]
        const colorValue = this.parseColorValue(colorMatch[2], colorMatch[3])
        result.colors.set(colorName, colorValue)
        continue
      }

      // Parse custom commands
      const newCommandMatch = trimmedLine.match(/\\newcommand\{([^}]+)\}(?:\[([^\]]+)\])?\{([^}]+)\}/)
      if (newCommandMatch) {
        result.customCommands.push({
          name: newCommandMatch[1],
          definition: newCommandMatch[3]
        })
        continue
      }

      // Parse title, author, date
      const titleMatch = trimmedLine.match(/\\title\{([^}]+)\}/)
      if (titleMatch) {
        result.title = this.cleanLatexText(titleMatch[1])
        continue
      }

      const authorMatch = trimmedLine.match(/\\author\{([^}]+)\}/)
      if (authorMatch) {
        result.author = this.cleanLatexText(authorMatch[1])
        continue
      }

      const dateMatch = trimmedLine.match(/\\date\{([^}]+)\}/)
      if (dateMatch) {
        result.date = this.cleanLatexText(dateMatch[1])
        continue
      }

      // Handle document environment
      if (trimmedLine.includes('\\begin{document}')) {
        inDocument = true
        inPreamble = false
        continue
      }

      if (trimmedLine.includes('\\end{document}')) {
        if (currentSection.content.trim()) {
          result.sections.push({ ...currentSection })
        }
        break
      }

      if (!inDocument) continue

      // Handle environments
      const beginEnvMatch = trimmedLine.match(/\\begin\{([^}]+)\}/)
      if (beginEnvMatch) {
        currentEnvironment = beginEnvMatch[1]

        if (currentEnvironment === 'abstract') {
          inAbstract = true
          continue
        }

        // Handle other environments (algorithm, algorithmic, tcolorbox, etc.)
        if (['algorithm', 'algorithmic', 'tcolorbox', 'enumerate', 'itemize', 'tabular', 'figure', 'center'].includes(currentEnvironment)) {
          currentSection.content += this.processEnvironment(lines, i, currentEnvironment)
          // Skip to end of environment
          let envLevel = 1
          for (let j = i + 1; j < lines.length; j++) {
            if (lines[j].includes(`\\begin{${currentEnvironment}}`)) envLevel++
            if (lines[j].includes(`\\end{${currentEnvironment}}`)) {
              envLevel--
              if (envLevel === 0) {
                i = j
                break
              }
            }
          }
          continue
        }
      }

      const endEnvMatch = trimmedLine.match(/\\end\{([^}]+)\}/)
      if (endEnvMatch && endEnvMatch[1] === 'abstract') {
        inAbstract = false
        continue
      }

      if (inAbstract) {
        result.abstract += this.processLatexContent(trimmedLine) + ' '
        continue
      }

      // Skip maketitle and other commands
      if (trimmedLine.includes('\\maketitle') ||
          trimmedLine.includes('\\pagestyle') ||
          trimmedLine.includes('\\fancyhf') ||
          trimmedLine.includes('\\fancyhead') ||
          trimmedLine.includes('\\fancyfoot')) {
        continue
      }

      // Handle sections with better parsing
      const sectionMatch = trimmedLine.match(/\\(chapter|section|subsection|subsubsection|paragraph)\*?\{([^}]+)\}/)
      if (sectionMatch) {
        // Save previous section
        if (currentSection.content.trim()) {
          result.sections.push({ ...currentSection })
        }

        // Determine section level
        const sectionType = sectionMatch[1]
        let level = 1
        switch (sectionType) {
          case 'chapter': level = 0; break
          case 'section': level = 1; break
          case 'subsection': level = 2; break
          case 'subsubsection': level = 3; break
          case 'paragraph': level = 4; break
        }

        const title = this.cleanLatexText(sectionMatch[2])
        currentSection = {
          title: title,
          content: '',
          level: level,
          id: this.generateSectionId(title)
        }
        continue
      }

      // Process regular content
      if (trimmedLine && !trimmedLine.startsWith('\\') && !trimmedLine.startsWith('%')) {
        const processedLine = this.processLatexContent(trimmedLine)
        // Avoid adding duplicate content
        if (!currentSection.content.includes(processedLine)) {
          currentSection.content += processedLine + ' '
        }
      }
    }

    // Add final section
    if (currentSection.content.trim()) {
      result.sections.push(currentSection)
    }

    return result
  }

  private parseColorValue(colorModel: string, colorValue: string): string {
    switch (colorModel.toLowerCase()) {
      case 'rgb':
        const rgbValues = colorValue.split(',').map(v => parseInt(v.trim()))
        return `rgb(${rgbValues.join(', ')})`
      case 'html':
        return colorValue.startsWith('#') ? colorValue : `#${colorValue}`
      case 'gray':
        const grayValue = Math.round(parseFloat(colorValue) * 255)
        return `rgb(${grayValue}, ${grayValue}, ${grayValue})`
      default:
        return colorValue
    }
  }

  private cleanLatexText(text: string): string {
    // Handle complex color and font commands first
    text = this.processComplexFormatting(text)

    // Handle basic text formatting
    text = text
      .replace(/\\textbf\{([^}]+)\}/g, '<strong>$1</strong>')
      .replace(/\\textit\{([^}]+)\}/g, '<em>$1</em>')
      .replace(/\\emph\{([^}]+)\}/g, '<em>$1</em>')
      .replace(/\\texttt\{([^}]+)\}/g, '<code>$1</code>')
      .replace(/\\underline\{([^}]+)\}/g, '<u>$1</u>')
      .replace(/\\textcolor\{([^}]+)\}\{([^}]+)\}/g, '<span style="color: var(--$1, $1)">$2</span>')

      // Handle framed boxes
      .replace(/\\fbox\{([^}]+)\}/g, '<span class="fbox">$1</span>')

      // Handle size commands with proper closing
      .replace(/\\large\s+([^\\]+)/g, '<span class="large">$1</span>')
      .replace(/\\Large\s+([^\\]+)/g, '<span class="Large">$1</span>')
      .replace(/\\LARGE\s+([^\\]+)/g, '<span class="LARGE">$1</span>')
      .replace(/\\huge\s+([^\\]+)/g, '<span class="huge">$1</span>')
      .replace(/\\Huge\s+([^\\]+)/g, '<span class="Huge">$1</span>')
      .replace(/\\small\s+([^\\]+)/g, '<span class="small">$1</span>')
      .replace(/\\footnotesize\s+([^\\]+)/g, '<span class="footnotesize">$1</span>')
      .replace(/\\scriptsize\s+([^\\]+)/g, '<span class="scriptsize">$1</span>')
      .replace(/\\tiny\s+([^\\]+)/g, '<span class="tiny">$1</span>')

      // Handle line breaks and spacing
      .replace(/\\\\(\[[^\]]*\])?/g, '<br>')
      .replace(/\\vspace\*?\{[^}]+\}/g, '<div class="vspace"></div>')
      .replace(/\\hspace\{[^}]+\}/g, '<span class="hspace"></span>')
      .replace(/~/g, '&nbsp;')

      // Handle quotes
      .replace(/``/g, '"')
      .replace(/''/g, '"')
      .replace(/`/g, "'")
      .replace(/'/g, "'")

      // Handle special characters
      .replace(/\\&/g, '&amp;')
      .replace(/\\%/g, '%')
      .replace(/\\#/g, '#')
      .replace(/\\\$/g, '$')
      .replace(/\\{/g, '{')
      .replace(/\\}/g, '}')

    return text
  }

  private processComplexFormatting(text: string): string {
    // Handle complex color and font size combinations like {\color{titlecolor}\fontsize{22}{26}\selectfont...}
    text = text.replace(/\{\\color\{([^}]+)\}\\fontsize\{([^}]+)\}\{[^}]+\}\\selectfont([^}]+)\}/g,
      '<span style="color: var(--$1, $1); font-size: $2pt;">$3</span>')

    // Handle simpler color commands {\color{colorname}text}
    text = text.replace(/\{\\color\{([^}]+)\}([^}]+)\}/g,
      '<span style="color: var(--$1, $1)">$2</span>')

    // Handle font size commands
    text = text.replace(/\\fontsize\{([^}]+)\}\{[^}]+\}\\selectfont/g,
      '<span style="font-size: $1pt;">')

    // Handle center environment
    text = text.replace(/\\begin\{center\}([\s\S]*?)\\end\{center\}/g,
      '<div class="center">$1</div>')

    return text
  }

  private generateSectionId(title: string): string {
    return title.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50)
  }

  private processLatexContent(text: string): string {
    // First clean basic LaTeX commands
    text = this.cleanLatexText(text)

    // Handle math expressions
    text = this.processMathExpressions(text)

    // Handle citations and references
    text = text.replace(/\\cite\{([^}]+)\}/g, '<sup class="citation">[$1]</sup>')
    text = text.replace(/\\ref\{([^}]+)\}/g, '<a href="#$1" class="reference">$1</a>')
    text = text.replace(/\\label\{([^}]+)\}/g, '<span id="$1"></span>')

    // Handle footnotes
    text = text.replace(/\\footnote\{([^}]+)\}/g, '<sup class="footnote">*</sup>')

    return text
  }

  private processMathExpressions(text: string): string {
    // Handle display math environments
    text = text.replace(/\\begin\{equation\*?\}([\s\S]*?)\\end\{equation\*?\}/g, (match, math) => {
      try {
        return katex.renderToString(math.trim(), { displayMode: true })
      } catch (error) {
        return `<div class="math-error">${match}</div>`
      }
    })

    text = text.replace(/\\begin\{align\*?\}([\s\S]*?)\\end\{align\*?\}/g, (match, math) => {
      try {
        return katex.renderToString(math.trim(), { displayMode: true })
      } catch (error) {
        return `<div class="math-error">${match}</div>`
      }
    })

    // Handle inline math $...$
    text = text.replace(/\$([^$]+)\$/g, (match, math) => {
      try {
        return katex.renderToString(math, { displayMode: false })
      } catch (error) {
        return `<span class="math-error">${match}</span>`
      }
    })

    // Handle display math $$...$$
    text = text.replace(/\$\$([^$]+)\$\$/g, (match, math) => {
      try {
        return katex.renderToString(math, { displayMode: true })
      } catch (error) {
        return `<div class="math-error">${match}</div>`
      }
    })

    return text
  }

  private processEnvironment(lines: string[], startIndex: number, envName: string): string {
    const envContent = []
    let i = startIndex + 1
    let envLevel = 1

    while (i < lines.length && envLevel > 0) {
      const line = lines[i]
      if (line.includes(`\\begin{${envName}}`)) envLevel++
      if (line.includes(`\\end{${envName}}`)) {
        envLevel--
        if (envLevel === 0) break
      }
      if (envLevel > 0) envContent.push(line)
      i++
    }

    const content = envContent.join('\n')

    switch (envName) {
      case 'algorithm':
        return this.processAlgorithm(content)
      case 'algorithmic':
        return this.processAlgorithmicEnvironment(content)
      case 'tcolorbox':
        return this.processTcolorbox(content)
      case 'enumerate':
      case 'itemize':
        return this.processList(content, envName)
      case 'tabular':
        return this.processTable(content)
      case 'figure':
        return this.processFigure(content)
      case 'center':
        return `<div class="center">${this.processLatexContent(content)}</div>`
      default:
        return `<div class="environment-${envName}">${this.processLatexContent(content)}</div>`
    }
  }

  private processAlgorithmicEnvironment(content: string): string {
    // Process standalone algorithmic environment (without algorithm wrapper)
    const processedContent = this.processAlgorithmicContent(content)

    return `
      <div class="algorithmic-box">
        ${processedContent}
      </div>
    `
  }

  private processAlgorithm(content: string): string {
    // Process algorithmic pseudocode
    let processedContent = this.processAlgorithmicContent(content)

    return `
      <div class="algorithm-box">
        <div class="algorithm-header">Algorithm</div>
        <div class="algorithm-content">
          ${processedContent}
        </div>
      </div>
    `
  }

  private processAlgorithmicContent(content: string): string {
    let processed = content

    // Clean up duplicated expressions first
    processed = this.cleanDuplicatedExpressions(processed)

    // Handle algorithmic commands
    processed = processed
      // Function and procedure declarations
      .replace(/\\Function\{([^}]+)\}\{([^}]+)\}\s*:\s*([^\\]+)/g,
        '<div class="algo-function"><strong>function</strong> $1($2) → $3</div>')
      .replace(/\\Procedure\{([^}]+)\}\{([^}]+)\}/g,
        '<div class="algo-procedure"><strong>procedure</strong> $1($2)</div>')
      .replace(/\\EndFunction/g, '<div class="algo-end"><strong>end function</strong></div>')
      .replace(/\\EndProcedure/g, '<div class="algo-end"><strong>end procedure</strong></div>')

      // Control structures
      .replace(/\\If\{([^}]+)\}/g, '<div class="algo-if"><strong>if</strong> $1 <strong>then</strong></div>')
      .replace(/\\ElsIf\{([^}]+)\}/g, '<div class="algo-elsif"><strong>else if</strong> $1 <strong>then</strong></div>')
      .replace(/\\Else/g, '<div class="algo-else"><strong>else</strong></div>')
      .replace(/\\EndIf/g, '<div class="algo-endif"><strong>end if</strong></div>')

      .replace(/\\While\{([^}]+)\}/g, '<div class="algo-while"><strong>while</strong> $1 <strong>do</strong></div>')
      .replace(/\\EndWhile/g, '<div class="algo-endwhile"><strong>end while</strong></div>')

      .replace(/\\For\{([^}]+)\}/g, '<div class="algo-for"><strong>for</strong> $1 <strong>do</strong></div>')
      .replace(/\\EndFor/g, '<div class="algo-endfor"><strong>end for</strong></div>')

      .replace(/\\Repeat/g, '<div class="algo-repeat"><strong>repeat</strong></div>')
      .replace(/\\Until\{([^}]+)\}/g, '<div class="algo-until"><strong>until</strong> $1</div>')

      // Statements
      .replace(/\\State\s+/g, '<div class="algo-state">')
      .replace(/\\Return\s+([^\\]+)/g, '<div class="algo-return"><strong>return</strong> $1</div>')

      // Comments
      .replace(/\\Comment\{([^}]+)\}/g, '<span class="algo-comment">// $1</span>')

    // Process mathematical expressions in the algorithm
    processed = this.processMathExpressions(processed)

    // Clean up and add proper indentation
    processed = this.addAlgorithmIndentation(processed)

    return processed
  }

  private cleanDuplicatedExpressions(text: string): string {
    // Fix duplicated expressions like "s←0s←0" to "s←0"
    text = text.replace(/([a-zA-Z]+←[^a-zA-Z]+)\1/g, '$1')

    // Fix duplicated expressions like "x≠0x=0" to "x≠0"
    text = text.replace(/([a-zA-Z]+[≠=<>]+[0-9]+)[a-zA-Z]+[≠=<>]+[0-9]+/g, '$1')

    // Fix duplicated expressions like "rmod2=0rmod2=0" to "r mod 2=0"
    text = text.replace(/([a-zA-Z]+)mod\s*([0-9]+)=([0-9]+)\1mod\s*\2=\3/g, '$1 mod $2=$3')

    // Fix duplicated expressions like "xmod10xmod10" to "x mod 10"
    text = text.replace(/([a-zA-Z]+)mod\s*([0-9]+)\1mod\s*\2/g, '$1 mod $2')

    // Fix duplicated expressions like "s+rs+r" to "s+r"
    text = text.replace(/([a-zA-Z]+[+\-*/][a-zA-Z]+)\1/g, '$1')

    // Fix duplicated expressions like "x÷10x÷10" to "x÷10"
    text = text.replace(/([a-zA-Z]+÷[0-9]+)\1/g, '$1')

    // Fix duplicated expressions like "ss" to "s" when it's a return value
    text = text.replace(/\\Return\s+([a-zA-Z]+)\1/g, '\\Return $1')

    return text
  }

  private addAlgorithmIndentation(content: string): string {
    const lines = content.split('\n')
    let indentLevel = 0
    const processedLines = []

    for (const line of lines) {
      const trimmedLine = line.trim()

      // Decrease indent for end statements
      if (trimmedLine.includes('algo-end') ||
          trimmedLine.includes('algo-endif') ||
          trimmedLine.includes('algo-endwhile') ||
          trimmedLine.includes('algo-endfor') ||
          trimmedLine.includes('algo-until') ||
          trimmedLine.includes('algo-else')) {
        indentLevel = Math.max(0, indentLevel - 1)
      }

      if (trimmedLine) {
        const indentedLine = `<div style="margin-left: ${indentLevel * 20}px;">${trimmedLine}</div>`
        processedLines.push(indentedLine)
      }

      // Increase indent for control structures
      if (trimmedLine.includes('algo-function') ||
          trimmedLine.includes('algo-procedure') ||
          trimmedLine.includes('algo-if') ||
          trimmedLine.includes('algo-elsif') ||
          trimmedLine.includes('algo-else') ||
          trimmedLine.includes('algo-while') ||
          trimmedLine.includes('algo-for') ||
          trimmedLine.includes('algo-repeat')) {
        indentLevel++
      }
    }

    return processedLines.join('')
  }

  private processTcolorbox(content: string): string {
    // Extract tcolorbox options
    const optionsMatch = content.match(/^\[([^\]]+)\]/)
    let options = {}

    if (optionsMatch) {
      const optionString = optionsMatch[1]
      // Parse options like colback=white, colframe=sectioncolor, width=0.8\textwidth
      const optionPairs = optionString.split(',')
      for (const pair of optionPairs) {
        const [key, value] = pair.split('=').map(s => s.trim())
        if (key && value) {
          options[key] = value
        }
      }
      // Remove options from content
      content = content.replace(/^\[[^\]]+\]/, '').trim()
    }

    // Generate CSS styles from options
    let styles = ''
    if (options['colback']) {
      styles += `background-color: var(--${options['colback']}, ${options['colback']});`
    }
    if (options['colframe']) {
      styles += `border-color: var(--${options['colframe']}, ${options['colframe']});`
    }
    if (options['width']) {
      const width = options['width'].replace('\\textwidth', '%').replace('0.8', '80')
      styles += `width: ${width}; margin: 0 auto;`
    }
    if (options['boxrule']) {
      styles += `border-width: ${options['boxrule']};`
    }
    if (options['arc'] === '0mm') {
      styles += `border-radius: 0;`
    }

    return `
      <div class="tcolorbox" style="${styles}">
        ${this.processLatexContent(content)}
      </div>
    `
  }

  private processList(content: string, listType: string): string {
    const tag = listType === 'enumerate' ? 'ol' : 'ul'
    const items = content.split('\\item').filter(item => item.trim())
    const processedItems = items.map(item =>
      `<li>${this.processLatexContent(item.trim())}</li>`
    ).join('')

    return `<${tag} class="latex-list">${processedItems}</${tag}>`
  }

  private processTable(content: string): string {
    // Handle table column specifications
    const lines = content.split('\n').filter(line => line.trim())
    let tableContent = lines.join(' ')

    // Remove hline commands and convert to CSS
    tableContent = tableContent.replace(/\\hline/g, '')

    // Handle rowcolor commands
    tableContent = tableContent.replace(/\\rowcolor\{[^}]*\}/g, '')

    // Split into rows
    const rows = tableContent.split('\\\\').filter(row => row.trim())

    let processedRows = ''
    let isHeaderRow = true

    for (const row of rows) {
      const trimmedRow = row.trim()
      if (!trimmedRow) continue

      // Handle spacing commands like [6pt]
      const cleanRow = trimmedRow.replace(/\[[^\]]*\]/g, '')

      // Split into cells
      const cells = cleanRow.split('&').map(cell => {
        const processedCell = this.processLatexContent(cell.trim())
        const tag = isHeaderRow ? 'th' : 'td'
        return `<${tag}>${processedCell}</${tag}>`
      }).join('')

      const rowClass = isHeaderRow ? 'header-row' : ''
      processedRows += `<tr class="${rowClass}">${cells}</tr>`
      isHeaderRow = false
    }

    return `<table class="latex-table">${processedRows}</table>`
  }

  private processFigure(content: string): string {
    const captionMatch = content.match(/\\caption\{([^}]+)\}/)
    const caption = captionMatch ? captionMatch[1] : ''

    return `
      <figure class="latex-figure">
        <div class="figure-placeholder">[Figure]</div>
        ${caption ? `<figcaption>${this.processLatexContent(caption)}</figcaption>` : ''}
      </figure>
    `
  }

  private generateHTML(content: {
    documentClass?: string
    packages: Array<{ name: string; options?: string }>
    customCommands: Array<{ name: string; definition: string }>
    colors: Map<string, string>
    geometry?: string
    title?: string
    author?: string
    date?: string
    abstract?: string
    sections: Array<{ title: string; content: string; level: number; id: string }>
    bibliography?: Array<{ key: string; entry: string }>
    customStyles: Map<string, any>
  }): string {
    // Generate dynamic styles based on parsed content
    const customColors = this.generateColorCSS(content.colors)
    const geometryStyles = this.generateGeometryCSS(content.geometry)

    const styles = `
      <style>
        /* Base document styles */
        .latex-document {
          max-width: ${content.documentClass === 'article' ? '800px' : '900px'};
          margin: 0 auto;
          padding: ${geometryStyles.padding || '40px'};
          font-family: 'Computer Modern', 'Latin Modern Roman', 'Times New Roman', serif;
          line-height: 1.6;
          color: #333;
          background: white;
          font-size: ${content.documentClass?.includes('11pt') ? '11pt' : '10pt'};
        }

        /* Custom colors */
        ${customColors}

        /* Document header */
        .document-header {
          text-align: center;
          margin-bottom: 40px;
          border-bottom: 2px solid var(--section-color, #e0e0e0);
          padding-bottom: 30px;
        }

        .document-title {
          font-size: 2.2em;
          font-weight: bold;
          margin-bottom: 20px;
          color: var(--title-color, #2c3e50);
          line-height: 1.2;
        }

        .document-author {
          font-size: 1.2em;
          color: #555;
          margin-bottom: 10px;
          font-style: italic;
        }

        .document-date {
          font-size: 1em;
          color: #777;
          margin-bottom: 20px;
        }

        /* Abstract */
        .abstract {
          background: var(--abstract-bg, #f8f9fa);
          border-left: 4px solid var(--abstract-border, #007bff);
          padding: 20px;
          margin: 30px 0;
          border-radius: 4px;
        }

        .abstract-title {
          font-weight: bold;
          font-size: 1.1em;
          margin-bottom: 10px;
          color: var(--abstract-border, #007bff);
        }

        /* Sections */
        .section {
          margin: 30px 0;
        }

        .section-title-0 {
          font-size: 2.2em;
          font-weight: bold;
          color: var(--section-color, #2c3e50);
          margin: 50px 0 30px 0;
          border-bottom: 2px solid var(--section-color, #ddd);
          padding-bottom: 15px;
          text-align: center;
        }

        .section-title-1 {
          font-size: 1.8em;
          font-weight: bold;
          color: var(--section-color, #2c3e50);
          margin: 40px 0 20px 0;
          border-bottom: 1px solid var(--section-color, #ddd);
          padding-bottom: 10px;
        }

        .section-title-2 {
          font-size: 1.4em;
          font-weight: bold;
          color: var(--subsection-color, #34495e);
          margin: 30px 0 15px 0;
        }

        .section-title-3 {
          font-size: 1.2em;
          font-weight: bold;
          color: var(--subsubsection-color, #5d6d7e);
          margin: 25px 0 10px 0;
        }

        .section-title-4 {
          font-size: 1.1em;
          font-weight: bold;
          color: #6c757d;
          margin: 20px 0 10px 0;
        }

        .section-content {
          font-size: 1em;
          line-height: 1.7;
          text-align: justify;
          margin-bottom: 20px;
        }

        /* Font size classes */
        .large { font-size: 1.2em; }
        .Large { font-size: 1.44em; }
        .LARGE { font-size: 1.728em; }
        .huge { font-size: 2.074em; }
        .Huge { font-size: 2.488em; }
        .small { font-size: 0.833em; }
        .footnotesize { font-size: 0.694em; }
        .scriptsize { font-size: 0.579em; }
        .tiny { font-size: 0.482em; }

        /* Spacing */
        .vspace { margin: 10px 0; }
        .hspace { margin: 0 10px; }
        .center { text-align: center; }

        /* Algorithm boxes */
        .algorithm-box {
          background: var(--algorithm-bg, #f0f8ff);
          border: 1px solid var(--algorithm-border, #007bff);
          border-radius: 8px;
          margin: 20px 0;
          overflow: hidden;
          font-family: 'Courier New', monospace;
        }

        .algorithm-header {
          background: var(--algorithm-border, #007bff);
          color: white;
          padding: 10px 15px;
          font-weight: bold;
          font-size: 0.9em;
        }

        .algorithm-content {
          padding: 15px;
          font-size: 0.9em;
          line-height: 1.6;
        }

        /* Algorithmic elements */
        .algo-function, .algo-procedure {
          font-weight: bold;
          color: #0066cc;
          margin: 8px 0;
        }

        .algo-if, .algo-elsif, .algo-else, .algo-while, .algo-for, .algo-repeat {
          font-weight: bold;
          color: #cc6600;
          margin: 5px 0;
        }

        .algo-end, .algo-endif, .algo-endwhile, .algo-endfor, .algo-until {
          font-weight: bold;
          color: #cc6600;
          margin: 5px 0;
        }

        .algo-state {
          margin: 3px 0;
          color: #333;
        }

        .algo-return {
          font-weight: bold;
          color: #009900;
          margin: 5px 0;
        }

        .algo-comment {
          color: #666;
          font-style: italic;
        }

        /* Standalone algorithmic environment */
        .algorithmic-box {
          background: var(--algorithm-bg, #f8f9fa);
          border: 1px solid var(--algorithm-border, #dee2e6);
          border-radius: 6px;
          padding: 15px;
          margin: 20px 0;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
          line-height: 1.6;
        }

        /* Tcolorbox */
        .tcolorbox {
          background: var(--box-bg, #f8f9fa);
          border: 1px solid var(--box-border, #dee2e6);
          border-radius: 6px;
          padding: 15px;
          margin: 20px 0;
        }

        /* Lists */
        .latex-list {
          margin: 15px 0;
          padding-left: 30px;
        }

        .latex-list li {
          margin: 8px 0;
          line-height: 1.6;
        }

        /* Tables */
        .latex-table {
          border-collapse: collapse;
          margin: 20px auto;
          font-size: 0.95em;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .latex-table td, .latex-table th {
          padding: 8px 12px;
          border: 1px solid #ddd;
          text-align: left;
          vertical-align: top;
        }

        .latex-table th {
          background-color: #f5f5f5;
          font-weight: bold;
          text-align: center;
        }

        .latex-table .header-row {
          background-color: #e9ecef;
        }

        .latex-table tr:nth-child(even) {
          background-color: #f9f9f9;
        }

        .latex-table tr:hover {
          background-color: #f0f0f0;
        }

        /* Figures */
        .latex-figure {
          text-align: center;
          margin: 25px 0;
        }

        .figure-placeholder {
          background: #f0f0f0;
          border: 2px dashed #ccc;
          padding: 40px;
          margin: 10px 0;
          color: #666;
          font-style: italic;
        }

        .latex-figure figcaption {
          margin-top: 10px;
          font-size: 0.9em;
          color: #666;
          font-style: italic;
        }

        /* Citations and references */
        .citation {
          color: #007bff;
          text-decoration: none;
          font-size: 0.8em;
        }

        .reference {
          color: #007bff;
          text-decoration: none;
        }

        .reference:hover {
          text-decoration: underline;
        }

        .footnote {
          color: #007bff;
          font-size: 0.8em;
        }

        /* Math error handling */
        .math-error {
          background: #ffe6e6;
          color: #d63031;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: monospace;
          font-size: 0.9em;
        }

        /* KaTeX styles integration */
        .katex {
          font-size: 1.1em;
        }

        .katex-display {
          margin: 20px 0;
          text-align: center;
        }

        /* Text formatting */
        strong { font-weight: bold; }
        em { font-style: italic; }
        code {
          font-family: 'Courier New', monospace;
          background: #f5f5f5;
          padding: 2px 4px;
          border-radius: 3px;
          font-size: 0.9em;
        }
        u { text-decoration: underline; }

        /* Framed boxes */
        .fbox {
          border: 1px solid #333;
          padding: 4px 8px;
          display: inline-block;
          margin: 2px;
          background: #f9f9f9;
        }

        /* Print styles */
        @media print {
          .latex-document {
            max-width: none;
            margin: 0;
            padding: 20px;
          }

          .algorithm-box, .tcolorbox {
            break-inside: avoid;
          }
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .latex-document {
            padding: 20px;
            font-size: 14px;
          }

          .document-title {
            font-size: 1.8em;
          }

          .section-title-1 {
            font-size: 1.5em;
          }
        }
      </style>
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css">
    `

    let html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${content.title || 'LaTeX Document'}</title>
        ${styles}
      </head>
      <body>
        <div class="latex-document">
    `

    // Document header
    if (content.title || content.author || content.date) {
      html += '<div class="document-header">'

      if (content.title) {
        html += `<h1 class="document-title">${this.escapeHtml(content.title)}</h1>`
      }

      if (content.author) {
        html += `<div class="document-author">${this.escapeHtml(content.author)}</div>`
      }

      if (content.date) {
        html += `<div class="document-date">${this.escapeHtml(content.date)}</div>`
      }

      html += '</div>'
    }

    // Abstract
    if (content.abstract) {
      html += `
        <div class="abstract">
          <div class="abstract-title">Abstract</div>
          <div>${content.abstract.trim()}</div>
        </div>
      `
    }

    // Sections
    for (const section of content.sections) {
      html += '<div class="section">'
      html += `<h${section.level + 1} class="section-title-${section.level}">${this.escapeHtml(section.title)}</h${section.level + 1}>`
      html += `<div class="section-content">${section.content.trim()}</div>`
      html += '</div>'
    }

    html += `
        </div>
      </body>
      </html>
    `

    return html
  }

  private generateColorCSS(colors: Map<string, string>): string {
    let css = ':root {\n'

    // Default color mappings
    css += '  --title-color: #2c3e50;\n'
    css += '  --section-color: #2c3e50;\n'
    css += '  --subsection-color: #34495e;\n'
    css += '  --subsubsection-color: #5d6d7e;\n'
    css += '  --abstract-bg: #f8f9fa;\n'
    css += '  --abstract-border: #007bff;\n'
    css += '  --algorithm-bg: #f0f8ff;\n'
    css += '  --algorithm-border: #007bff;\n'
    css += '  --box-bg: #f8f9fa;\n'
    css += '  --box-border: #dee2e6;\n'

    // Override with custom colors
    for (const [name, value] of colors) {
      css += `  --${name}: ${value};\n`

      // Map common LaTeX color names to CSS variables
      switch (name) {
        case 'titlecolor':
          css += `  --title-color: ${value};\n`
          break
        case 'sectioncolor':
          css += `  --section-color: ${value};\n`
          css += `  --subsection-color: ${value};\n`
          break
        case 'algorithmbackground':
          css += `  --algorithm-bg: ${value};\n`
          break
        case 'examplebackground':
          css += `  --box-bg: ${value};\n`
          break
      }
    }

    css += '}\n'
    return css
  }

  private generateGeometryCSS(geometry?: string): { padding?: string; margin?: string } {
    if (!geometry) return {}

    const styles: { padding?: string; margin?: string } = {}

    // Parse geometry settings
    const marginMatch = geometry.match(/margin=([^,]+)/)
    if (marginMatch) {
      styles.padding = marginMatch[1]
    }

    const topMatch = geometry.match(/top=([^,]+)/)
    if (topMatch) {
      // Could adjust top padding based on this
    }

    return styles
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  private validateLatexCode(latexCode: string): string[] {
    const errors: string[] = []

    // Basic LaTeX validation
    if (!latexCode.includes("\\documentclass")) {
      errors.push("Missing \\documentclass declaration")
    }

    if (!latexCode.includes("\\begin{document}")) {
      errors.push("Missing \\begin{document}")
    }

    if (!latexCode.includes("\\end{document}")) {
      errors.push("Missing \\end{document}")
    }

    // Check for unmatched braces
    const openBraces = (latexCode.match(/\{/g) || []).length
    const closeBraces = (latexCode.match(/\}/g) || []).length
    if (openBraces !== closeBraces) {
      errors.push("Unmatched braces in LaTeX code")
    }

    return errors
  }

  async downloadPdf(documentId: string): Promise<Blob> {
    try {
      // Use the real API client to download PDF
      return await apiClient.latex.downloadPdf(documentId)
    } catch (error) {
      console.error("Failed to download PDF:", error)
      throw new Error("Failed to download PDF: " + (error instanceof Error ? error.message : "Unknown error"))
    }
  }

  generateShareableLink(documentId: string, permissions: "view" | "comment" | "edit" = "view"): string {
    // Generate a shareable link for the document
    const baseUrl = window.location.origin
    return `${baseUrl}/shared/${documentId}?permissions=${permissions}`
  }
}

export const latexService = LatexService.getInstance()
