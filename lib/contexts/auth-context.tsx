"use client"

import React, { create<PERSON>ontext, useContext, useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { apiClient } from "@/lib/api/client"
import type { User } from "@/lib/types/database"

interface AuthContextType {
  user: User | null
  loading: boolean
  error: string | null
  isAuthenticated: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (userData: {
    email: string
    password: string
    first_name: string
    last_name: string
  }) => Promise<void>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const pathname = usePathname()

  // Public routes that don't require authentication
  const publicRoutes = ["/", "/auth/signin", "/auth/signup", "/forgot-password"]
  const isPublicRoute = publicRoutes.includes(pathname)

  useEffect(() => {
    // Check if user is already authenticated on app load
    checkAuthStatus()
  }, [])

  useEffect(() => {
    // Handle route protection
    if (!loading) {
      if (!user && !isPublicRoute) {
        // User is not authenticated and trying to access protected route
        router.push("/auth/signin")
      } else if (user && (pathname === "/auth/signin" || pathname === "/auth/signup")) {
        // User is authenticated but on auth pages, redirect to dashboard
        router.push("/dashboard")
      }
    }
  }, [user, loading, pathname, isPublicRoute, router])

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem("auth_token")
      if (token) {
        // Add token to API client headers
        apiClient.setAuthToken(token)
        const userData = await apiClient.auth.me()
        setUser(userData)
      }
    } catch (err) {
      // Token might be expired or invalid
      localStorage.removeItem("auth_token")
      apiClient.clearAuthToken()
      console.error("Auth check failed:", err)
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.auth.signIn(email, password)

      // Store token and set it in API client
      localStorage.setItem("auth_token", response.token)
      apiClient.setAuthToken(response.token)
      setUser(response.user)

      // Redirect to dashboard
      router.push("/dashboard")
    } catch (err) {
      setError(err instanceof Error ? err.message : "Sign in failed")
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (userData: {
    email: string
    password: string
    first_name: string
    last_name: string
  }) => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.auth.signUp(userData)

      // Store token and set it in API client
      localStorage.setItem("auth_token", response.token)
      apiClient.setAuthToken(response.token)
      setUser(response.user)

      // Redirect to dashboard
      router.push("/dashboard")
    } catch (err) {
      setError(err instanceof Error ? err.message : "Sign up failed")
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      await apiClient.auth.signOut()
    } catch (err) {
      console.error("Sign out error:", err)
    } finally {
      // Clear local state regardless of API call success
      localStorage.removeItem("auth_token")
      apiClient.clearAuthToken()
      setUser(null)
      setError(null)

      // Redirect to signin page
      router.push("/auth/signin")
    }
  }

  const refreshUser = async () => {
    try {
      const userData = await apiClient.auth.me()
      setUser(userData)
    } catch (err) {
      console.error("Failed to refresh user:", err)
      // If refresh fails, user might need to sign in again
      await signOut()
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    signIn,
    signUp,
    signOut,
    refreshUser,
  }

  // Show loading screen while checking authentication on initial load
  if (loading) {
    return (
      <AuthContext.Provider value={value}>
        <div className="min-h-screen bg-gray-950 flex items-center justify-center">
          <div className="text-center">
            <div className="mb-8">
              <img src="/deepdocx_full.png" alt="DeepDocX" className="w-40 h-auto mx-auto" />
            </div>
            <div className="w-8 h-8 animate-spin border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-400">Loading...</p>
          </div>
        </div>
      </AuthContext.Provider>
    )
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
