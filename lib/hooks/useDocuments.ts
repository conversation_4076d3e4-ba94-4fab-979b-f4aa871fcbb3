"use client"

import { useState, useEffect } from "react"
import { apiClient } from "@/lib/api/client"
import type { Document, DocumentVersion } from "@/lib/types/database"

export function useDocuments() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadDocuments()
  }, [])

  const loadDocuments = async () => {
    try {
      setLoading(true)
      const response = await apiClient.documents.list()
      setDocuments(response.documents || response) // Handle different response formats
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load documents")
      console.error("Failed to load documents:", err)
    } finally {
      setLoading(false)
    }
  }

  const createDocument = async (data: Partial<Document>) => {
    try {
      const response = await apiClient.documents.create(data)
      const newDoc = response.document || response // Handle different response formats
      setDocuments((prev) => [newDoc, ...prev])
      return newDoc
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create document")
      console.error("Failed to create document:", err)
      throw err
    }
  }

  const updateDocument = async (id: string, data: Partial<Document>) => {
    try {
      const response = await apiClient.documents.update(id, data)
      const updatedDoc = response.document || response // Handle different response formats
      setDocuments((prev) => prev.map((doc) => (doc.id === id ? updatedDoc : doc)))
      return updatedDoc
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update document")
      console.error("Failed to update document:", err)
      throw err
    }
  }

  const deleteDocument = async (id: string) => {
    try {
      await apiClient.documents.delete(id)
      setDocuments((prev) => prev.filter((doc) => doc.id !== id))
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete document")
      console.error("Failed to delete document:", err)
      throw err
    }
  }

  return {
    documents,
    loading,
    error,
    loadDocuments,
    createDocument,
    updateDocument,
    deleteDocument,
  }
}

export function useDocumentVersions(documentId: string) {
  const [versions, setVersions] = useState<DocumentVersion[]>([])
  const [currentVersion, setCurrentVersion] = useState<DocumentVersion | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (documentId) {
      loadVersions()
      loadCurrentVersion()
    }
  }, [documentId])

  const loadVersions = async () => {
    try {
      const response = await apiClient.documents.getVersions(documentId)
      const versionList = response.versions || response // Handle different response formats
      setVersions(versionList)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load versions")
      console.error("Failed to load versions:", err)
    }
  }

  const loadCurrentVersion = async () => {
    try {
      setLoading(true)
      const response = await apiClient.documents.getCurrentVersion(documentId)
      const current = response.version || response // Handle different response formats
      setCurrentVersion(current)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load current version")
      console.error("Failed to load current version:", err)
    } finally {
      setLoading(false)
    }
  }

  const createVersion = async (latexCode: string, description?: string) => {
    try {
      const response = await apiClient.documents.createVersion(documentId, {
        latex_code: latexCode,
        description,
      })
      const newVersion = response.version || response // Handle different response formats
      setVersions((prev) => [newVersion, ...prev])
      setCurrentVersion(newVersion)
      return newVersion
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create version")
      console.error("Failed to create version:", err)
      throw err
    }
  }

  const switchToVersion = async (versionId: string) => {
    try {
      const response = await apiClient.documents.setCurrentVersion(documentId, versionId)
      const version = response.version || response // Handle different response formats
      setCurrentVersion(version)
      await loadVersions() // Refresh to update is_current flags
      return version
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to switch version")
      console.error("Failed to switch version:", err)
      throw err
    }
  }

  return {
    versions,
    currentVersion,
    loading,
    error,
    createVersion,
    switchToVersion,
    loadVersions,
    loadCurrentVersion,
  }
}
