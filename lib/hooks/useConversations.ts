"use client"

import { useState, useEffect } from "react"
import { apiClient } from "@/lib/api/client"
import type { Conversation, Message } from "@/lib/types/database"

export function useConversations(documentId?: string) {
  const [conversation, setConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (documentId) {
      loadConversation(documentId)
    }
  }, [documentId])

  const loadConversation = async (docId: string) => {
    try {
      setLoading(true)
      const response = await apiClient.conversations.get(docId)
      const conv = response.conversation || response // Handle different response formats
      setConversation(conv)
      
      if (conv?.id) {
        await loadMessages(conv.id)
      }
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load conversation")
      console.error("Failed to load conversation:", err)
    } finally {
      setLoading(false)
    }
  }

  const loadMessages = async (conversationId: string) => {
    try {
      const response = await apiClient.conversations.getMessages(conversationId)
      const messageList = response.messages || response // Handle different response formats
      setMessages(messageList)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load messages")
      console.error("Failed to load messages:", err)
    }
  }

  const sendMessage = async (content: string) => {
    if (!conversation?.id) {
      throw new Error("No active conversation")
    }

    try {
      setSending(true)
      const response = await apiClient.conversations.sendMessage(conversation.id, content)
      const newMessage = response.message || response // Handle different response formats
      
      // Add user message to the list
      setMessages((prev) => [...prev, newMessage])
      
      // In a real implementation, you might want to poll for AI response
      // or use WebSockets for real-time updates
      
      return newMessage
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send message")
      console.error("Failed to send message:", err)
      throw err
    } finally {
      setSending(false)
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  return {
    conversation,
    messages,
    loading,
    sending,
    error,
    loadConversation,
    loadMessages,
    sendMessage,
    clearMessages,
  }
}
