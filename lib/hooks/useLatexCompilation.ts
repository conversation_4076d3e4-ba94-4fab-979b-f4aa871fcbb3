"use client"

import { useState } from "react"
import { apiClient } from "@/lib/api/client"

export function useLatexCompilation() {
  const [isCompiling, setIsCompiling] = useState(false)
  const [compilationResult, setCompilationResult] = useState<{
    success: boolean
    pdf_url?: string
    errors?: string[]
  } | null>(null)

  const compileLatex = async (documentId: string, latexCode: string) => {
    setIsCompiling(true)
    setCompilationResult(null)

    try {
      // Use server-side LaTeX compilation for professional PDF generation
      const result = await apiClient.latex.compile(documentId, latexCode)
      setCompilationResult(result)
      return result
    } catch (error) {
      const errorResult = {
        success: false,
        errors: ["Compilation failed: " + (error instanceof Error ? error.message : "Unknown error")],
      }
      setCompilationResult(errorResult)
      return errorResult
    } finally {
      setIsCompiling(false)
    }
  }

  const downloadPdf = async (documentId: string) => {
    try {
      const blob = await latexService.downloadPdf(documentId)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = `document-${documentId}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      throw new Error("Failed to download PDF: " + (error instanceof Error ? error.message : "Unknown error"))
    }
  }

  return {
    isCompiling,
    compilationResult,
    compileLatex,
    downloadPdf,
  }
}
