"use client"

import { useState, useEffect } from "react"
import { apiClient } from "@/lib/api/client"
import type { File as DatabaseFile } from "@/lib/types/database"

export function useFiles() {
  const [files, setFiles] = useState<DatabaseFile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    loadFiles()
  }, [])

  const loadFiles = async (folderId?: string) => {
    try {
      setLoading(true)
      const response = await apiClient.files.list(folderId)
      const fileList = response.files || response // Handle different response formats
      setFiles(fileList)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load files")
      console.error("Failed to load files:", err)
    } finally {
      setLoading(false)
    }
  }

  const uploadFile = async (file: File, folderId?: string) => {
    try {
      setUploading(true)
      const response = await apiClient.files.upload(file, folderId)
      const uploadedFile = response.file || response // Handle different response formats
      setFiles((prev) => [uploadedFile, ...prev])
      return uploadedFile
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to upload file")
      console.error("Failed to upload file:", err)
      throw err
    } finally {
      setUploading(false)
    }
  }

  const deleteFile = async (id: string) => {
    try {
      await apiClient.files.delete(id)
      setFiles((prev) => prev.filter((file) => file.id !== id))
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete file")
      console.error("Failed to delete file:", err)
      throw err
    }
  }

  const downloadFile = async (id: string, filename: string) => {
    try {
      const response = await fetch(`${apiClient.baseUrl}/files/${id}/download`)
      if (!response.ok) {
        throw new Error("Failed to download file")
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to download file")
      console.error("Failed to download file:", err)
      throw err
    }
  }

  return {
    files,
    loading,
    error,
    uploading,
    loadFiles,
    uploadFile,
    deleteFile,
    downloadFile,
  }
}
