"use client"

import { useState, useEffect } from "react"
import { apiClient } from "@/lib/api/client"
import type { Folder } from "@/lib/types/database"

export function useFolders() {
  const [folders, setFolders] = useState<Folder[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadFolders()
  }, [])

  const loadFolders = async (parentId?: string) => {
    try {
      setLoading(true)
      const response = await apiClient.folders.list(parentId)
      const folderList = response.folders || response // Handle different response formats
      setFolders(folderList)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load folders")
      console.error("Failed to load folders:", err)
    } finally {
      setLoading(false)
    }
  }

  const createFolder = async (name: string, parentFolderId?: string) => {
    try {
      const response = await apiClient.folders.create({
        name,
        parent_folder_id: parentFolderId,
      })
      const newFolder = response.folder || response // Handle different response formats
      setFolders((prev) => [newFolder, ...prev])
      return newFolder
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create folder")
      console.error("Failed to create folder:", err)
      throw err
    }
  }

  const updateFolder = async (id: string, data: Partial<Folder>) => {
    try {
      const response = await apiClient.folders.update(id, data)
      const updatedFolder = response.folder || response // Handle different response formats
      setFolders((prev) => prev.map((folder) => (folder.id === id ? updatedFolder : folder)))
      return updatedFolder
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update folder")
      console.error("Failed to update folder:", err)
      throw err
    }
  }

  const deleteFolder = async (id: string) => {
    try {
      await apiClient.folders.delete(id)
      setFolders((prev) => prev.filter((folder) => folder.id !== id))
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete folder")
      console.error("Failed to delete folder:", err)
      throw err
    }
  }

  return {
    folders,
    loading,
    error,
    loadFolders,
    createFolder,
    updateFolder,
    deleteFolder,
  }
}
