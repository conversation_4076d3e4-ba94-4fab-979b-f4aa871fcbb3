"use client"

import { useState } from "react"
import { sharingService } from "@/lib/services/sharing"
import type { SharedDocument } from "@/lib/types/database"

export function useSharing() {
  const [isSharing, setIsSharing] = useState(false)
  const [shares, setShares] = useState<SharedDocument[]>([])

  const shareDocument = async (
    documentId: string,
    options: {
      email?: string
      permissions: "view" | "comment" | "edit"
      expiresIn?: number
    },
  ) => {
    setIsSharing(true)
    try {
      const result = await sharingService.shareDocument(documentId, options)

      // Copy to clipboard
      await sharingService.copyToClipboard(result.shareUrl)

      // Refresh shares list
      await loadShares(documentId)

      return result
    } finally {
      setIsSharing(false)
    }
  }

  const loadShares = async (documentId: string) => {
    try {
      const documentShares = await sharingService.getDocumentShares(documentId)
      setShares(documentShares)
    } catch (error) {
      console.error("Failed to load shares:", error)
    }
  }

  const revokeShare = async (shareId: string, documentId: string) => {
    try {
      await sharingService.revokeShare(shareId)
      await loadShares(documentId) // Refresh list
    } catch (error) {
      throw new Error("Failed to revoke share: " + (error instanceof Error ? error.message : "Unknown error"))
    }
  }

  return {
    isSharing,
    shares,
    shareDocument,
    loadShares,
    revokeShare,
  }
}
