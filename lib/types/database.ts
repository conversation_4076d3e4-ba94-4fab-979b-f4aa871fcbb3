export interface User {
  id: string
  email: string
  password_hash: string
  first_name: string
  last_name: string
  bio?: string
  company?: string
  role?: string
  avatar_url?: string
  plan_type: "free" | "pro" | "enterprise"
  settings: Record<string, any>
  created_at: string
  updated_at: string
}

export interface Folder {
  id: string
  user_id: string
  name: string
  parent_folder_id?: string
  created_at: string
  updated_at: string
}

export interface File {
  id: string
  user_id: string
  folder_id?: string
  name: string
  type: string
  size?: number
  url?: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface Document {
  id: string
  user_id: string
  title: string
  description?: string
  type: string
  status: "draft" | "completed" | "processing" | "error"
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface DocumentVersion {
  id: string
  document_id: string
  version_number: number
  latex_code: string
  compiled_pdf_url?: string
  is_current: boolean
  description?: string
  created_at: string
}

export interface Conversation {
  id: string
  document_id: string
  user_id: string
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  conversation_id: string
  role: "user" | "assistant" | "system"
  content: string
  metadata: Record<string, any>
  created_at: string
}

export interface SharedDocument {
  id: string
  document_id: string
  shared_by: string
  shared_with?: string
  share_token?: string
  permissions: "view" | "comment" | "edit"
  expires_at?: string
  created_at: string
}
