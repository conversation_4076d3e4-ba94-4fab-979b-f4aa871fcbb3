// API Client for making requests to backend
import type { User, Document, DocumentVersion, Folder, File as DatabaseFile, Conversation, Message, SharedDocument } from "@/lib/types/database"

class ApiClient {
  private baseUrl: string
  private authToken: string | null = null

  constructor(baseUrl = process.env.NEXT_PUBLIC_API_URL || "https://api.lightray-technologies.com/api") {
    this.baseUrl = baseUrl
  }

  setAuthToken(token: string) {
    this.authToken = token
  }

  clearAuthToken() {
    this.authToken = null
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    }

    // Add auth token if available
    const token = this.authToken || localStorage.getItem("auth_token")
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      }
    }

    const response = await fetch(url, config)

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  // Auth endpoints
  auth = {
    signIn: (email: string, password: string) =>
      this.request<{ user: User; token: string }>("/auth/signin", {
        method: "POST",
        body: JSON.stringify({ email, password }),
      }),

    signUp: (userData: Partial<User> & { password: string }) =>
      this.request<{ user: User; token: string }>("/auth/signup", {
        method: "POST",
        body: JSON.stringify(userData),
      }),

    signOut: () => this.request("/auth/signout", { method: "POST" }),

    me: () => this.request<User>("/auth/me"),
  }

  // Users endpoints
  users = {
    getProfile: () => this.request<User>("/users/profile"),

    updateProfile: (data: Partial<User>) =>
      this.request<User>("/users/profile", {
        method: "PATCH",
        body: JSON.stringify(data),
      }),

    getSettings: () => this.request<Record<string, any>>("/users/settings"),

    updateSettings: (data: Record<string, any>) =>
      this.request<Record<string, any>>("/users/settings", {
        method: "PATCH",
        body: JSON.stringify(data),
      }),

    changePassword: (currentPassword: string, newPassword: string) =>
      this.request("/users/change-password", {
        method: "POST",
        body: JSON.stringify({ current_password: currentPassword, new_password: newPassword }),
      }),
  }

  // Documents endpoints
  documents = {
    list: (folderId?: string) =>
      this.request<Document[]>(`/documents${folderId ? `?folder_id=${folderId}` : ""}`),

    get: (id: string) => this.request<{
      document: Document;
      current_version: DocumentVersion | null;
      conversation: Conversation | null;
    }>(`/documents/${id}`),

    create: (data: { title?: string; description?: string; folder_id?: string; type?: string }) =>
      this.request<{ document: Document; conversation: Conversation }>("/documents", {
        method: "POST",
        body: JSON.stringify(data),
      }),

    // Create document with first message - AI generates name and LaTeX
    createWithMessage: (data: {
      message: string;
      folder_id?: string;
      type?: string;
      referenced_files?: string[]
    }) =>
      this.request<{
        document: Document;
        conversation: Conversation;
        first_version: DocumentVersion | null;
        user_message: Message;
        ai_message: Message;
        latex_code: string | null;
      }>("/documents/create-with-message", {
        method: "POST",
        body: JSON.stringify(data),
      }),

    update: (id: string, data: Partial<Document>) =>
      this.request<Document>(`/documents/${id}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      }),

    delete: (id: string) => this.request(`/documents/${id}`, { method: "DELETE" }),

    getVersions: (id: string) => this.request<DocumentVersion[]>(`/documents/${id}/versions`),

    createVersion: (id: string, data: { latex_code: string; description?: string }) =>
      this.request<DocumentVersion>(`/documents/${id}/versions`, {
        method: "POST",
        body: JSON.stringify(data),
      }),

    setCurrentVersion: (versionId: string) =>
      this.request<DocumentVersion>(`/documents/versions/${versionId}/set-current`, {
        method: "POST",
      }),

    // AI Chat endpoint for document creation and editing
    chat: (id: string, message: string, referencedFiles?: string[]) =>
      this.request<{
        user_message: Message;
        ai_message: Message;
        new_version: DocumentVersion | null;
        updated_document: {
          document: Document;
          current_version: DocumentVersion | null;
          conversation: Conversation | null;
        };
      }>(`/documents/${id}/chat`, {
        method: "POST",
        body: JSON.stringify({
          message,
          referenced_files: referencedFiles || []
        }),
      }),
  }

  // Folders endpoints
  folders = {
    list: (parentId?: string) =>
      this.request<Folder[]>(`/folders${parentId ? `?parent_id=${parentId}` : ""}`),

    create: (data: { name: string; parent_folder_id?: string }) =>
      this.request<Folder>("/folders", {
        method: "POST",
        body: JSON.stringify(data),
      }),

    update: (id: string, data: Partial<Folder>) =>
      this.request<Folder>(`/folders/${id}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      }),

    delete: (id: string) => this.request(`/folders/${id}`, { method: "DELETE" }),

    getContents: (id?: string) =>
      this.request<{
        folder: Folder | null;
        subfolders: Folder[];
        files: DatabaseFile[];
        documents: Document[];
      }>(`/folders/${id ? `${id}/contents` : 'root/contents'}`),

    getPath: (id: string) =>
      this.request<Folder[]>(`/folders/${id}/path`),

    getPath: (id: string) => this.request<Folder[]>(`/folders/${id}/path`),

    move: (id: string, newParentId?: string) =>
      this.request<Folder>(`/folders/${id}/move`, {
        method: "POST",
        body: JSON.stringify({ new_parent_id: newParentId }),
      }),
  }

  // Files endpoints
  files = {
    list: (folderId?: string) =>
      this.request<DatabaseFile[]>(`/files${folderId ? `?folder_id=${folderId}` : ""}`),

    get: (id: string) => this.request<DatabaseFile>(`/files/${id}`),

    upload: (file: File, folderId?: string) => {
      const formData = new FormData()
      formData.append("file", file)
      if (folderId) formData.append("folder_id", folderId)

      return this.request<DatabaseFile>("/files/upload", {
        method: "POST",
        body: formData,
        headers: {}, // Remove Content-Type to let browser set it for FormData
      })
    },

    update: (id: string, data: Partial<DatabaseFile>) =>
      this.request<DatabaseFile>(`/files/${id}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      }),

    delete: (id: string) => this.request(`/files/${id}`, { method: "DELETE" }),

    getContent: (id: string) =>
      this.request<{ file_info: DatabaseFile; content: string }>(`/files/${id}/content`),

    search: (query: string, type?: string) =>
      this.request<DatabaseFile[]>(`/files/search?q=${encodeURIComponent(query)}${type ? `&type=${type}` : ""}`),
  }

  // Conversations endpoints
  conversations = {
    get: (conversationId: string) => this.request<Conversation>(`/conversations/${conversationId}`),

    getMessages: (conversationId: string, limit?: number) =>
      this.request<Message[]>(`/conversations/${conversationId}/messages${limit ? `?limit=${limit}` : ""}`),

    createMessage: (conversationId: string, content: string, role: string = "user", metadata?: Record<string, any>) =>
      this.request<Message>(`/conversations/${conversationId}/messages`, {
        method: "POST",
        body: JSON.stringify({ content, role, metadata }),
      }),

    update: (conversationId: string, data: Partial<Conversation>) =>
      this.request<Conversation>(`/conversations/${conversationId}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      }),

    delete: (conversationId: string) =>
      this.request(`/conversations/${conversationId}`, { method: "DELETE" }),
  }

  // Sharing endpoints
  share = {
    create: (
      documentId: string,
      data: {
        shared_with?: string
        permissions: "view" | "comment" | "edit"
        expires_at?: string
      },
    ) =>
      this.request<SharedDocument>("/share", {
        method: "POST",
        body: JSON.stringify({ document_id: documentId, ...data }),
      }),

    getByToken: (token: string) =>
      this.request<{
        document: Document;
        current_version: DocumentVersion | null;
        permissions: string;
        shared_by: string;
        expires_at?: string;
      }>(`/share/${token}`),

    list: (documentId: string) => this.request<SharedDocument[]>(`/share/document/${documentId}`),

    update: (shareId: string, data: { permissions?: string; expires_at?: string }) =>
      this.request<SharedDocument>(`/share/${shareId}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      }),

    revoke: (shareId: string) => this.request(`/share/${shareId}`, { method: "DELETE" }),
  }

  // LaTeX compilation endpoints
  latex = {
    compile: (documentId: string, latexCode: string) =>
      this.request<{
        success: boolean;
        pdf_url?: string;
        errors?: string[];
      }>("/latex/compile", {
        method: "POST",
        body: JSON.stringify({
          document_id: documentId,
          latex_code: latexCode,
        }),
      }),

    downloadPdf: (documentId: string) =>
      this.request<{
        download_url: string;
        filename: string;
      }>(`/latex/download/${documentId}`),

    compileVersion: (versionId: string) =>
      this.request<{
        success: boolean;
        pdf_url?: string;
        errors?: string[];
      }>(`/latex/compile-version/${versionId}`, {
        method: "POST",
      }),
  }
}

export const apiClient = new ApiClient()
