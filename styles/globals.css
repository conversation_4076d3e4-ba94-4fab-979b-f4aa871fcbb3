@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Dark Theme Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: #0a0a0a; /* Deep Black */
  color: #e4e4e7; /* Light Gray */
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Professional Dark Theme Color Palette */
:root {
  /* Primary Colors */
  --deep-black: #0a0a0a;
  --charcoal: #18181b;
  --slate: #27272a;
  --light-gray: #e4e4e7;
  --medium-gray: #a1a1aa;
  --dark-gray: #52525b;

  /* Accent Colors */
  --electric-blue: #3b82f6;
  --emerald: #10b981;
  --amber: #f59e0b;
  --rose: #f43f5e;
  --violet: #8b5cf6;

  /* Professional Gradients */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
  --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #f43f5e 100%);

  /* Subtle Textures */
  --noise-texture: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='27' cy='17' r='1'/%3E%3Ccircle cx='47' cy='27' r='1'/%3E%3Ccircle cx='17' cy='37' r='1'/%3E%3Ccircle cx='37' cy='47' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Professional animations */
@keyframes smooth-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(59, 130, 246, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
}

@keyframes subtle-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes slide-in {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Professional Dark Components */
.professional-bg {
  background: var(--deep-black);
  background-image: var(--noise-texture);
  position: relative;
}

.professional-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(139, 92, 246, 0.03) 100%);
  pointer-events: none;
}

.modern-border {
  position: relative;
  border: 1px solid var(--dark-gray);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.modern-border:hover {
  border-color: var(--electric-blue);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
}

.glass-effect {
  background: rgba(39, 39, 42, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.professional-button {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  font-size: 14px;
  letter-spacing: 0.025em;
}

.professional-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.4);
  background: var(--gradient-secondary);
}

.professional-button:active {
  transform: translateY(0px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.professional-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  border-radius: 8px;
  pointer-events: none;
}

.watercolor-wash {
  position: relative;
  overflow: hidden;
}

.watercolor-wash::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(ellipse at center, var(--soft-lavender) 0%, transparent 70%);
  opacity: 0.1;
  transform: rotate(-2deg);
  z-index: -1;
}

.sketch-tooltip {
  background: var(--cream-paper);
  border: 2px solid var(--warm-charcoal);
  border-radius: 12px;
  padding: 8px 12px;
  position: relative;
  transform: rotate(-1deg);
  box-shadow: 0 4px 12px rgba(45, 55, 72, 0.15);
}

.sketch-tooltip::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid var(--warm-charcoal);
  transform: rotate(5deg);
}

@layer base {
  :root {
    /* Living Paper Design System - Updated for organic feel */
    --background: 60 9% 98%; /* Cream Paper */
    --foreground: 210 22% 22%; /* Warm Charcoal */
    --card: 60 9% 98%;
    --card-foreground: 210 22% 22%;
    --popover: 60 9% 98%;
    --popover-foreground: 210 22% 22%;
    --primary: 142 23% 45%; /* Sage Green */
    --primary-foreground: 60 9% 98%;
    --secondary: 213 20% 58%; /* Dusty Blue */
    --secondary-foreground: 60 9% 98%;
    --muted: 60 5% 92%;
    --muted-foreground: 210 22% 22%;
    --accent: 262 83% 58%; /* Soft Lavender */
    --accent-foreground: 60 9% 98%;
    --destructive: 4 90% 58%; /* Terracotta */
    --destructive-foreground: 60 9% 98%;
    --border: 60 5% 85%;
    --input: 60 5% 90%;
    --ring: 142 23% 45%;
    --chart-1: 45 93% 66%; /* Golden Yellow */
    --chart-2: 142 23% 45%; /* Sage Green */
    --chart-3: 213 20% 58%; /* Dusty Blue */
    --chart-4: 4 90% 58%; /* Terracotta */
    --chart-5: 262 83% 58%; /* Soft Lavender */
    --radius: 0.75rem;
    --sidebar-background: 60 9% 96%;
    --sidebar-foreground: 210 22% 22%;
    --sidebar-primary: 142 23% 45%;
    --sidebar-primary-foreground: 60 9% 98%;
    --sidebar-accent: 60 5% 92%;
    --sidebar-accent-foreground: 210 22% 22%;
    --sidebar-border: 60 5% 85%;
    --sidebar-ring: 142 23% 45%;
  }
  .dark {
    /* Dark Living Paper - Midnight Parchment */
    --background: 210 22% 8%; /* Dark Charcoal */
    --foreground: 60 9% 95%; /* Light Cream */
    --card: 210 22% 10%;
    --card-foreground: 60 9% 95%;
    --popover: 210 22% 10%;
    --popover-foreground: 60 9% 95%;
    --primary: 142 23% 55%; /* Brighter Sage Green */
    --primary-foreground: 210 22% 8%;
    --secondary: 213 20% 68%; /* Lighter Dusty Blue */
    --secondary-foreground: 210 22% 8%;
    --muted: 210 22% 15%;
    --muted-foreground: 60 9% 75%;
    --accent: 262 83% 68%; /* Brighter Soft Lavender */
    --accent-foreground: 210 22% 8%;
    --destructive: 4 90% 68%; /* Brighter Terracotta */
    --destructive-foreground: 60 9% 95%;
    --border: 210 22% 20%;
    --input: 210 22% 15%;
    --ring: 142 23% 55%;
    --chart-1: 45 93% 76%; /* Brighter Golden Yellow */
    --chart-2: 142 23% 55%; /* Brighter Sage Green */
    --chart-3: 213 20% 68%; /* Brighter Dusty Blue */
    --chart-4: 4 90% 68%; /* Brighter Terracotta */
    --chart-5: 262 83% 68%; /* Brighter Soft Lavender */
    --sidebar-background: 210 22% 12%;
    --sidebar-foreground: 60 9% 90%;
    --sidebar-primary: 142 23% 55%;
    --sidebar-primary-foreground: 210 22% 8%;
    --sidebar-accent: 210 22% 18%;
    --sidebar-accent-foreground: 60 9% 90%;
    --sidebar-border: 210 22% 20%;
    --sidebar-ring: 142 23% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Hand-drawn loading spinner */
  .hand-drawn-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid transparent;
    border-top: 3px solid var(--sage-green);
    border-radius: 50%;
    animation: hand-draw-spin 1.5s ease-in-out infinite;
    position: relative;
  }

  .hand-drawn-spinner::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 2px solid transparent;
    border-top: 2px solid var(--dusty-blue);
    border-radius: 50%;
    animation: hand-draw-spin 2s ease-in-out infinite reverse;
  }

  @keyframes hand-draw-spin {
    0% {
      transform: rotate(0deg);
      border-radius: 50%;
    }
    25% {
      border-radius: 45% 55% 60% 40%;
    }
    50% {
      transform: rotate(180deg);
      border-radius: 40% 60% 55% 45%;
    }
    75% {
      border-radius: 55% 45% 40% 60%;
    }
    100% {
      transform: rotate(360deg);
      border-radius: 50%;
    }
  }

  /* Organic input fields */
  .organic-input {
    background: var(--cream-paper);
    border: 2px solid var(--sage-green);
    border-radius: 12px;
    padding: 12px 16px;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
    transform: rotate(-0.2deg);
    position: relative;
  }

  .organic-input:focus {
    outline: none;
    transform: rotate(0deg);
    border-color: var(--dusty-blue);
    box-shadow: 0 0 0 3px rgba(124, 147, 179, 0.1);
  }

  .organic-input::placeholder {
    color: var(--warm-charcoal);
    opacity: 0.6;
    font-style: italic;
  }

  /* Handwritten headings */
  .handwritten-heading {
    font-family: 'Caveat', cursive;
    font-weight: 600;
    color: var(--warm-charcoal);
    position: relative;
    display: inline-block;
  }

  .handwritten-heading::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--golden-yellow) 0%, var(--coral-pink) 100%);
    border-radius: 2px;
    transform: rotate(-0.5deg);
    opacity: 0.7;
    animation: ink-flow 1s ease-out;
  }

  /* Paper card with subtle shadow */
  .paper-card {
    background: var(--cream-paper);
    background-image: var(--paper-grain);
    border-radius: 16px;
    padding: 24px;
    box-shadow:
      0 4px 6px rgba(45, 55, 72, 0.07),
      0 1px 3px rgba(45, 55, 72, 0.06);
    border: 1px solid rgba(104, 160, 99, 0.1);
    position: relative;
    transform: rotate(-0.3deg);
    transition: all 0.3s ease;
  }

  .paper-card:hover {
    transform: rotate(0deg) translateY(-2px);
    box-shadow:
      0 10px 25px rgba(45, 55, 72, 0.1),
      0 4px 10px rgba(45, 55, 72, 0.06);
    animation: paper-float 3s ease-in-out infinite;
  }

  /* Enhanced PDF viewer with professional styling */
  .organic-pdf-viewer {
    background: var(--cream-paper);
    border-radius: 20px;
    padding: 0;
    position: relative;
    overflow: visible;
    min-height: 90vh;
  }

  .organic-pdf-viewer::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, var(--sage-green), var(--dusty-blue));
    border-radius: 23px;
    z-index: -1;
    opacity: 0.8;
  }

  /* Compact right side controls panel styling */
  .pdf-side-controls {
    background: var(--cream-paper);
    border: 1px solid var(--sage-green);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(45, 55, 72, 0.08);
    max-width: 64px;
  }

  /* Compact PDF control buttons */
  .pdf-control-button {
    background: var(--cream-paper);
    border: 1px solid var(--sage-green);
    border-radius: 6px;
    padding: 4px;
    color: var(--warm-charcoal);
    font-weight: 500;
    transition: all 0.2s ease;
    transform: rotate(-0.2deg);
    position: relative;
    min-height: 32px;
    font-size: 11px;
  }

  .pdf-control-button:hover {
    transform: rotate(0deg) translateY(-1px);
    background: var(--sage-green);
    color: white;
    box-shadow: 0 2px 8px rgba(104, 160, 99, 0.3);
  }

  .pdf-control-button:disabled {
    opacity: 0.5;
    transform: rotate(-0.2deg);
    cursor: not-allowed;
  }

  .pdf-control-button:disabled:hover {
    background: var(--cream-paper);
    color: var(--warm-charcoal);
    transform: rotate(-0.2deg);
    box-shadow: none;
  }

  /* PDF container with larger viewing area */
  .pdf-main-container {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    min-height: 85vh;
    box-shadow: inset 0 2px 8px rgba(45, 55, 72, 0.1);
    padding: 1.5rem;
  }

  /* PDF zoom indicator - compact version */
  .pdf-zoom-indicator {
    background: var(--cream-paper);
    border: 1px solid var(--sage-green);
    border-radius: 4px;
    padding: 2px 4px;
    font-size: 10px;
    font-weight: 600;
    color: var(--warm-charcoal);
    text-align: center;
    display: inline-block;
  }

  /* PDF loading overlay */
  .pdf-loading-overlay {
    background: linear-gradient(135deg, var(--cream-paper) 0%, rgba(255, 254, 247, 0.95) 100%);
    backdrop-filter: blur(8px);
    border-radius: 16px;
  }

  /* PDF iframe enhancements */
  .pdf-iframe {
    border-radius: 12px;
    box-shadow: inset 0 2px 8px rgba(45, 55, 72, 0.1);
    transition: all 0.3s ease;
  }

  .pdf-iframe:hover {
    box-shadow: inset 0 2px 12px rgba(45, 55, 72, 0.15);
  }

  /* Enhanced PDF document styling for scrollable view */
  .pdf-document {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .pdf-page {
    border-radius: 12px;
    display: block !important;
    transition: all 0.3s ease;
    margin: 0 auto;
  }

  .pdf-page canvas {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(45, 55, 72, 0.12);
    transition: all 0.3s ease;
    max-width: 100% !important;
    height: auto !important;
    border: 1px solid rgba(45, 55, 72, 0.1);
    display: block;
    margin: 0 auto;
  }

  .pdf-page canvas:hover {
    box-shadow: 0 12px 40px rgba(45, 55, 72, 0.2);
    transform: translateY(-1px);
  }

  /* React-PDF specific centering */
  .react-pdf__Document {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .react-pdf__Page {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto !important;
  }

  .react-pdf__Page__canvas {
    display: block !important;
    margin: 0 auto !important;
  }

  /* Smooth scrolling for PDF container */
  .pdf-main-container {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for PDF container */
  .pdf-main-container::-webkit-scrollbar {
    width: 8px;
  }

  .pdf-main-container::-webkit-scrollbar-track {
    background: var(--cream-paper);
    border-radius: 4px;
  }

  .pdf-main-container::-webkit-scrollbar-thumb {
    background: var(--sage-green);
    border-radius: 4px;
    border: 1px solid var(--cream-paper);
  }

  .pdf-main-container::-webkit-scrollbar-thumb:hover {
    background: #5a8f56;
  }

  /* React-PDF specific styling */
  .react-pdf__Document {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .react-pdf__Page {
    margin-bottom: 1rem;
  }

  .react-pdf__Page__textContent {
    user-select: text;
  }

  .react-pdf__Page__annotations {
    pointer-events: auto;
  }

  /* PDF viewer responsive design */
  @media (max-width: 768px) {
    .organic-pdf-viewer {
      border-radius: 16px;
    }

    .pdf-control-button {
      padding: 6px 10px;
      font-size: 14px;
    }

    .pdf-page canvas {
      max-width: 100% !important;
      height: auto !important;
    }
  }

  /* Improved scrollbar for PDF container */
  .organic-pdf-viewer div[style*="overflow"] {
    scrollbar-width: thin;
    scrollbar-color: var(--sage-green) var(--cream-paper);
  }

  .organic-pdf-viewer div[style*="overflow"]::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .organic-pdf-viewer div[style*="overflow"]::-webkit-scrollbar-track {
    background: var(--cream-paper);
    border-radius: 4px;
  }

  .organic-pdf-viewer div[style*="overflow"]::-webkit-scrollbar-thumb {
    background: var(--sage-green);
    border-radius: 4px;
    border: 1px solid var(--cream-paper);
  }

  .organic-pdf-viewer div[style*="overflow"]::-webkit-scrollbar-thumb:hover {
    background: #5a8f56;
  }

  /* Compilation status with hand-drawn elements */
  .compilation-status {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 12px;
    font-weight: 500;
    position: relative;
    transform: rotate(-0.5deg);
  }

  .compilation-status.success {
    background: rgba(104, 160, 99, 0.1);
    color: var(--sage-green);
    border: 2px solid var(--sage-green);
  }

  .compilation-status.error {
    background: rgba(184, 84, 80, 0.1);
    color: var(--terracotta);
    border: 2px solid var(--terracotta);
  }

  .compilation-status.compiling {
    background: rgba(124, 147, 179, 0.1);
    color: var(--dusty-blue);
    border: 2px solid var(--dusty-blue);
  }

  /* Advanced animations for enhanced user experience */
  @keyframes gentle-bounce {
    0%, 100% {
      transform: translateY(0px) rotate(-0.5deg);
    }
    50% {
      transform: translateY(-3px) rotate(0deg);
    }
  }

  @keyframes fade-in-up {
    0% {
      opacity: 0;
      transform: translateY(20px) rotate(-1deg);
    }
    100% {
      opacity: 1;
      transform: translateY(0px) rotate(-0.5deg);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  /* Enhanced button hover effects */
  .organic-button:hover {
    animation: gentle-bounce 0.6s ease-in-out;
  }

  /* Fade in animation for cards */
  .paper-card {
    animation: fade-in-up 0.6s ease-out;
  }

  /* Shimmer effect for loading states */
  .shimmer {
    background: linear-gradient(
      90deg,
      var(--cream-paper) 0%,
      rgba(104, 160, 99, 0.1) 50%,
      var(--cream-paper) 100%
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  /* Enhanced focus states */
  .organic-input:focus,
  .organic-button:focus {
    outline: 3px solid rgba(104, 160, 99, 0.3);
    outline-offset: 2px;
  }

  /* Smooth transitions for all interactive elements */
  .hand-drawn-border,
  .organic-button,
  .paper-card,
  .handwritten-heading {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced mobile responsiveness */
  @media (max-width: 768px) {
    .handwritten-heading {
      font-size: 1.2em;
    }

    .organic-button {
      padding: 10px 20px;
      font-size: 14px;
    }

    .paper-card {
      padding: 16px;
      transform: rotate(-0.2deg);
    }
  }
}

/* Custom scrollbar with organic styling */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--cream-paper);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--sage-green);
  border-radius: 6px;
  border: 2px solid var(--cream-paper);
}

::-webkit-scrollbar-thumb:hover {
  background: #5a8f56;
}
