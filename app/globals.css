/* This file is kept minimal to avoid conflicts with the main Living Paper design system */
/* Main styles are in ../styles/globals.css */

@layer utilities {
  /* Additional utility classes that don't conflict with main design */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
  }

  .safe-area-inset {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .responsive-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
