"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Head from "next/head"
import { DocumentEditor } from "@/components/research-dashboard/pages/document-editor"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { useResponsive } from "@/components/research-dashboard/hooks/use-responsive"
import { useAuth } from "@/lib/contexts/auth-context"
import { apiClient } from "@/lib/api/client"
import { toast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"
import type { Document } from "@/components/research-dashboard/types"

export default function DocumentPage() {
  const params = useParams()
  const router = useRouter()
  const documentId = params.documentId as string

  const [documentData, setDocumentData] = useState<Document | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const responsive = useResponsive()
  const { user, isAuthenticated, loading: authLoading } = useAuth()

  useEffect(() => {
    // Only load document when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && documentId) {
      loadDocument()
    } else if (!authLoading && !isAuthenticated) {
      // If not authenticated, redirect will be handled by ProtectedRoute
      setLoading(false)
    }

    // Cleanup: reset page title when component unmounts
    return () => {
      if (typeof window !== 'undefined') {
        document.title = "DeepDocX - Professional Document Creation"
      }
    }
  }, [documentId, authLoading, isAuthenticated])

  const loadDocument = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch document data from API
      const response = await apiClient.documents.get(documentId)

      if (!response || !response.document) {
        setError("Document not found")
        return
      }

      // Extract document from API response
      const docData = response.document

      // Transform API response to match component expectations
      const transformedDocument: Document = {
        id: docData.id || documentId,
        user_id: docData.user_id,
        title: docData.title || "Untitled Document",
        description: docData.description,
        type: docData.type || "research_paper",
        status: docData.status || "draft",
        metadata: docData.metadata || {},
        created_at: docData.created_at,
        updated_at: docData.updated_at
      }

      setDocumentData(transformedDocument)

      // Update page title
      if (typeof window !== 'undefined') {
        document.title = `${transformedDocument.title} - DeepDocX`
      }
    } catch (err: any) {
      console.error("Error loading document:", err)

      // Handle specific error cases
      if (err.message?.includes("401") || err.message?.includes("UNAUTHORIZED")) {
        setError("Authentication required. Please sign in again.")
        // Redirect to sign in page
        router.push("/auth/signin")
        return
      } else if (err.message?.includes("404") || err.message?.includes("NOT FOUND")) {
        setError("Document not found")
      } else {
        setError("Failed to load document")
      }

      toast({
        title: "Error",
        description: "Failed to load document. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBackToWelcome = () => {
    router.push("/dashboard")
  }

  const handleStateChange = (state: string) => {
    // Handle state changes if needed
    if (state === "welcome" || state === "generated-documents") {
      router.push("/dashboard")
    }
  }

  const handleShowSettings = () => {
    // Settings functionality can be implemented here
  }

  if (authLoading || loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">
              {authLoading ? "Authenticating..." : "Loading document..."}
            </p>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (error || !documentData) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-semibold text-foreground mb-2">Document Not Found</h1>
            <p className="text-muted-foreground mb-4">
              {error || "The document you're looking for doesn't exist or you don't have access to it."}
            </p>
            <button
              onClick={handleBackToWelcome}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <DocumentEditor
        document={documentData}
        onStateChange={handleStateChange}
        onBackToWelcome={handleBackToWelcome}
        onShowSettings={handleShowSettings}
        isMobile={responsive.isMobile}
        mobileMenuOpen={responsive.mobileMenuOpen}
        onToggleMobileMenu={responsive.toggleMobileMenu}
        onCloseMobileMenu={responsive.closeMobileMenu}
      />
    </ProtectedRoute>
  )
}
