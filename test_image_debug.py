#!/usr/bin/env python3
"""
Debug script to test image copying functionality
"""

import sys
import os
import tempfile
sys.path.append('backend-deepdocx')

# Mock the database calls for testing
class MockFileService:
    @staticmethod
    def get_user_files(user_id):
        return [
            {
                'id': '1',
                'name': 'database.jpg',
                'type': 'jpg',
                'folder_id': 'folder_images',
                'storage_path': '/tmp/mock_database.jpg'
            },
            {
                'id': '2', 
                'name': 'chart.png',
                'type': 'png',
                'folder_id': None,
                'storage_path': '/tmp/mock_chart.png'
            }
        ]

class MockFolderService:
    @staticmethod
    def get_user_folders(user_id, filter_type=None):
        return [
            {
                'id': 'folder_images',
                'name': 'images',
                'parent_folder_id': None
            },
            {
                'id': 'folder_figures',
                'name': 'figures', 
                'parent_folder_id': None
            }
        ]

# Create mock files
def create_mock_files():
    """Create mock image files for testing"""
    with open('/tmp/mock_database.jpg', 'w') as f:
        f.write('mock image content')
    with open('/tmp/mock_chart.png', 'w') as f:
        f.write('mock image content')

def test_image_copying():
    """Test the image copying logic"""
    
    # Create mock files
    create_mock_files()
    
    # Mock the imports
    import sys
    sys.modules['services.file_service'] = type('MockModule', (), {'FileService': MockFileService})()
    sys.modules['services.folder_service'] = type('MockModule', (), {'FolderService': MockFolderService})()
    
    from services.latex_service import LatexService
    
    # Test LaTeX code with image references
    latex_code = """
\\documentclass{article}
\\usepackage{graphicx}
\\begin{document}
\\includegraphics{images/database.jpg}
\\includegraphics{chart.png}
\\end{document}
"""
    
    print("Testing image copying with LaTeX code:")
    print(latex_code)
    print("\n" + "="*50 + "\n")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Temporary directory: {temp_dir}")
        
        # Test the image copying
        try:
            LatexService._copy_referenced_images(latex_code, temp_dir, 'test_user')
            
            # List files in temp directory
            print("\nFiles copied to temp directory:")
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, temp_dir)
                    print(f"  {rel_path}")
                for dir_name in dirs:
                    print(f"  {dir_name}/ (directory)")
                    
        except Exception as e:
            print(f"Error during image copying: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("Image Copying Debug Test")
    print("=" * 50)
    
    test_image_copying()
