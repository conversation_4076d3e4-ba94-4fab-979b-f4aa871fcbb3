# DeepDocX Backend Documentation
## Flask + Supabase Implementation Guide

## Overview

This document outlines the complete backend architecture for DeepDocX, a professional document creation platform. The backend is built with Flask and uses Supabase as the database and authentication provider.

## Technology Stack

- **Framework**: Flask (Python)
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage
- **LaTeX Compilation**: External service (TeX Live or Overleaf API)
- **Environment**: Python 3.9+

## Project Structure

```
backend/
├── app/
│   ├── __init__.py
│   ├── config.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── document.py
│   │   ├── folder.py
│   │   └── sharing.py
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── documents.py
│   │   ├── folders.py
│   │   ├── files.py
│   │   ├── latex.py
│   │   ├── sharing.py
│   │   └── conversations.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   ├── document_service.py
│   │   ├── latex_service.py
│   │   ├── file_service.py
│   │   └── sharing_service.py
│   └── utils/
│       ├── __init__.py
│       ├── decorators.py
│       ├── validators.py
│       └── helpers.py
├── migrations/
├── tests/
├── requirements.txt
├── .env
└── run.py
```

## Database Schema (Supabase)

### Users Table
```sql
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  bio TEXT,
  company VARCHAR(255),
  role VARCHAR(255),
  avatar_url TEXT,
  plan_type VARCHAR(50) DEFAULT 'free' CHECK (plan_type IN ('free', 'pro', 'enterprise')),
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Documents Table
```sql
CREATE TABLE documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(500) NOT NULL,
  description TEXT,
  type VARCHAR(100) DEFAULT 'research_paper',
  status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'completed', 'processing', 'error')),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Document Versions Table
```sql
CREATE TABLE document_versions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  latex_code TEXT NOT NULL,
  compiled_pdf_url TEXT,
  is_current BOOLEAN DEFAULT false,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, version_number)
);
```

### Folders Table
```sql
CREATE TABLE folders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  parent_folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, parent_folder_id, name)
);
```

### Files Table
```sql
CREATE TABLE files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL,
  size BIGINT,
  url TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Conversations Table
```sql
CREATE TABLE conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Messages Table
```sql
CREATE TABLE messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Shared Documents Table
```sql
CREATE TABLE shared_documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  shared_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  shared_with UUID REFERENCES users(id) ON DELETE CASCADE,
  share_token VARCHAR(255) UNIQUE,
  permissions VARCHAR(20) NOT NULL CHECK (permissions IN ('view', 'comment', 'edit')),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Core Features & Functionalities

### 1. Authentication System
- **User Registration**: Email/password with validation
- **User Login**: JWT token-based authentication
- **OAuth Integration**: Google and GitHub sign-in
- **Password Reset**: Email-based password recovery
- **Session Management**: Token refresh and validation
- **User Profile**: Update profile information and settings

### 2. Document Management
- **Document Creation**: Create new documents with AI assistance
- **Document Editing**: Real-time LaTeX editing
- **Document Listing**: View all user documents with search/filter
- **Document Deletion**: Soft/hard delete with confirmation
- **Document Metadata**: Title, description, type, status tracking
- **Document Types**: Research papers, invoices, reports, proposals, etc.

### 3. Version Control System
- **Version History**: Track all document changes
- **Version Comparison**: Compare different versions
- **Version Restoration**: Revert to previous versions
- **Current Version**: Mark and switch current version
- **Version Descriptions**: Add notes to versions

### 4. LaTeX Compilation
- **Real-time Compilation**: Compile LaTeX to PDF
- **Error Handling**: Display compilation errors
- **PDF Generation**: Store compiled PDFs
- **Download PDFs**: Download compiled documents
- **LaTeX Validation**: Syntax checking before compilation

### 5. File Management
- **Folder Structure**: Hierarchical folder organization
- **File Upload**: Support multiple file types (PDF, DOCX, images, CSV)
- **File Organization**: Move files between folders
- **File Search**: Search files by name and type
- **File Sharing**: Share files with other users

### 6. AI Chat Integration
- **Document Conversations**: AI assistance for document creation
- **Chat History**: Persistent conversation storage
- **Context Awareness**: AI understands document context
- **Message Threading**: Organized conversation flow

### 7. Sharing & Collaboration
- **Document Sharing**: Share documents with permissions
- **Permission Levels**: View, comment, edit access
- **Share Links**: Generate shareable links with expiration
- **Access Control**: Manage who can access documents
- **Share Revocation**: Remove access to shared documents

### 8. User Management
- **Profile Management**: Update user information
- **Plan Management**: Free, Pro, Enterprise plans
- **Settings**: Customizable user preferences
- **Avatar Upload**: Profile picture management

## API Endpoints Structure

### Authentication Routes (`/api/auth`)
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/signout` - User logout
- `GET /auth/me` - Get current user
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/forgot-password` - Password reset request
- `POST /auth/reset-password` - Reset password with token

### User Routes (`/api/users`)
- `GET /users/profile` - Get user profile
- `PATCH /users/profile` - Update user profile
- `DELETE /users/account` - Delete user account
- `POST /users/avatar` - Upload avatar

### Document Routes (`/api/documents`)
- `GET /documents` - List user documents
- `POST /documents` - Create new document
- `GET /documents/{id}` - Get document details
- `PATCH /documents/{id}` - Update document
- `DELETE /documents/{id}` - Delete document
- `GET /documents/{id}/versions` - Get document versions
- `POST /documents/{id}/versions` - Create new version
- `GET /documents/{id}/current-version` - Get current version
- `POST /documents/{id}/versions/{version_id}/set-current` - Set current version

### Folder Routes (`/api/folders`)
- `GET /folders` - List folders
- `POST /folders` - Create folder
- `PATCH /folders/{id}` - Update folder
- `DELETE /folders/{id}` - Delete folder

### File Routes (`/api/files`)
- `GET /files` - List files
- `POST /files/upload` - Upload file
- `DELETE /files/{id}` - Delete file
- `GET /files/{id}/download` - Download file

### LaTeX Routes (`/api/latex`)
- `POST /latex/compile` - Compile LaTeX to PDF
- `GET /latex/download/{document_id}` - Download compiled PDF

### Sharing Routes (`/api/share`)
- `POST /share` - Create document share
- `GET /share/{token}` - Access shared document
- `GET /share/document/{document_id}` - List document shares
- `DELETE /share/{share_id}` - Revoke share

### Conversation Routes (`/api/conversations`)
- `GET /conversations/document/{document_id}` - Get document conversation
- `GET /conversations/{id}/messages` - Get conversation messages
- `POST /conversations/{id}/messages` - Send message

## Environment Configuration

```env
# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret
JWT_ACCESS_TOKEN_EXPIRES=3600

# LaTeX Service
LATEX_SERVICE_URL=http://localhost:8080
LATEX_SERVICE_API_KEY=your-latex-api-key

# File Storage
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=pdf,docx,png,jpg,jpeg,csv

# Email Configuration (for password reset)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

## Dependencies (requirements.txt)

```txt
Flask==2.3.3
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3
supabase==1.0.4
python-dotenv==1.0.0
requests==2.31.0
Werkzeug==2.3.7
gunicorn==21.2.0
psycopg2-binary==2.9.7
python-multipart==0.0.6
Pillow==10.0.1
PyPDF2==3.0.1
python-docx==0.8.11
```

## Implementation Examples

### 1. Flask App Configuration (`app/__init__.py`)

```python
from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from supabase import create_client, Client
import os
from dotenv import load_dotenv

load_dotenv()

def create_app():
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = int(os.getenv('JWT_ACCESS_TOKEN_EXPIRES', 3600))

    # Initialize extensions
    CORS(app)
    jwt = JWTManager(app)

    # Supabase client
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    app.supabase = create_client(supabase_url, supabase_key)

    # Register blueprints
    from app.routes.auth import auth_bp
    from app.routes.documents import documents_bp
    from app.routes.folders import folders_bp
    from app.routes.files import files_bp
    from app.routes.latex import latex_bp
    from app.routes.sharing import sharing_bp
    from app.routes.conversations import conversations_bp

    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(documents_bp, url_prefix='/api/documents')
    app.register_blueprint(folders_bp, url_prefix='/api/folders')
    app.register_blueprint(files_bp, url_prefix='/api/files')
    app.register_blueprint(latex_bp, url_prefix='/api/latex')
    app.register_blueprint(sharing_bp, url_prefix='/api/share')
    app.register_blueprint(conversations_bp, url_prefix='/api/conversations')

    return app
```

### 2. Authentication Service (`app/services/auth_service.py`)

```python
from flask import current_app
from flask_jwt_extended import create_access_token, decode_token
from werkzeug.security import generate_password_hash, check_password_hash
import uuid
from datetime import datetime, timedelta

class AuthService:
    @staticmethod
    def register_user(email, password, first_name, last_name):
        """Register a new user"""
        supabase = current_app.supabase

        # Check if user exists
        existing_user = supabase.table('users').select('*').eq('email', email).execute()
        if existing_user.data:
            raise ValueError("User already exists")

        # Hash password
        password_hash = generate_password_hash(password)

        # Create user
        user_data = {
            'email': email,
            'password_hash': password_hash,
            'first_name': first_name,
            'last_name': last_name,
            'plan_type': 'free',
            'settings': {}
        }

        result = supabase.table('users').insert(user_data).execute()
        user = result.data[0]

        # Generate JWT token
        token = create_access_token(identity=user['id'])

        return {'user': user, 'token': token}

    @staticmethod
    def login_user(email, password):
        """Login user and return JWT token"""
        supabase = current_app.supabase

        # Get user by email
        result = supabase.table('users').select('*').eq('email', email).execute()
        if not result.data:
            raise ValueError("Invalid credentials")

        user = result.data[0]

        # Check password
        if not check_password_hash(user['password_hash'], password):
            raise ValueError("Invalid credentials")

        # Generate JWT token
        token = create_access_token(identity=user['id'])

        return {'user': user, 'token': token}

    @staticmethod
    def get_user_by_id(user_id):
        """Get user by ID"""
        supabase = current_app.supabase
        result = supabase.table('users').select('*').eq('id', user_id).execute()
        return result.data[0] if result.data else None
```

### 3. Document Service (`app/services/document_service.py`)

```python
from flask import current_app
from datetime import datetime
import uuid

class DocumentService:
    @staticmethod
    def create_document(user_id, title, description=None, doc_type='research_paper'):
        """Create a new document"""
        supabase = current_app.supabase

        # Create document
        document_data = {
            'user_id': user_id,
            'title': title,
            'description': description,
            'type': doc_type,
            'status': 'draft',
            'metadata': {}
        }

        result = supabase.table('documents').insert(document_data).execute()
        document = result.data[0]

        # Create initial version with LaTeX template
        latex_template = DocumentService._get_latex_template(doc_type)
        version_data = {
            'document_id': document['id'],
            'version_number': 1,
            'latex_code': latex_template,
            'is_current': True,
            'description': 'Initial version'
        }

        supabase.table('document_versions').insert(version_data).execute()

        return document

    @staticmethod
    def get_user_documents(user_id):
        """Get all documents for a user"""
        supabase = current_app.supabase
        result = supabase.table('documents').select('*').eq('user_id', user_id).order('updated_at', desc=True).execute()
        return result.data

    @staticmethod
    def get_document(document_id, user_id):
        """Get a specific document"""
        supabase = current_app.supabase
        result = supabase.table('documents').select('*').eq('id', document_id).eq('user_id', user_id).execute()
        return result.data[0] if result.data else None

    @staticmethod
    def update_document(document_id, user_id, **updates):
        """Update document"""
        supabase = current_app.supabase
        updates['updated_at'] = datetime.utcnow().isoformat()

        result = supabase.table('documents').update(updates).eq('id', document_id).eq('user_id', user_id).execute()
        return result.data[0] if result.data else None

    @staticmethod
    def delete_document(document_id, user_id):
        """Delete document"""
        supabase = current_app.supabase
        result = supabase.table('documents').delete().eq('id', document_id).eq('user_id', user_id).execute()
        return len(result.data) > 0

    @staticmethod
    def create_version(document_id, latex_code, description=None):
        """Create a new document version"""
        supabase = current_app.supabase

        # Get current version number
        versions = supabase.table('document_versions').select('version_number').eq('document_id', document_id).order('version_number', desc=True).limit(1).execute()
        next_version = (versions.data[0]['version_number'] + 1) if versions.data else 1

        # Set all versions as not current
        supabase.table('document_versions').update({'is_current': False}).eq('document_id', document_id).execute()

        # Create new version
        version_data = {
            'document_id': document_id,
            'version_number': next_version,
            'latex_code': latex_code,
            'is_current': True,
            'description': description
        }

        result = supabase.table('document_versions').insert(version_data).execute()
        return result.data[0]

    @staticmethod
    def _get_latex_template(doc_type):
        """Get LaTeX template based on document type"""
        templates = {
            'research_paper': '''\\documentclass[11pt,a4paper]{article}
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{geometry}
\\usepackage{amsmath}
\\usepackage{amsfonts}
\\usepackage{amssymb}
\\usepackage{graphicx}
\\usepackage{hyperref}

\\geometry{margin=1in}

\\title{Research Paper Title}
\\author{Author Name}
\\date{\\today}

\\begin{document}
\\maketitle

\\begin{abstract}
Your abstract here...
\\end{abstract}

\\section{Introduction}
Your introduction here...

\\section{Methodology}
Your methodology here...

\\section{Results}
Your results here...

\\section{Conclusion}
Your conclusion here...

\\end{document}''',
            'invoice': '''\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\usepackage{geometry}
\\usepackage{array}
\\usepackage{booktabs}

\\geometry{margin=1in}

\\begin{document}

\\begin{center}
{\\Large \\textbf{INVOICE}}
\\end{center}

\\vspace{1cm}

\\begin{tabular}{ll}
Invoice Number: & \\#001 \\\\
Date: & \\today \\\\
Due Date: & \\today \\\\
\\end{tabular}

\\vspace{1cm}

\\textbf{Bill To:}\\\\
Customer Name\\\\
Customer Address\\\\

\\vspace{1cm}

\\begin{tabular}{|l|c|c|c|}
\\hline
Description & Quantity & Rate & Amount \\\\
\\hline
Item 1 & 1 & \\$100.00 & \\$100.00 \\\\
\\hline
\\multicolumn{3}{|r|}{\\textbf{Total:}} & \\textbf{\\$100.00} \\\\
\\hline
\\end{tabular}

\\end{document}'''
        }
        return templates.get(doc_type, templates['research_paper'])
```

### 4. LaTeX Compilation Service (`app/services/latex_service.py`)

```python
import requests
import os
import tempfile
import subprocess
from flask import current_app

class LaTeXService:
    @staticmethod
    def compile_latex(document_id, latex_code):
        """Compile LaTeX code to PDF"""
        try:
            # Create temporary files
            with tempfile.NamedTemporaryFile(mode='w', suffix='.tex', delete=False) as tex_file:
                tex_file.write(latex_code)
                tex_filename = tex_file.name

            # Compile with pdflatex
            output_dir = os.path.dirname(tex_filename)
            pdf_filename = tex_filename.replace('.tex', '.pdf')

            # Run pdflatex
            result = subprocess.run([
                'pdflatex',
                '-output-directory', output_dir,
                '-interaction=nonstopmode',
                tex_filename
            ], capture_output=True, text=True)

            if result.returncode == 0 and os.path.exists(pdf_filename):
                # Upload PDF to Supabase Storage
                pdf_url = LaTeXService._upload_pdf_to_storage(document_id, pdf_filename)

                # Clean up temporary files
                os.unlink(tex_filename)
                if os.path.exists(pdf_filename):
                    os.unlink(pdf_filename)

                return {
                    'success': True,
                    'pdf_url': pdf_url,
                    'errors': []
                }
            else:
                # Parse errors from output
                errors = LaTeXService._parse_latex_errors(result.stderr)
                return {
                    'success': False,
                    'pdf_url': None,
                    'errors': errors
                }

        except Exception as e:
            return {
                'success': False,
                'pdf_url': None,
                'errors': [str(e)]
            }

    @staticmethod
    def _upload_pdf_to_storage(document_id, pdf_path):
        """Upload PDF to Supabase Storage"""
        supabase = current_app.supabase

        with open(pdf_path, 'rb') as pdf_file:
            file_name = f"documents/{document_id}/compiled.pdf"
            result = supabase.storage.from_('documents').upload(file_name, pdf_file)

            if result.get('error'):
                raise Exception(f"Failed to upload PDF: {result['error']}")

            # Get public URL
            public_url = supabase.storage.from_('documents').get_public_url(file_name)
            return public_url

    @staticmethod
    def _parse_latex_errors(stderr_output):
        """Parse LaTeX compilation errors"""
        errors = []
        lines = stderr_output.split('\n')

        for line in lines:
            if 'Error:' in line or 'error:' in line:
                errors.append(line.strip())

        return errors if errors else ['Unknown compilation error']

### 5. File Service (`app/services/file_service.py`)

```python
from flask import current_app
import os
from werkzeug.utils import secure_filename

class FileService:
    ALLOWED_EXTENSIONS = {'pdf', 'docx', 'png', 'jpg', 'jpeg', 'csv', 'txt'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

    @staticmethod
    def upload_file(file, user_id, folder_id=None):
        """Upload file to Supabase Storage"""
        if not FileService._allowed_file(file.filename):
            raise ValueError("File type not allowed")

        if file.content_length > FileService.MAX_FILE_SIZE:
            raise ValueError("File too large")

        supabase = current_app.supabase

        # Generate secure filename
        filename = secure_filename(file.filename)
        file_path = f"users/{user_id}/files/{filename}"

        # Upload to Supabase Storage
        result = supabase.storage.from_('files').upload(file_path, file.read())

        if result.get('error'):
            raise Exception(f"Failed to upload file: {result['error']}")

        # Get public URL
        public_url = supabase.storage.from_('files').get_public_url(file_path)

        # Save file metadata to database
        file_data = {
            'user_id': user_id,
            'folder_id': folder_id,
            'name': filename,
            'type': FileService._get_file_type(filename),
            'size': file.content_length,
            'url': public_url,
            'metadata': {}
        }

        result = supabase.table('files').insert(file_data).execute()
        return result.data[0]

    @staticmethod
    def get_user_files(user_id, folder_id=None):
        """Get files for user, optionally filtered by folder"""
        supabase = current_app.supabase
        query = supabase.table('files').select('*').eq('user_id', user_id)

        if folder_id:
            query = query.eq('folder_id', folder_id)
        else:
            query = query.is_('folder_id', 'null')

        result = query.order('created_at', desc=True).execute()
        return result.data

    @staticmethod
    def delete_file(file_id, user_id):
        """Delete file"""
        supabase = current_app.supabase

        # Get file info
        file_result = supabase.table('files').select('*').eq('id', file_id).eq('user_id', user_id).execute()
        if not file_result.data:
            return False

        file_info = file_result.data[0]

        # Delete from storage
        file_path = file_info['url'].split('/')[-1]  # Extract path from URL
        supabase.storage.from_('files').remove([file_path])

        # Delete from database
        result = supabase.table('files').delete().eq('id', file_id).eq('user_id', user_id).execute()
        return len(result.data) > 0

    @staticmethod
    def _allowed_file(filename):
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in FileService.ALLOWED_EXTENSIONS

    @staticmethod
    def _get_file_type(filename):
        """Get file type from filename"""
        if '.' in filename:
            return filename.rsplit('.', 1)[1].lower()
        return 'unknown'

### 6. Sharing Service (`app/services/sharing_service.py`)

```python
from flask import current_app
import secrets
from datetime import datetime, timedelta

class SharingService:
    @staticmethod
    def create_share(document_id, shared_by, shared_with=None, permissions='view', expires_in_hours=None):
        """Create a document share"""
        supabase = current_app.supabase

        # Generate share token
        share_token = secrets.token_urlsafe(32)

        # Calculate expiration
        expires_at = None
        if expires_in_hours:
            expires_at = (datetime.utcnow() + timedelta(hours=expires_in_hours)).isoformat()

        share_data = {
            'document_id': document_id,
            'shared_by': shared_by,
            'shared_with': shared_with,
            'share_token': share_token,
            'permissions': permissions,
            'expires_at': expires_at
        }

        result = supabase.table('shared_documents').insert(share_data).execute()
        return result.data[0]

    @staticmethod
    def get_shared_document(share_token):
        """Get shared document by token"""
        supabase = current_app.supabase

        # Get share info
        share_result = supabase.table('shared_documents').select('*').eq('share_token', share_token).execute()
        if not share_result.data:
            return None

        share = share_result.data[0]

        # Check if expired
        if share['expires_at']:
            expires_at = datetime.fromisoformat(share['expires_at'])
            if datetime.utcnow() > expires_at:
                return None

        # Get document
        doc_result = supabase.table('documents').select('*').eq('id', share['document_id']).execute()
        if not doc_result.data:
            return None

        return {
            'document': doc_result.data[0],
            'permissions': share['permissions'],
            'share_info': share
        }

    @staticmethod
    def get_document_shares(document_id, user_id):
        """Get all shares for a document"""
        supabase = current_app.supabase

        # Verify user owns the document
        doc_result = supabase.table('documents').select('*').eq('id', document_id).eq('user_id', user_id).execute()
        if not doc_result.data:
            return []

        result = supabase.table('shared_documents').select('*').eq('document_id', document_id).execute()
        return result.data

    @staticmethod
    def revoke_share(share_id, user_id):
        """Revoke a document share"""
        supabase = current_app.supabase

        # Verify user owns the share
        share_result = supabase.table('shared_documents').select('*').eq('id', share_id).execute()
        if not share_result.data:
            return False

        share = share_result.data[0]

        # Check if user owns the document
        doc_result = supabase.table('documents').select('*').eq('id', share['document_id']).eq('user_id', user_id).execute()
        if not doc_result.data:
            return False

        # Delete share
        result = supabase.table('shared_documents').delete().eq('id', share_id).execute()
        return len(result.data) > 0
```

## Route Examples

### 7. Authentication Routes (`app/routes/auth.py`)

```python
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.auth_service import AuthService
from app.utils.validators import validate_email, validate_password

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/signup', methods=['POST'])
def signup():
    """User registration endpoint"""
    try:
        data = request.get_json()

        # Validate input
        email = data.get('email')
        password = data.get('password')
        first_name = data.get('first_name')
        last_name = data.get('last_name')

        if not all([email, password, first_name, last_name]):
            return jsonify({'error': 'Missing required fields'}), 400

        if not validate_email(email):
            return jsonify({'error': 'Invalid email format'}), 400

        if not validate_password(password):
            return jsonify({'error': 'Password does not meet requirements'}), 400

        # Register user
        result = AuthService.register_user(email, password, first_name, last_name)

        return jsonify({
            'message': 'User registered successfully',
            'user': {
                'id': result['user']['id'],
                'email': result['user']['email'],
                'first_name': result['user']['first_name'],
                'last_name': result['user']['last_name']
            },
            'token': result['token']
        }), 201

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/signin', methods=['POST'])
def signin():
    """User login endpoint"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')

        if not email or not password:
            return jsonify({'error': 'Email and password required'}), 400

        result = AuthService.login_user(email, password)

        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': result['user']['id'],
                'email': result['user']['email'],
                'first_name': result['user']['first_name'],
                'last_name': result['user']['last_name']
            },
            'token': result['token']
        }), 200

    except ValueError as e:
        return jsonify({'error': str(e)}), 401
    except Exception as e:
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """Get current user information"""
    try:
        user_id = get_jwt_identity()
        user = AuthService.get_user_by_id(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({
            'user': {
                'id': user['id'],
                'email': user['email'],
                'first_name': user['first_name'],
                'last_name': user['last_name'],
                'bio': user.get('bio'),
                'company': user.get('company'),
                'role': user.get('role'),
                'plan_type': user['plan_type']
            }
        }), 200

    except Exception as e:
        return jsonify({'error': 'Internal server error'}), 500

### 8. Document Routes (`app/routes/documents.py`)

```python
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.document_service import DocumentService

documents_bp = Blueprint('documents', __name__)

@documents_bp.route('', methods=['GET'])
@jwt_required()
def get_documents():
    """Get all documents for current user"""
    try:
        user_id = get_jwt_identity()
        documents = DocumentService.get_user_documents(user_id)
        return jsonify({'documents': documents}), 200
    except Exception as e:
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('', methods=['POST'])
@jwt_required()
def create_document():
    """Create a new document"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        title = data.get('title')
        description = data.get('description')
        doc_type = data.get('type', 'research_paper')

        if not title:
            return jsonify({'error': 'Title is required'}), 400

        document = DocumentService.create_document(user_id, title, description, doc_type)
        return jsonify({'document': document}), 201

    except Exception as e:
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>', methods=['GET'])
@jwt_required()
def get_document(document_id):
    """Get a specific document"""
    try:
        user_id = get_jwt_identity()
        document = DocumentService.get_document(document_id, user_id)

        if not document:
            return jsonify({'error': 'Document not found'}), 404

        return jsonify({'document': document}), 200
    except Exception as e:
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>/versions', methods=['POST'])
@jwt_required()
def create_version(document_id):
    """Create a new document version"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Verify user owns the document
        document = DocumentService.get_document(document_id, user_id)
        if not document:
            return jsonify({'error': 'Document not found'}), 404

        latex_code = data.get('latex_code')
        description = data.get('description')

        if not latex_code:
            return jsonify({'error': 'LaTeX code is required'}), 400

        version = DocumentService.create_version(document_id, latex_code, description)
        return jsonify({'version': version}), 201

    except Exception as e:
        return jsonify({'error': 'Internal server error'}), 500
```

## Deployment Configuration

### 1. Docker Configuration (`Dockerfile`)

```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    texlive-latex-base \
    texlive-latex-extra \
    texlive-fonts-recommended \
    texlive-fonts-extra \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 5000

# Run the application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "run:app"]
```

### 2. Docker Compose (`docker-compose.yml`)

```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped
```

### 3. Production Setup Script (`setup.sh`)

```bash
#!/bin/bash

# Install system dependencies
sudo apt-get update
sudo apt-get install -y python3 python3-pip python3-venv nginx

# Install TeX Live for LaTeX compilation
sudo apt-get install -y texlive-latex-base texlive-latex-extra texlive-fonts-recommended

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
echo "Please edit .env file with your configuration"

# Set up systemd service
sudo cp deepdocx.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable deepdocx
sudo systemctl start deepdocx

# Configure Nginx
sudo cp nginx.conf /etc/nginx/sites-available/deepdocx
sudo ln -s /etc/nginx/sites-available/deepdocx /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

echo "Setup complete! Your DeepDocX backend is now running."
```

## Security Best Practices

### 1. Input Validation
- Validate all user inputs
- Sanitize LaTeX code to prevent injection
- Implement file type and size restrictions
- Use parameterized queries

### 2. Authentication & Authorization
- Use strong JWT secrets
- Implement token expiration
- Validate user permissions for all operations
- Use HTTPS in production

### 3. Rate Limiting
- Implement API rate limiting
- Limit file upload sizes
- Throttle LaTeX compilation requests

### 4. Error Handling
- Don't expose sensitive information in errors
- Log errors for debugging
- Return consistent error formats

## Testing Strategy

### 1. Unit Tests
```python
# Example test for document service
import unittest
from app.services.document_service import DocumentService

class TestDocumentService(unittest.TestCase):
    def test_create_document(self):
        # Test document creation
        pass

    def test_get_user_documents(self):
        # Test document retrieval
        pass
```

### 2. Integration Tests
- Test API endpoints
- Test database operations
- Test file upload/download
- Test LaTeX compilation

### 3. Load Testing
- Test concurrent users
- Test file upload limits
- Test LaTeX compilation under load

## Monitoring & Logging

### 1. Application Logging
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

### 2. Performance Monitoring
- Monitor API response times
- Track database query performance
- Monitor file storage usage
- Track LaTeX compilation times

This comprehensive documentation provides everything needed to implement a robust Flask backend for DeepDocX with Supabase integration.
```
