/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Handle PDF.js worker
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        canvas: false,
        fs: false,
        path: false,
      }
    }

    // Copy PDF.js worker to public directory during build
    config.module.rules.push({
      test: /pdf\.worker\.(min\.)?js/,
      type: 'asset/resource',
      generator: {
        filename: 'static/worker/[hash][ext][query]',
      },
    })

    return config
  },
  
  // Enable experimental features for better PDF handling
  experimental: {
    esmExternals: 'loose',
  },

  // Ensure proper handling of ES modules
  transpilePackages: ['react-pdf', 'pdfjs-dist'],
}

module.exports = nextConfig
