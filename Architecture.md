# DeepDocX Architecture Documentation

## Overview

DeepDocX is a professional document creation platform built with Next.js, featuring AI-powered writing assistance, LaTeX compilation, real-time collaboration, and comprehensive document management. The system is designed with a modular architecture that separates concerns and enables easy scaling and maintenance.

## System Architecture

### Frontend Architecture

\`\`\`
┌─────────────────────────────────────────────────────────────┐
│                    Next.js Frontend                         │
├─────────────────────────────────────────────────────────────┤
│  Components/                                                │
│  ├── auth/           # Authentication components            │
│  ├── ui/             # Reusable UI components (shadcn/ui)   │
│  ├── landing-page.tsx                                       │
│  ├── research-dashboard.tsx                                 │
│  └── settings-dialog.tsx                                    │
├─────────────────────────────────────────────────────────────┤
│  Lib/                                                       │
│  ├── api/            # API client and endpoints             │
│  ├── hooks/          # Custom React hooks                   │
│  ├── services/       # Business logic services             │
│  ├── types/          # TypeScript type definitions          │
│  └── utils/          # Utility functions                    │
├─────────────────────────────────────────────────────────────┤
│  App/                                                       │
│  ├── auth/           # Authentication pages                 │
│  ├── dashboard/      # Main dashboard                       │
│  ├── shared/         # Shared document viewer               │
│  └── layout.tsx      # Root layout                          │
└─────────────────────────────────────────────────────────────┘
\`\`\`

### Backend Architecture

\`\`\`
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
├─────────────────────────────────────────────────────────────┤
│  /api/auth/*         # Authentication endpoints             │
│  /api/users/*        # User management                      │
│  /api/documents/*    # Document CRUD operations             │
│  /api/folders/*      # Folder management                    │
│  /api/files/*        # File upload/management               │
│  /api/conversations/* # Chat/AI interactions                │
│  /api/latex/*        # LaTeX compilation                    │
│  /api/share/*        # Document sharing                     │
└─────────────────────────────────────────────────────────────┘
\`\`\`

### Database Schema

\`\`\`
┌─────────────────────────────────────────────────────────────┐
│                    Supabase Database                        │
├─────────────────────────────────────────────────────────────┤
│  users                                                      │
│  ├── id (UUID, PK)                                          │
│  ├── email (VARCHAR, UNIQUE)                                │
│  ├── password_hash (VARCHAR)                                │
│  ├── first_name, last_name (VARCHAR)                        │
│  ├── bio, company, role (VARCHAR, NULLABLE)                 │
│  ├── avatar_url (TEXT, NULLABLE)                            │
│  ├── plan_type (ENUM: free, pro, enterprise)                │
│  ├── settings (JSONB)                                       │
│  └── created_at, updated_at (TIMESTAMP)                     │
├─────────────────────────────────────────────────────────────┤
│  documents                                                  │
│  ├── id (UUID, PK)                                          │
│  ├── user_id (UUID, FK → users.id)                          │
│  ├── title (VARCHAR)                                        │
│  ├── description (TEXT, NULLABLE)                           │
│  ├── type (VARCHAR)                                         │
│  ├── status (ENUM: draft, completed, processing, error)     │
│  ├── metadata (JSONB)                                       │
│  └── created_at, updated_at (TIMESTAMP)                     │
├─────────────────────────────────────────────────────────────┤
│  document_versions                                          │
│  ├── id (UUID, PK)                                          │
│  ├── document_id (UUID, FK → documents.id)                  │
│  ├── version_number (INTEGER)                               │
│  ├── latex_code (TEXT)                                      │
│  ├── compiled_pdf_url (TEXT, NULLABLE)                      │
│  ├── is_current (BOOLEAN)                                   │
│  ├── description (TEXT, NULLABLE)                           │
│  └── created_at (TIMESTAMP)                                 │
├─────────────────────────────────────────────────────────────┤
│  conversations                                              │
│  ├── id (UUID, PK)                                          │
│  ├── document_id (UUID, FK → documents.id)                  │
│  ├── user_id (UUID, FK → users.id)                          │
│  └── created_at, updated_at (TIMESTAMP)                     │
├─────────────────────────────────────────────────────────────┤
│  messages                                                   │
│  ├── id (UUID, PK)                                          │
│  ├── conversation_id (UUID, FK → conversations.id)          │
│  ├── role (ENUM: user, assistant, system)                   │
│  ├── content (TEXT)                                         │
│  ├── metadata (JSONB)                                       │
│  └── created_at (TIMESTAMP)                                 │
├─────────────────────────────────────────────────────────────┤
│  folders                                                    │
│  ├── id (UUID, PK)                                          │
│  ├── user_id (UUID, FK → users.id)                          │
│  ├── name (VARCHAR)                                         │
│  ├── parent_folder_id (UUID, FK → folders.id, NULLABLE)     │
│  └── created_at, updated_at (TIMESTAMP)                     │
├─────────────────────────────────────────────────────────────┤
│  files                                                      │
│  ├── id (UUID, PK)                                          │
│  ├── user_id (UUID, FK → users.id)                          │
│  ├── folder_id (UUID, FK → folders.id, NULLABLE)            │
│  ├── name (VARCHAR)                                         │
│  ├── type (VARCHAR)                                         │
│  ├── size (BIGINT, NULLABLE)                                │
│  ├── url (TEXT, NULLABLE)                                   │
│  ├── metadata (JSONB)                                       │
│  └── created_at, updated_at (TIMESTAMP)                     │
├─────────────────────────────────────────────────────────────┤
│  shared_documents                                           │
│  ├── id (UUID, PK)                                          │
│  ├── document_id (UUID, FK → documents.id)                  │
│  ├── shared_by (UUID, FK → users.id)                        │
│  ├── shared_with (UUID, FK → users.id, NULLABLE)            │
│  ├── share_token (VARCHAR, UNIQUE, NULLABLE)                │
│  ├── permissions (ENUM: view, comment, edit)                │
│  ├── expires_at (TIMESTAMP, NULLABLE)                       │
│  └── created_at (TIMESTAMP)                                 │
└─────────────────────────────────────────────────────────────┘
\`\`\`

## Core Services

### 1. Authentication Service
- **Location**: `lib/api/client.ts` (auth endpoints)
- **Purpose**: Handle user authentication, registration, and session management
- **Features**:
  - Email/password authentication
  - OAuth integration (Google, GitHub)
  - JWT token management
  - Session persistence

### 2. Document Management Service
- **Location**: `lib/hooks/useDocuments.ts`
- **Purpose**: CRUD operations for documents and version control
- **Features**:
  - Document creation, editing, deletion
  - Version history tracking
  - Current version management
  - Document metadata handling

### 3. LaTeX Compilation Service
- **Location**: `lib/services/latex.ts`
- **Purpose**: Handle LaTeX code compilation to PDF
- **Features**:
  - LaTeX syntax validation
  - Compilation queue management
  - Error handling and reporting
  - PDF generation and storage

### 4. Sharing Service
- **Location**: `lib/services/sharing.ts`
- **Purpose**: Document sharing and collaboration
- **Features**:
  - Generate shareable links
  - Permission management (view, comment, edit)
  - Expiration handling
  - Public/private sharing

### 5. File Management Service
- **Location**: `lib/api/client.ts` (files/folders endpoints)
- **Purpose**: Handle file uploads and folder organization
- **Features**:
  - File upload to cloud storage
  - Folder hierarchy management
  - File type validation
  - Storage quota management

## Data Flow

### Document Creation Flow
\`\`\`
1. User inputs document description
2. Frontend calls createDocument API
3. Backend creates document record in database
4. Initial LaTeX template is generated
5. First version is created in document_versions
6. Document is marked as current version
7. Frontend updates UI with new document
\`\`\`

### LaTeX Compilation Flow
\`\`\`
1. User modifies LaTeX code
2. User clicks "Recompile" button
3. Frontend calls latex.compile API
4. Backend validates LaTeX syntax
5. LaTeX is sent to compilation service
6. PDF is generated and stored in cloud storage
7. New version is created with PDF URL
8. Frontend displays compilation result
\`\`\`

### Document Sharing Flow
\`\`\`
1. User clicks "Share Document"
2. User sets permissions and optional email
3. Frontend calls share.create API
4. Backend generates unique share token
5. Share record is created in database
6. Share URL is generated and copied to clipboard
7. Recipient accesses document via share URL
\`\`\`

### Version Management Flow
\`\`\`
1. User makes changes to document
2. System auto-saves or user manually saves
3. New version is created in document_versions
4. Previous version is marked as not current
5. New version becomes current version
6. Version history is updated in UI
7. User can switch between versions
\`\`\`

## API Endpoints

### Authentication
- `POST /api/auth/signin` - User sign in
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signout` - User sign out
- `GET /api/auth/me` - Get current user

### Documents
- `GET /api/documents` - List user documents
- `POST /api/documents` - Create new document
- `GET /api/documents/:id` - Get document details
- `PATCH /api/documents/:id` - Update document
- `DELETE /api/documents/:id` - Delete document
- `GET /api/documents/:id/versions` - Get document versions
- `POST /api/documents/:id/versions` - Create new version
- `GET /api/documents/:id/current-version` - Get current version
- `POST /api/documents/:id/versions/:versionId/set-current` - Set current version

### LaTeX Compilation
- `POST /api/latex/compile` - Compile LaTeX to PDF
- `GET /api/latex/download/:documentId` - Download compiled PDF

### Sharing
- `POST /api/share` - Create document share
- `GET /api/share/:token` - Access shared document
- `GET /api/share/document/:documentId` - List document shares
- `DELETE /api/share/:shareId` - Revoke share

### Files & Folders
- `GET /api/folders` - List folders
- `POST /api/folders` - Create folder
- `PATCH /api/folders/:id` - Update folder
- `DELETE /api/folders/:id` - Delete folder
- `GET /api/files` - List files
- `POST /api/files/upload` - Upload file
- `DELETE /api/files/:id` - Delete file

## State Management

### React Hooks Pattern
The application uses custom React hooks for state management:

- `useDocuments()` - Document list and CRUD operations
- `useDocumentVersions()` - Version history management
- `useLatexCompilation()` - Compilation state and results
- `useSharing()` - Document sharing functionality

### Local State
- Component-level state for UI interactions
- Form state for user inputs
- Loading states for async operations
- Error states for error handling

## Security Considerations

### Authentication
- JWT tokens for session management
- Secure password hashing (bcrypt)
- OAuth integration for third-party auth
- Session expiration and refresh

### Authorization
- User-based document access control
- Permission-based sharing (view, comment, edit)
- Share token validation
- Expiration handling for shared links

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting on API endpoints

## Deployment Architecture

### Frontend Deployment
- **Platform**: Vercel
- **Features**: 
  - Automatic deployments from Git
  - Edge functions for API routes
  - CDN for static assets
  - Environment variable management

### Database
- **Platform**: Supabase
- **Features**:
  - PostgreSQL database
  - Real-time subscriptions
  - Row-level security
  - Automatic backups

### File Storage
- **Platform**: Vercel Blob / AWS S3
- **Features**:
  - PDF storage for compiled documents
  - File uploads for user assets
  - CDN distribution
  - Access control

### LaTeX Compilation
- **Options**:
  - Docker containers with TeX Live
  - Overleaf API integration
  - Custom compilation service
  - Serverless functions

## Customization Guide

### Adding New Document Types
1. Update `document.type` enum in database schema
2. Create new LaTeX templates in `lib/templates/`
3. Add type-specific validation in `lib/services/latex.ts`
4. Update UI components to handle new type

### Extending API Endpoints
1. Create new endpoint in `app/api/` directory
2. Add corresponding client method in `lib/api/client.ts`
3. Create custom hook if needed in `lib/hooks/`
4. Update TypeScript types in `lib/types/`

### Adding New Sharing Permissions
1. Update `permissions` enum in database schema
2. Modify sharing logic in `lib/services/sharing.ts`
3. Update UI components for permission selection
4. Add permission validation in API endpoints

### Customizing LaTeX Compilation
1. Modify `LatexService` in `lib/services/latex.ts`
2. Add new validation rules
3. Integrate with external compilation services
4. Update error handling and reporting

### Theme Customization
1. Modify Tailwind configuration in `tailwind.config.ts`
2. Update CSS variables in `app/globals.css`
3. Customize component styles in `components/ui/`
4. Add new color schemes or themes

## Performance Optimizations

### Frontend
- React.memo for expensive components
- Lazy loading for large components
- Debounced search and auto-save
- Virtual scrolling for large lists
- Image optimization with Next.js

### Backend
- Database indexing for common queries
- Connection pooling
- Caching for frequently accessed data
- Pagination for large datasets
- Background job processing

### LaTeX Compilation
- Compilation queue to prevent overload
- Caching of compiled PDFs
- Incremental compilation for large documents
- Resource limits and timeouts

## Monitoring and Analytics

### Error Tracking
- Frontend error boundaries
- API error logging
- Database error monitoring
- Compilation failure tracking

### Performance Monitoring
- Page load times
- API response times
- Database query performance
- Compilation times

### User Analytics
- Document creation metrics
- Feature usage tracking
- User engagement metrics
- Conversion funnel analysis

## Future Enhancements

### Planned Features
1. Real-time collaborative editing
2. Advanced AI writing assistance
3. Template marketplace
4. Mobile application
5. Offline editing capabilities
6. Advanced analytics dashboard
7. Enterprise SSO integration
8. API for third-party integrations

### Scalability Improvements
1. Microservices architecture
2. Horizontal database scaling
3. CDN optimization
4. Caching layer implementation
5. Load balancing
6. Auto-scaling infrastructure

This architecture provides a solid foundation for a professional document creation platform while maintaining flexibility for future enhancements and customizations.
\`\`\`

Perfect! I've created a comprehensive system with:

## **🏗️ Complete Architecture:**

### **1. Database Schema (Supabase):**
- **Users table** with authentication and profile data
- **Documents table** for document metadata
- **Document_versions table** for LaTeX code and version history
- **Conversations & Messages** for AI chat history
- **Folders & Files** for file management
- **Shared_documents** for sharing functionality

### **2. API Client Structure:**
- **Modular API client** with organized endpoints
- **Type-safe interfaces** for all data structures
- **Authentication handling** with JWT tokens
- **Error handling** and response management

### **3. Core Services:**
- **LaTeX Service** - Compilation, validation, PDF generation
- **Sharing Service** - Document sharing with permissions
- **Custom React Hooks** - State management for documents, versions, compilation

### **4. Key Features Implemented:**
- **Document version control** with current/history tracking
- **LaTeX compilation** with error handling and validation
- **Document sharing** with permissions and expiration
- **File management** with folder hierarchy
- **Real-time UI updates** with loading states

### **5. Architecture Documentation:**
- **Complete system overview** in Architecture.md
- **Database schema** with relationships
- **API endpoint structure** 
- **Data flow diagrams**
- **Customization guide** for modifications
- **Security considerations**
- **Deployment architecture**

The system is now ready for backend implementation - all the API endpoints are structured to be easily replaceable with real implementations, and the frontend is fully integrated with the service layer!
