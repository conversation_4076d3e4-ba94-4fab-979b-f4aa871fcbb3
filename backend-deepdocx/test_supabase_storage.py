#!/usr/bin/env python3
"""
Test script to verify Supabase Storage integration
"""

import os
import sys
import tempfile
import logging
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables first
load_dotenv()

# Import Flask app and services
from app import create_app
from services.supabase_client import get_service_supabase
from services.file_service import FileService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app instance
app = create_app()

def test_supabase_connection():
    """Test basic Supabase connection"""
    with app.app_context():
        try:
            supabase = get_service_supabase()

            # Test database connection
            result = supabase.table('users').select('count').execute()
            logger.info("✅ Supabase database connection successful")

            # Test storage connection
            try:
                buckets = supabase.storage.list_buckets()
                if hasattr(buckets, 'error') and buckets.error:
                    logger.error(f"❌ Storage connection failed: {buckets.error}")
                    return False

                # Handle different response formats
                if hasattr(buckets, 'data'):
                    bucket_list = buckets.data
                else:
                    bucket_list = buckets

                bucket_names = [bucket.name for bucket in bucket_list]

            except Exception as e:
                logger.error(f"❌ Storage connection failed: {e}")
                return False
            logger.info(f"✅ Supabase storage connection successful. Buckets: {bucket_names}")

            # Check for required buckets
            required_buckets = ['files', 'documents']
            missing_buckets = [bucket for bucket in required_buckets if bucket not in bucket_names]

            if missing_buckets:
                logger.error(f"❌ Missing required buckets: {missing_buckets}")
                logger.info("Run setup_supabase_storage.sql to create missing buckets")
                return False

            logger.info("✅ All required storage buckets found")
            return True

        except Exception as e:
            logger.error(f"❌ Supabase connection failed: {e}")
            return False

def test_file_upload():
    """Test file upload to Supabase Storage"""
    with app.app_context():
        try:
            # Create a test file
            test_content = b"This is a test file for Supabase Storage integration"

            with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
                temp_file.write(test_content)
                temp_file_path = temp_file.name

            # Create a mock file object
            class MockFile:
                def __init__(self, file_path, filename):
                    self.file_path = file_path
                    self.filename = filename
                    self._position = 0

                def read(self):
                    with open(self.file_path, 'rb') as f:
                        return f.read()

                def seek(self, position, whence=0):
                    if whence == 2:  # Seek to end
                        self._position = os.path.getsize(self.file_path)
                    else:
                        self._position = position

                def tell(self):
                    return self._position

            mock_file = MockFile(temp_file_path, 'test_file.txt')

            # Test upload with proper UUID
            import uuid
            test_user_id = str(uuid.uuid4())  # Generate proper UUID

            try:
                # Test direct storage upload instead of full file service
                supabase = get_service_supabase()
                storage_path = f"users/{test_user_id}/files/test_file.txt"

                # Upload directly to storage
                upload_result = supabase.storage.from_('files').upload(
                    storage_path,
                    test_content,
                    file_options={'content-type': 'text/plain'}
                )

                logger.info(f"✅ File upload successful to: {storage_path}")

                # Test download
                download_result = supabase.storage.from_('files').download(storage_path)

                if hasattr(download_result, 'error') and download_result.error:
                    logger.error(f"❌ File download failed: {download_result.error}")
                    return False

                # Handle different response formats
                downloaded_data = download_result.data if hasattr(download_result, 'data') else download_result

                if downloaded_data == test_content:
                    logger.info("✅ File download successful - content matches")
                else:
                    logger.error("❌ File download failed - content mismatch")
                    return False

                # Cleanup test file from storage
                supabase.storage.from_('files').remove([storage_path])
                logger.info("✅ Test file cleaned up from storage")

                return True

            except Exception as e:
                logger.error(f"❌ File upload test failed: {e}")
                return False

            finally:
                # Cleanup local temp file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"❌ File upload test setup failed: {e}")
            return False

def test_migration_status():
    """Check migration status of existing files"""
    with app.app_context():
        try:
            supabase = get_service_supabase()

            # Get all files from database
            result = supabase.table('files').select('*').execute()
            files = result.data

            if not files:
                logger.info("ℹ️  No files found in database")
                return True

            logger.info(f"📊 Found {len(files)} files in database")

            local_files = 0
            migrated_files = 0

            for file_record in files:
                storage_path = file_record.get('storage_path', '')

                if storage_path.startswith(('uploads/', '/')):
                    local_files += 1
                elif 'users/' in storage_path and 'files/' in storage_path:
                    migrated_files += 1

            logger.info(f"📈 Migration status:")
            logger.info(f"   Migrated files: {migrated_files}")
            logger.info(f"   Local files: {local_files}")

            if local_files == 0:
                logger.info("✅ All files migrated to Supabase Storage")
            else:
                logger.warning(f"⚠️  {local_files} files still need migration")
                logger.info("Run: python migrate_files_to_supabase.py")

            return True

        except Exception as e:
            logger.error(f"❌ Migration status check failed: {e}")
            return False

def main():
    """Run all tests"""
    logger.info("🧪 Testing Supabase Storage Integration")
    logger.info("=" * 50)
    
    tests = [
        ("Supabase Connection", test_supabase_connection),
        ("File Upload/Download", test_file_upload),
        ("Migration Status", test_migration_status),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Testing: {test_name}")
        logger.info("-" * 30)
        
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Supabase Storage is ready to use.")
    else:
        logger.warning(f"⚠️  {total - passed} test(s) failed. Check configuration and try again.")
        sys.exit(1)

if __name__ == '__main__':
    main()
