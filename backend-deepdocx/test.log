This is pdfTeX, Version 3.14159265-2.6-1.40.20 (TeX Live 2019/Debian) (preloaded format=pdflatex 2025.6.10)  10 JUN 2025 15:56
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**test.tex
(./test.tex
LaTeX2e <2020-02-02> patch level 2
L3 programming layer <2020-02-14>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2019/12/20 v1.4l Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2019/12/20 v1.4l Standard LaTeX file (size option)
)
\c@part=\count167
\c@section=\count168
\c@subsection=\count169
\c@subsubsection=\count170
\c@paragraph=\count171
\c@subparagraph=\count172
\c@figure=\count173
\c@table=\count174
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen134
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks14
\pgfutil@tempdima=\dimen135
\pgfutil@tempdimb=\dimen136

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.t
ex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box45
(/usr/share/texlive/texmf-dist/tex/latex/ms/everyshi.sty
Package: everyshi 2001/05/15 v3.00 EveryShipout Package (MS)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2020/01/08 v3.1.5b (3.1.5b)
))
Package: pgf 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2019/11/30 v1.2a Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks15
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2019/11/30 v1.4a Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 105.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2018/01/08 v1.0l Graphics/color driver for pdftex
))
\Gin@req@height=\dimen137
\Gin@req@width=\dimen138
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks16
\pgfkeys@temptoks=\toks17

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.t
ex
\pgfkeys@tmptoks=\toks18
))
\pgf@x=\dimen139
\pgf@y=\dimen140
\pgf@xa=\dimen141
\pgf@ya=\dimen142
\pgf@xb=\dimen143
\pgf@yb=\dimen144
\pgf@xc=\dimen145
\pgf@yc=\dimen146
\pgf@xd=\dimen147
\pgf@yd=\dimen148
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count175
\c@pgf@countb=\count176
\c@pgf@countc=\count177
\c@pgf@countd=\count178
\t@pgf@toka=\toks19
\t@pgf@tokb=\toks20
\t@pgf@tokc=\toks21
\pgf@sys@id@count=\count179
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2020/01/08 v3.1.5b (3.1.5b)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfsyssoftpath@smallbuffer@items=\count180
\pgfsyssoftpath@bigbuffer@items=\count181
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen149
\pgfmath@count=\count182
\pgfmath@box=\box46
\pgfmath@toks=\toks22
\pgfmath@stack@operand=\toks23
\pgfmath@stack@operation=\toks24
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count183
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@picminx=\dimen150
\pgf@picmaxx=\dimen151
\pgf@picminy=\dimen152
\pgf@picmaxy=\dimen153
\pgf@pathminx=\dimen154
\pgf@pathmaxx=\dimen155
\pgf@pathminy=\dimen156
\pgf@pathmaxy=\dimen157
\pgf@xx=\dimen158
\pgf@xy=\dimen159
\pgf@yx=\dimen160
\pgf@yy=\dimen161
\pgf@zx=\dimen162
\pgf@zy=\dimen163
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@path@lastx=\dimen164
\pgf@path@lasty=\dimen165
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@shorten@end@additional=\dimen166
\pgf@shorten@start@additional=\dimen167
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfpic=\box47
\pgf@hbox=\box48
\pgf@layerbox@main=\box49
\pgf@picture@serial@count=\count184
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgflinewidth=\dimen168
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@pt@x=\dimen169
\pgf@pt@y=\dimen170
\pgf@pt@temp=\dimen171
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfarrowsep=\dimen172
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@max=\dimen173
\pgf@sys@shading@range@num=\count185
\pgf@shadingcount=\count186
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfexternal@startupbox=\box50
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfnodeparttextbox=\box51
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2020/01/08 v3.1.5b (3.1.5b)
\pgf@nodesepstart=\dimen174
\pgf@nodesepend=\dimen175
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2020/01/08 v3.1.5b (3.1.5b)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen176
\pgffor@skip=\dimen177
\pgffor@stack=\toks25
\pgffor@toks=\toks26
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@plot@mark@count=\count187
\pgfplotmarksize=\dimen178
)
\tikz@lastx=\dimen179
\tikz@lasty=\dimen180
\tikz@lastxsaved=\dimen181
\tikz@lastysaved=\dimen182
\tikz@lastmovetox=\dimen183
\tikz@lastmovetoy=\dimen184
\tikzleveldistance=\dimen185
\tikzsiblingdistance=\dimen186
\tikz@figbox=\box52
\tikz@figbox@bg=\box53
\tikz@tempbox=\box54
\tikz@tempbox@bg=\box55
\tikztreelevel=\count188
\tikznumberofchildren=\count189
\tikznumberofcurrentchild=\count190
\tikz@fig@count=\count191

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfmatrixcurrentrow=\count192
\pgfmatrixcurrentcolumn=\count193
\pgf@matrix@numberofcolumns=\count194
)
\tikz@expandcount=\count195

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2019/11/15 version 4.22 text color boxes

(/usr/share/texlive/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2019/11/10 v1.5r LaTeX2e package for verbatim enhancements
\every@verbatim=\toks27
\verbatim@line=\toks28
\verbatim@in@stream=\read3
)
(/usr/share/texlive/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(/usr/share/texlive/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)
\@envbody=\toks29
)
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2019/09/21 v2.5h e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count196
)
\tcb@titlebox=\box56
\tcb@upperbox=\box57
\tcb@lowerbox=\box58
\tcb@phantombox=\box59
\c@tcbbreakpart=\count197
\c@tcblayer=\count198
\tcolorbox@number=\count199
\tcb@temp=\box60
\tcb@temp=\box61
\tcb@temp=\box62
\tcb@temp=\box63
\tcb@out=\write4
\tcb@record@out=\write5
)
(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdfmode.def
File: l3backend-pdfmode.def 2020-02-03 L3 backend support: PDF mode
\l__kernel_color_stack_int=\count266
\l__pdf_internal_box=\box64
)
No file test.aux.
\openout1 = `test.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
ABD: EveryShipout initializing macros
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count267
\scratchdimen=\dimen187
\scratchbox=\box65
\nofMPsegments=\count268
\nofMParguments=\count269
\everyMPshowfont=\toks30
\MPscratchCnt=\count270
\MPscratchDim=\dimen188
\MPnumerator=\count271
\makeMPintoPDFobject=\count272
\everyMPtoPDFconversion=\toks31
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}] (./test.aux) ) 
Here is how much of TeX's memory you used:
 13372 strings out of 483107
 281789 string characters out of 5964630
 485312 words of memory out of 5000000
 28211 multiletter control sequences out of 15000+600000
 532338 words of font info for 24 fonts, out of 8000000 for 9000
 59 hyphenation exceptions out of 8191
 60i,5n,75p,826b,106s stack positions out of 5000i,500n,10000p,200000b,80000s
</usr/share/
texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb>
Output written on test.pdf (1 page, 15464 bytes).
PDF statistics:
 15 PDF objects out of 1000 (max. 8388607)
 10 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 13 words of extra memory for PDF output out of 10000 (max. 10000000)

