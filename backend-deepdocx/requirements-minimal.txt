# Minimal requirements for DeepDocX Backend
# Core functionality only

# Flask framework
Flask==2.3.3
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3

# Supabase client
supabase==1.2.0

# Password hashing
bcrypt==4.0.1

# HTTP requests for OAuth
requests==2.31.0

# Environment variables
python-dotenv==1.0.0

# OpenAI for AI integration
openai==0.28.1

# File handling
Pillow==10.0.1

# Production WSGI server
gunicorn==21.2.0
