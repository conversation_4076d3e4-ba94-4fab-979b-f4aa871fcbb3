-- Temporarily disable Row Level Security for development
-- Run this in your Supabase SQL editor to allow the service role to work properly

-- Disable <PERSON><PERSON> on all tables
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE folders DISABLE ROW LEVEL SECURITY;
ALTER TABLE files DISABLE ROW LEVEL SECURITY;
ALTER TABLE documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions DISABLE ROW LEVEL SECURITY;
ALTER TABLE conversations DISABLE ROW LEVEL SECURITY;
ALTER TABLE messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE shared_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE oauth_sessions DISABLE ROW LEVEL SECURITY;

-- Note: In production, you should re-enable RLS and configure proper policies
-- This is just for development to get the backend working quickly
