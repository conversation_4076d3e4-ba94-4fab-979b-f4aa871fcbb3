#!/bin/bash

# TeX Live Complete Installation Script for DeepDocX
# This script installs a comprehensive TeX Live distribution with ALL packages
# to provide Overleaf-like package support

set -e  # Exit on any error

echo "=========================================="
echo "TeX Live Complete Installation for DeepDocX"
echo "=========================================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "This script should not be run as root for security reasons."
   echo "Please run as a regular user with sudo privileges."
   exit 1
fi

# Update system packages
echo "Updating system packages..."
sudo apt update

# Remove any existing partial TeX Live installations
echo "Removing any existing partial TeX Live installations..."
sudo apt remove -y texlive-* || true

# Install dependencies
echo "Installing dependencies..."
sudo apt install -y \
    wget \
    perl \
    fontconfig \
    python3-pygments \
    ghostscript \
    imagemagick \
    curl \
    unzip

# Create temporary directory for installation
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

echo "Working in temporary directory: $TEMP_DIR"

# Download TeX Live installer
echo "Downloading TeX Live installer..."
wget -O install-tl-unx.tar.gz http://mirror.ctan.org/systems/texlive/tlnet/install-tl-unx.tar.gz

# Extract installer
echo "Extracting TeX Live installer..."
tar -xzf install-tl-unx.tar.gz
cd install-tl-*

# Create installation profile for full installation
echo "Creating TeX Live installation profile..."
cat > texlive.profile << 'EOF'
# TeX Live installation profile for complete installation
selected_scheme scheme-full
TEXDIR /usr/local/texlive/2024
TEXMFCONFIG ~/.texlive2024/texmf-config
TEXMFHOME ~/texmf
TEXMFLOCAL /usr/local/texlive/texmf-local
TEXMFSYSCONFIG /usr/local/texlive/2024/texmf-config
TEXMFSYSVAR /usr/local/texlive/2024/texmf-var
TEXMFVAR ~/.texlive2024/texmf-var
binary_x86_64-linux 1
collection-basic 1
collection-bibtexextra 1
collection-binextra 1
collection-context 1
collection-fontsextra 1
collection-fontsrecommended 1
collection-fontutils 1
collection-formatsextra 1
collection-games 1
collection-humanities 1
collection-langarabic 1
collection-langchinese 1
collection-langcjk 1
collection-langcyrillic 1
collection-langczechslovak 1
collection-langenglish 1
collection-langeuropean 1
collection-langfrench 1
collection-langgerman 1
collection-langgreek 1
collection-langitalian 1
collection-langjapanese 1
collection-langkorean 1
collection-langother 1
collection-langpolish 1
collection-langportuguese 1
collection-langspanish 1
collection-latex 1
collection-latexextra 1
collection-latexrecommended 1
collection-luatex 1
collection-mathscience 1
collection-metapost 1
collection-music 1
collection-pictures 1
collection-plaingeneric 1
collection-pstricks 1
collection-publishers 1
collection-texworks 1
collection-wintools 0
collection-xetex 1
instopt_adjustpath 1
instopt_adjustrepo 1
instopt_letter 0
instopt_portable 0
instopt_write18_restricted 1
tlpdbopt_autobackup 1
tlpdbopt_backupdir tlpkg/backups
tlpdbopt_create_formats 1
tlpdbopt_desktop_integration 1
tlpdbopt_file_assocs 1
tlpdbopt_generate_updmap 0
tlpdbopt_install_docfiles 1
tlpdbopt_install_srcfiles 1
tlpdbopt_post_code 1
tlpdbopt_sys_bin /usr/local/bin
tlpdbopt_sys_info /usr/local/share/info
tlpdbopt_sys_man /usr/local/share/man
tlpdbopt_w32_multi_user 1
EOF

echo "Starting TeX Live installation (this may take 30-60 minutes)..."
echo "Installing complete TeX Live distribution with ALL packages..."

# Run the installer with the profile
sudo ./install-tl --profile=texlive.profile --no-interaction

# Add TeX Live to PATH
echo "Configuring PATH for TeX Live..."
TEXLIVE_PATH="/usr/local/texlive/2024/bin/x86_64-linux"

# Add to system-wide PATH
echo "export PATH=\"$TEXLIVE_PATH:\$PATH\"" | sudo tee /etc/profile.d/texlive.sh
sudo chmod +x /etc/profile.d/texlive.sh

# Add to current session
export PATH="$TEXLIVE_PATH:$PATH"

# Update font cache
echo "Updating font cache..."
sudo fc-cache -fv

# Verify installation
echo "Verifying TeX Live installation..."
which pdflatex
pdflatex --version

# Test package availability
echo "Testing package availability..."
cd /tmp
cat > test_packages.tex << 'EOF'
\documentclass{article}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{tcolorbox}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{minted}
\usepackage{beamer}
\begin{document}
All packages loaded successfully!
\end{document}
EOF

if pdflatex -interaction=nonstopmode test_packages.tex; then
    echo "✅ SUCCESS: All major packages are available!"
else
    echo "⚠️  Some packages may need additional setup"
fi

# Clean up
cd /
rm -rf "$TEMP_DIR"

echo "=========================================="
echo "TeX Live installation completed!"
echo "=========================================="
echo "✅ Complete TeX Live distribution installed"
echo "✅ ALL LaTeX packages available (like Overleaf)"
echo "✅ PATH configured for system-wide access"
echo "✅ Ready for DeepDocX integration"
echo ""
echo "To use in current session, run:"
echo "export PATH=\"$TEXLIVE_PATH:\$PATH\""
echo ""
echo "Or restart your terminal/session."
echo "=========================================="
