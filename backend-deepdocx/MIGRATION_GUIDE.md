# DeepDocX Storage Migration Guide
## From Local Storage to Supabase Storage

This guide will help you migrate your DeepDocX backend from local file storage to Supabase Storage.

## Overview

**Current State**: Files are stored locally in the `uploads/` directory with metadata in Supabase database.
**Target State**: All files stored in Supabase Storage with metadata in Supabase database.

## Prerequisites

1. Supabase project with database already set up
2. Supabase Service Role Key configured in environment variables
3. Backend application running and accessible

## Migration Steps

### Step 1: Set up Supabase Storage

1. **Run the storage setup SQL script**:
   ```bash
   # In your Supabase SQL Editor, run:
   cat setup_supabase_storage.sql
   ```
   This will create the necessary storage buckets and policies.

2. **Verify storage buckets are created**:
   - Go to your Supabase dashboard
   - Navigate to Storage
   - You should see `files` and `documents` buckets

### Step 2: Update Environment Variables

Ensure your `.env` file has the required Supabase configuration:

```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### Step 3: Test New File Upload

1. **Test file upload with new system**:
   ```bash
   # Upload a test file through the API
   curl -X POST http://localhost:5000/api/files/upload \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -F "file=@test_file.pdf"
   ```

2. **Verify file is stored in Supabase Storage**:
   - Check Supabase Storage dashboard
   - File should appear in `files` bucket under `users/{user_id}/files/`

### Step 4: Migrate Existing Files

1. **Run the migration script**:
   ```bash
   cd backend-deepdocx
   python migrate_files_to_supabase.py
   ```

2. **Verify migration**:
   ```bash
   python migrate_files_to_supabase.py verify
   ```

### Step 5: Update Frontend (if needed)

The frontend should continue to work without changes, but you may want to update file download URLs:

- **Old**: `/api/files/download/{filename}`
- **New**: `/api/files/{file_id}/download`

### Step 6: Test Complete System

1. **Test file upload**: Upload new files through the frontend
2. **Test file download**: Download files through the frontend
3. **Test document compilation**: Create documents with image references
4. **Test @mention system**: Use @folder/image.png references in LaTeX

### Step 7: Cleanup (Optional)

After successful migration and testing:

1. **Remove local upload directory**:
   ```bash
   # Backup first (optional)
   tar -czf uploads_backup.tar.gz uploads/
   
   # Remove uploads directory
   rm -rf uploads/
   ```

2. **Remove legacy download endpoint** (in future update):
   - Remove `/download/<filename>` route from `routes/files.py`

## Troubleshooting

### Common Issues

1. **Storage bucket not found**:
   - Ensure you ran `setup_supabase_storage.sql`
   - Check bucket names in Supabase dashboard

2. **Permission denied errors**:
   - Verify `SUPABASE_SERVICE_ROLE_KEY` is correct
   - Check RLS policies in Supabase

3. **File not found during migration**:
   - Some local files may be missing
   - Migration script will log warnings for missing files

4. **Large file upload failures**:
   - Check file size limits in Supabase Storage settings
   - Current limit is set to 50MB

### Rollback Plan

If you need to rollback to local storage:

1. **Restore uploads directory**:
   ```bash
   tar -xzf uploads_backup.tar.gz
   ```

2. **Revert file service changes**:
   ```bash
   git checkout HEAD~1 -- services/file_service.py
   ```

## Verification Checklist

- [ ] Supabase Storage buckets created
- [ ] Environment variables configured
- [ ] New file uploads work
- [ ] Existing files migrated successfully
- [ ] File downloads work
- [ ] Document compilation with images works
- [ ] @mention system works with migrated files
- [ ] No errors in application logs

## Benefits After Migration

1. **Scalability**: No local disk space limitations
2. **Reliability**: Built-in redundancy and backups
3. **Performance**: CDN-backed file delivery
4. **Security**: Fine-grained access control with RLS
5. **Maintenance**: No need to manage local file storage

## Support

If you encounter issues during migration:

1. Check application logs for detailed error messages
2. Verify Supabase Storage dashboard for uploaded files
3. Run the verification script to check migration status
4. Ensure all environment variables are correctly set
