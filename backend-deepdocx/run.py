#!/usr/bin/env python3
"""
Development server runner for DeepDocX Backend
"""

import os
from app import create_app

if __name__ == '__main__':
    app = create_app()
    
    # Development configuration
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    
    print(f"Starting DeepDocX Backend on {host}:{port}")
    print(f"Debug mode: {debug}")
    print(f"Frontend URL: {os.getenv('FRONTEND_URL', 'http://localhost:3000')}")
    
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True
    )
