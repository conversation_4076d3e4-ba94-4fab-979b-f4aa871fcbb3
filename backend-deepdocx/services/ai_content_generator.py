"""
AI-powered content generation for comprehensive documents
"""

import openai
import logging
from flask import current_app
from typing import Dict, List, Any, Optional
import re

logger = logging.getLogger(__name__)

class AIContentGenerator:
    """AI-powered content generator for creating substantial, detailed content"""
    
    @staticmethod
    def _get_openai_client():
        """Get configured OpenAI client"""
        api_key = current_app.config.get('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not configured")
        openai.api_key = api_key
        return openai
    
    @staticmethod
    def generate_comprehensive_section(
        section_title: str,
        section_description: str,
        document_context: Dict[str, Any],
        extracted_content: Dict[str, str] = None,
        target_length: int = 500
    ) -> str:
        """Generate comprehensive content for a document section"""
        
        try:
            client = AIContentGenerator._get_openai_client()
            
            # Build context for content generation
            context_info = AIContentGenerator._build_context_prompt(
                document_context, extracted_content
            )
            
            # Create detailed prompt for substantial content
            prompt = f"""
            You are an expert academic and professional writer. Generate comprehensive, detailed content for a document section.
            
            SECTION DETAILS:
            - Title: {section_title}
            - Description: {section_description}
            - Target length: {target_length} words minimum
            - Document type: {document_context.get('document_type', 'research paper')}
            - Domain: {document_context.get('domain', 'general')}
            
            CONTEXT:
            {context_info}
            
            REQUIREMENTS:
            1. Write substantial, detailed content (minimum {target_length} words)
            2. Use professional, academic tone appropriate for {document_context.get('document_type', 'research paper')}
            3. Include specific examples, evidence, and detailed explanations
            4. Structure content with subsections using LaTeX formatting
            5. Incorporate relevant data and insights from provided context
            6. Use proper LaTeX formatting (sections, subsections, lists, tables where appropriate)
            7. Ensure content is factually grounded and well-researched
            8. Include citations and references where appropriate using [Author et al., Year] format
            
            CONTENT STRUCTURE:
            - Start with \\section{{{section_title}}}
            - Use \\subsection{{}} for major topics within the section
            - Use \\subsubsection{{}} for detailed subtopics
            - Include itemized lists with \\begin{{itemize}} when listing key points
            - Add tables with \\begin{{table}} when presenting data
            - Use \\textbf{{}} for emphasis on key terms
            
            Generate comprehensive, detailed content that thoroughly covers the topic:
            """
            
            response = client.ChatCompletion.create(
                model=current_app.config.get('OPENAI_MODEL', 'gpt-4'),
                messages=[
                    {"role": "system", "content": "You are an expert academic and professional writer specializing in creating comprehensive, detailed documents with substantial content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,  # Increased for longer content
                temperature=0.3,
                request_timeout=120  # Longer timeout for comprehensive content
            )
            
            content = response.choices[0].message.content.strip()
            
            # Clean up the response
            content = re.sub(r'^```latex\s*\n?', '', content)
            content = re.sub(r'\n?```$', '', content)
            
            return content
            
        except Exception as e:
            logger.error(f"Error generating AI content for section {section_title}: {e}")
            # Return substantial fallback content
            return AIContentGenerator._generate_fallback_content(
                section_title, section_description, document_context, target_length
            )
    
    @staticmethod
    def generate_research_paper_section(
        section_title: str,
        document_context: Dict[str, Any],
        extracted_content: Dict[str, str] = None
    ) -> str:
        """Generate comprehensive research paper section content"""
        
        section_prompts = {
            'introduction': f"""
            Generate a comprehensive Introduction section for a research paper about {document_context.get('title', 'the research topic')}.
            
            The introduction should include:
            1. Background and context (2-3 paragraphs)
            2. Problem statement and significance (1-2 paragraphs)
            3. Research questions and objectives (1 paragraph)
            4. Scope and limitations (1 paragraph)
            5. Structure overview (1 paragraph)
            
            Minimum 800 words. Use academic tone with proper citations.
            """,
            
            'literature review': f"""
            Generate a comprehensive Literature Review section for a research paper about {document_context.get('title', 'the research topic')}.
            
            The literature review should include:
            1. Theoretical foundations (2-3 paragraphs)
            2. Previous empirical research (3-4 paragraphs)
            3. Methodological approaches (2 paragraphs)
            4. Gaps and limitations in existing research (2 paragraphs)
            5. Synthesis and implications for current study (1-2 paragraphs)
            
            Minimum 1000 words. Include multiple citations and critical analysis.
            """,
            
            'methodology': f"""
            Generate a comprehensive Methodology section for a research paper about {document_context.get('title', 'the research topic')}.
            
            The methodology should include:
            1. Research design and approach (2 paragraphs)
            2. Data collection procedures (2-3 paragraphs)
            3. Sampling strategy and participants (1-2 paragraphs)
            4. Analytical methods and techniques (2-3 paragraphs)
            5. Validity and reliability measures (1-2 paragraphs)
            6. Ethical considerations (1 paragraph)
            
            Minimum 800 words. Be specific and detailed about procedures.
            """,
            
            'results': f"""
            Generate a comprehensive Results section for a research paper about {document_context.get('title', 'the research topic')}.
            
            The results should include:
            1. Descriptive statistics and sample characteristics (2 paragraphs)
            2. Primary findings with statistical analysis (3-4 paragraphs)
            3. Secondary findings and patterns (2-3 paragraphs)
            4. Tables and figures with detailed descriptions (2-3 tables/figures)
            5. Hypothesis testing results (2 paragraphs)
            
            Minimum 900 words. Include specific statistical results and data presentations.
            """,
            
            'discussion': f"""
            Generate a comprehensive Discussion section for a research paper about {document_context.get('title', 'the research topic')}.
            
            The discussion should include:
            1. Interpretation of key findings (3-4 paragraphs)
            2. Comparison with previous research (2-3 paragraphs)
            3. Theoretical implications (2 paragraphs)
            4. Practical implications (2 paragraphs)
            5. Limitations and constraints (2 paragraphs)
            6. Future research directions (1-2 paragraphs)
            
            Minimum 1000 words. Provide deep analysis and interpretation.
            """,
            
            'conclusion': f"""
            Generate a comprehensive Conclusion section for a research paper about {document_context.get('title', 'the research topic')}.
            
            The conclusion should include:
            1. Summary of key findings (2 paragraphs)
            2. Contributions to knowledge (1-2 paragraphs)
            3. Practical implications and applications (1-2 paragraphs)
            4. Final recommendations (1 paragraph)
            5. Closing statement (1 paragraph)
            
            Minimum 600 words. Synthesize all major points and contributions.
            """
        }
        
        section_key = section_title.lower()
        specific_prompt = section_prompts.get(section_key, f"Generate comprehensive content for the {section_title} section.")
        
        return AIContentGenerator._generate_with_specific_prompt(
            section_title, specific_prompt, document_context, extracted_content
        )
    
    @staticmethod
    def generate_business_report_section(
        section_title: str,
        document_context: Dict[str, Any],
        extracted_content: Dict[str, str] = None
    ) -> str:
        """Generate comprehensive business report section content"""
        
        section_prompts = {
            'executive summary': f"""
            Generate a comprehensive Executive Summary for a business report about {document_context.get('title', 'business analysis')}.
            
            Include:
            1. Purpose and scope (1 paragraph)
            2. Key findings summary (2-3 paragraphs)
            3. Strategic recommendations (2 paragraphs)
            4. Expected outcomes and benefits (1 paragraph)
            
            Minimum 500 words. Use business-appropriate language.
            """,
            
            'analysis': f"""
            Generate a comprehensive Analysis section for a business report about {document_context.get('title', 'business analysis')}.
            
            Include:
            1. Market analysis and trends (3-4 paragraphs)
            2. Performance metrics and KPIs (2-3 paragraphs)
            3. Competitive landscape (2-3 paragraphs)
            4. SWOT analysis (2 paragraphs)
            5. Financial analysis (2-3 paragraphs)
            
            Minimum 1000 words. Include data-driven insights and charts/tables.
            """,
            
            'recommendations': f"""
            Generate comprehensive Recommendations for a business report about {document_context.get('title', 'business analysis')}.
            
            Include:
            1. Strategic recommendations (3-4 detailed recommendations)
            2. Implementation roadmap (2 paragraphs)
            3. Resource requirements (1-2 paragraphs)
            4. Risk assessment and mitigation (2 paragraphs)
            5. Success metrics and KPIs (1 paragraph)
            
            Minimum 800 words. Be specific and actionable.
            """
        }
        
        section_key = section_title.lower()
        specific_prompt = section_prompts.get(section_key, f"Generate comprehensive business content for the {section_title} section.")
        
        return AIContentGenerator._generate_with_specific_prompt(
            section_title, specific_prompt, document_context, extracted_content
        )
    
    @staticmethod
    def _generate_with_specific_prompt(
        section_title: str,
        specific_prompt: str,
        document_context: Dict[str, Any],
        extracted_content: Dict[str, str] = None
    ) -> str:
        """Generate content using a specific detailed prompt"""
        
        try:
            client = AIContentGenerator._get_openai_client()
            
            context_info = AIContentGenerator._build_context_prompt(document_context, extracted_content)
            
            full_prompt = f"""
            {specific_prompt}
            
            DOCUMENT CONTEXT:
            {context_info}
            
            FORMATTING REQUIREMENTS:
            - Start with \\section{{{section_title}}}
            - Use \\subsection{{}} and \\subsubsection{{}} for organization
            - Include \\begin{{itemize}} lists for key points
            - Add \\begin{{table}} for data presentation
            - Use \\textbf{{}} for emphasis
            - Include proper LaTeX formatting throughout
            
            Generate substantial, detailed content:
            """
            
            response = client.ChatCompletion.create(
                model=current_app.config.get('OPENAI_MODEL', 'gpt-4'),
                messages=[
                    {"role": "system", "content": "You are an expert writer creating comprehensive, detailed professional documents."},
                    {"role": "user", "content": full_prompt}
                ],
                max_tokens=2500,
                temperature=0.3,
                request_timeout=120
            )
            
            content = response.choices[0].message.content.strip()
            content = re.sub(r'^```latex\s*\n?', '', content)
            content = re.sub(r'\n?```$', '', content)
            
            return content
            
        except Exception as e:
            logger.error(f"Error generating specific content for {section_title}: {e}")
            return AIContentGenerator._generate_fallback_content(
                section_title, "Comprehensive section content", document_context, 600
            )
    
    @staticmethod
    def _build_context_prompt(document_context: Dict[str, Any], extracted_content: Dict[str, str] = None) -> str:
        """Build context information for content generation"""
        
        context_parts = []
        
        # Document information
        if document_context.get('title'):
            context_parts.append(f"Document Title: {document_context['title']}")
        
        if document_context.get('domain'):
            context_parts.append(f"Domain/Field: {document_context['domain']}")
        
        if document_context.get('key_concepts'):
            concepts = ', '.join(document_context['key_concepts'][:5])
            context_parts.append(f"Key Concepts: {concepts}")
        
        # Extracted content information
        if extracted_content:
            context_parts.append("Available Data Sources:")
            for file_name, content_info in extracted_content.items():
                summary = content_info.get('summary', 'Data file')
                context_parts.append(f"- {file_name}: {summary}")
        
        return '\n'.join(context_parts) if context_parts else "General academic/professional document"
    
    @staticmethod
    def _generate_fallback_content(
        section_title: str,
        section_description: str,
        document_context: Dict[str, Any],
        target_length: int
    ) -> str:
        """Generate substantial fallback content when AI generation fails"""
        
        domain = document_context.get('domain', 'general')
        key_concepts = document_context.get('key_concepts', ['topic', 'analysis', 'research'])
        
        return f"""\\section{{{section_title}}}

This section provides a comprehensive examination of {section_title.lower()} within the context of {domain}. The analysis encompasses multiple dimensions and perspectives to ensure thorough coverage of the topic.

\\subsection{{Overview and Context}}

The importance of {section_title.lower()} in {domain} cannot be overstated. Current research and practice in this area have established several key principles and approaches that guide effective implementation and understanding. The complexity of modern {domain} systems requires comprehensive analysis that addresses both theoretical foundations and practical applications.

Key aspects that require consideration include:
\\begin{{itemize}}
\\item Theoretical frameworks that underpin understanding of {key_concepts[0] if key_concepts else 'core concepts'}
\\item Empirical evidence from research and practice
\\item Methodological approaches for systematic investigation
\\item Practical implications for implementation and application
\\item Future directions and emerging trends
\\end{{itemize}}

\\subsection{{Detailed Analysis}}

The comprehensive analysis reveals several important dimensions that merit detailed examination. Each of these dimensions contributes to a holistic understanding of the topic and provides insights for both theoretical development and practical application.

\\subsubsection{{Primary Considerations}}

The primary considerations in this area focus on {key_concepts[0] if key_concepts else 'fundamental aspects'} and their relationship to broader {domain} objectives. Research has consistently demonstrated the importance of systematic approaches that address multiple factors simultaneously.

Evidence suggests that effective approaches must consider:
\\begin{{enumerate}}
\\item Contextual factors that influence outcomes and effectiveness
\\item Stakeholder perspectives and requirements
\\item Resource constraints and optimization opportunities
\\item Risk factors and mitigation strategies
\\item Performance metrics and evaluation criteria
\\end{{enumerate}}

\\subsubsection{{Implementation Strategies}}

Successful implementation requires careful planning and systematic execution. Best practices in the field emphasize the importance of evidence-based approaches that are tailored to specific contexts and requirements.

Key implementation strategies include:
\\begin{{itemize}}
\\item Comprehensive planning and preparation phases
\\item Stakeholder engagement and communication protocols
\\item Phased implementation with regular evaluation and adjustment
\\item Quality assurance and performance monitoring systems
\\item Continuous improvement and optimization processes
\\end{{itemize}}

\\subsection{{Implications and Future Directions}}

The analysis has significant implications for both current practice and future development in {domain}. The findings suggest several important directions for continued research and development.

Future research should focus on:
\\begin{{itemize}}
\\item Advanced methodological approaches for complex analysis
\\item Integration of emerging technologies and innovations
\\item Cross-disciplinary collaboration and knowledge sharing
\\item Long-term sustainability and scalability considerations
\\item Global perspectives and cross-cultural validation
\\end{{itemize}}

The continued evolution of {domain} will require ongoing attention to these critical areas and sustained commitment to evidence-based practice and continuous improvement."""
