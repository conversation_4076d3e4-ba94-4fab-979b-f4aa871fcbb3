"""
Document planning service for creating structured document outlines
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from services.document_classifier import DocumentClassifier

logger = logging.getLogger(__name__)

@dataclass
class SectionPlan:
    """Plan for a document section"""
    title: str
    description: str
    estimated_length: int  # in paragraphs
    content_type: str  # text, table, figure, code, etc.
    requirements: List[str]
    subsections: List['SectionPlan'] = None

@dataclass
class DocumentPlan:
    """Complete document plan"""
    title: str
    document_type: str
    estimated_pages: tuple
    sections: List[SectionPlan]
    latex_packages: List[str]
    style_requirements: Dict[str, str]
    special_requirements: List[str]
    file_references: List[Dict] = None

class DocumentPlanner:
    """Service for creating detailed document plans"""
    
    @staticmethod
    def create_document_plan(
        user_request: str,
        document_type: str = None,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> DocumentPlan:
        """
        Create a comprehensive document plan
        
        Args:
            user_request: User's description of the document
            document_type: Specific document type (auto-detected if None)
            referenced_files: Files referenced by the user
            extracted_content: Content extracted from referenced files
            
        Returns:
            DocumentPlan object with detailed structure
        """
        try:
            # Classify document type if not provided
            if not document_type:
                document_type = DocumentClassifier.classify_document_type(
                    user_request, referenced_files
                )
            
            # Get document specifications
            spec = DocumentClassifier.get_document_specification(document_type)
            requirements = DocumentClassifier.estimate_content_requirements(
                document_type, user_request
            )
            
            # Generate title
            title = DocumentPlanner._generate_title(user_request, document_type)
            
            # Create section plans
            sections = DocumentPlanner._create_section_plans(
                document_type, user_request, referenced_files, extracted_content
            )
            
            return DocumentPlan(
                title=title,
                document_type=document_type,
                estimated_pages=requirements['estimated_pages'],
                sections=sections,
                latex_packages=requirements['latex_packages'],
                style_requirements=requirements['style_requirements'],
                special_requirements=requirements['special_requirements'],
                file_references=referenced_files or []
            )
            
        except Exception as e:
            logger.error(f"Error creating document plan: {e}")
            # Return a basic plan as fallback
            return DocumentPlanner._create_fallback_plan(user_request, document_type)
    
    @staticmethod
    def _generate_title(user_request: str, document_type: str) -> str:
        """Generate an appropriate title for the document"""
        request_words = user_request.split()
        
        # Extract key concepts (simple approach)
        important_words = []
        skip_words = {'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        
        for word in request_words[:10]:  # First 10 words
            clean_word = word.strip('.,!?').lower()
            if clean_word not in skip_words and len(clean_word) > 2:
                important_words.append(word.strip('.,!?'))
        
        if len(important_words) >= 3:
            title = ' '.join(important_words[:5])
        else:
            title = user_request[:50] + "..." if len(user_request) > 50 else user_request
        
        # Add document type context
        type_suffixes = {
            'research_paper': 'Research Paper',
            'business_report': 'Analysis Report',
            'invoice': 'Invoice',
            'exam': 'Examination',
            'letter': 'Letter',
            'thesis': 'Thesis',
            'presentation': 'Presentation'
        }
        
        suffix = type_suffixes.get(document_type, '')
        if suffix and suffix.lower() not in title.lower():
            title = f"{title}: {suffix}"
        
        return title.title()
    
    @staticmethod
    def _create_section_plans(
        document_type: str,
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> List[SectionPlan]:
        """Create detailed plans for each section"""
        
        spec = DocumentClassifier.get_document_specification(document_type)
        sections = []
        
        # Section planning based on document type
        if document_type == 'research_paper':
            sections = DocumentPlanner._plan_research_paper_sections(
                user_request, referenced_files, extracted_content
            )
        elif document_type == 'business_report':
            sections = DocumentPlanner._plan_business_report_sections(
                user_request, referenced_files, extracted_content
            )
        elif document_type == 'invoice':
            sections = DocumentPlanner._plan_invoice_sections(
                user_request, referenced_files, extracted_content
            )
        elif document_type == 'exam':
            sections = DocumentPlanner._plan_exam_sections(
                user_request, referenced_files, extracted_content
            )
        elif document_type == 'letter':
            sections = DocumentPlanner._plan_letter_sections(
                user_request, referenced_files, extracted_content
            )
        else:
            # Generic planning
            sections = DocumentPlanner._plan_generic_sections(
                spec.sections, user_request, referenced_files, extracted_content
            )
        
        return sections
    
    @staticmethod
    def _plan_research_paper_sections(
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> List[SectionPlan]:
        """Plan sections for a research paper"""
        
        sections = [
            SectionPlan(
                title="Abstract",
                description="Concise summary of the research objectives, methods, results, and conclusions",
                estimated_length=1,
                content_type="text",
                requirements=["concise", "comprehensive_summary"]
            ),
            SectionPlan(
                title="Introduction",
                description="Background, problem statement, research questions, and objectives",
                estimated_length=3,
                content_type="text",
                requirements=["background_context", "problem_definition", "research_objectives"]
            ),
            SectionPlan(
                title="Literature Review",
                description="Review of existing research and theoretical framework",
                estimated_length=5,
                content_type="text",
                requirements=["citations", "critical_analysis", "research_gaps"]
            ),
            SectionPlan(
                title="Methodology",
                description="Research design, data collection, and analysis methods",
                estimated_length=3,
                content_type="text",
                requirements=["detailed_methods", "reproducible_procedures"]
            ),
            SectionPlan(
                title="Results",
                description="Presentation of findings with tables and figures",
                estimated_length=4,
                content_type="mixed",
                requirements=["data_presentation", "tables", "figures", "statistical_analysis"]
            ),
            SectionPlan(
                title="Discussion",
                description="Interpretation of results and implications",
                estimated_length=4,
                content_type="text",
                requirements=["result_interpretation", "limitations", "implications"]
            ),
            SectionPlan(
                title="Conclusion",
                description="Summary of findings and future research directions",
                estimated_length=2,
                content_type="text",
                requirements=["summary", "future_work"]
            ),
            SectionPlan(
                title="References",
                description="Bibliography of cited sources",
                estimated_length=1,
                content_type="bibliography",
                requirements=["proper_citations", "academic_format"]
            )
        ]
        
        # Adjust based on referenced files
        if referenced_files:
            for file_info in referenced_files:
                if file_info.get('type') in ['csv', 'xlsx']:
                    # Add data analysis emphasis
                    for section in sections:
                        if section.title == "Results":
                            section.requirements.append("data_analysis")
                            section.estimated_length += 2
        
        return sections
    
    @staticmethod
    def _plan_business_report_sections(
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> List[SectionPlan]:
        """Plan sections for a business report"""
        
        return [
            SectionPlan(
                title="Executive Summary",
                description="High-level overview of key findings and recommendations",
                estimated_length=2,
                content_type="text",
                requirements=["concise", "key_findings", "actionable_recommendations"]
            ),
            SectionPlan(
                title="Introduction",
                description="Purpose, scope, and methodology of the analysis",
                estimated_length=2,
                content_type="text",
                requirements=["business_context", "objectives"]
            ),
            SectionPlan(
                title="Analysis",
                description="Detailed analysis of data and business metrics",
                estimated_length=5,
                content_type="mixed",
                requirements=["data_analysis", "charts", "business_insights"]
            ),
            SectionPlan(
                title="Findings",
                description="Key discoveries and patterns identified",
                estimated_length=3,
                content_type="text",
                requirements=["clear_findings", "evidence_based"]
            ),
            SectionPlan(
                title="Recommendations",
                description="Actionable recommendations based on analysis",
                estimated_length=3,
                content_type="text",
                requirements=["actionable", "prioritized", "implementation_guidance"]
            ),
            SectionPlan(
                title="Conclusion",
                description="Summary and next steps",
                estimated_length=1,
                content_type="text",
                requirements=["summary", "next_steps"]
            )
        ]
    
    @staticmethod
    def _plan_invoice_sections(
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> List[SectionPlan]:
        """Plan sections for an invoice"""
        
        return [
            SectionPlan(
                title="Header",
                description="Company information and invoice details",
                estimated_length=1,
                content_type="header",
                requirements=["company_info", "invoice_number", "date"]
            ),
            SectionPlan(
                title="Client Information",
                description="Billing address and client details",
                estimated_length=1,
                content_type="text",
                requirements=["client_details", "billing_address"]
            ),
            SectionPlan(
                title="Services/Products",
                description="Itemized list of services or products",
                estimated_length=1,
                content_type="table",
                requirements=["itemized_list", "quantities", "rates", "amounts"]
            ),
            SectionPlan(
                title="Calculations",
                description="Subtotal, taxes, and total amount",
                estimated_length=1,
                content_type="table",
                requirements=["calculations", "tax_breakdown", "total"]
            ),
            SectionPlan(
                title="Payment Terms",
                description="Payment instructions and terms",
                estimated_length=1,
                content_type="text",
                requirements=["payment_methods", "due_date", "terms"]
            )
        ]
    
    @staticmethod
    def _plan_exam_sections(
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> List[SectionPlan]:
        """Plan sections for an exam"""
        
        return [
            SectionPlan(
                title="Instructions",
                description="Exam instructions and guidelines",
                estimated_length=1,
                content_type="text",
                requirements=["clear_instructions", "time_limits", "guidelines"]
            ),
            SectionPlan(
                title="Multiple Choice Questions",
                description="Multiple choice questions with options",
                estimated_length=2,
                content_type="questions",
                requirements=["clear_questions", "multiple_options", "single_correct_answer"]
            ),
            SectionPlan(
                title="Short Answer Questions",
                description="Brief response questions",
                estimated_length=2,
                content_type="questions",
                requirements=["concise_questions", "specific_answers"]
            ),
            SectionPlan(
                title="Essay Questions",
                description="Extended response questions",
                estimated_length=1,
                content_type="questions",
                requirements=["comprehensive_questions", "detailed_responses"]
            )
        ]
    
    @staticmethod
    def _plan_letter_sections(
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> List[SectionPlan]:
        """Plan sections for a letter"""
        
        return [
            SectionPlan(
                title="Header",
                description="Sender information and date",
                estimated_length=1,
                content_type="header",
                requirements=["sender_info", "date"]
            ),
            SectionPlan(
                title="Recipient",
                description="Recipient address and salutation",
                estimated_length=1,
                content_type="text",
                requirements=["recipient_address", "appropriate_salutation"]
            ),
            SectionPlan(
                title="Body",
                description="Main content of the letter",
                estimated_length=3,
                content_type="text",
                requirements=["clear_purpose", "appropriate_tone", "logical_flow"]
            ),
            SectionPlan(
                title="Closing",
                description="Closing remarks and signature",
                estimated_length=1,
                content_type="text",
                requirements=["appropriate_closing", "signature_line"]
            )
        ]
    
    @staticmethod
    def _plan_generic_sections(
        section_names: List[str],
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> List[SectionPlan]:
        """Create generic section plans"""
        
        sections = []
        for section_name in section_names:
            sections.append(
                SectionPlan(
                    title=section_name,
                    description=f"Content for {section_name.lower()} section",
                    estimated_length=2,
                    content_type="text",
                    requirements=["relevant_content"]
                )
            )
        
        return sections
    
    @staticmethod
    def _create_fallback_plan(user_request: str, document_type: str = None) -> DocumentPlan:
        """Create a basic fallback plan when detailed planning fails"""
        
        return DocumentPlan(
            title=f"Document: {user_request[:50]}",
            document_type=document_type or 'research_paper',
            estimated_pages=(5, 15),
            sections=[
                SectionPlan(
                    title="Introduction",
                    description="Introduction to the topic",
                    estimated_length=2,
                    content_type="text",
                    requirements=["introduction"]
                ),
                SectionPlan(
                    title="Main Content",
                    description="Main content of the document",
                    estimated_length=5,
                    content_type="text",
                    requirements=["main_content"]
                ),
                SectionPlan(
                    title="Conclusion",
                    description="Conclusion and summary",
                    estimated_length=1,
                    content_type="text",
                    requirements=["conclusion"]
                )
            ],
            latex_packages=['amsmath', 'graphicx'],
            style_requirements={'tone': 'formal', 'depth': 'moderate'},
            special_requirements=[],
            file_references=[]
        )
