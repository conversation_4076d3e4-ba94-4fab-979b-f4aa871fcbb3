"""
Supabase client configuration and initialization
"""

from supabase import create_client, Client
from flask import current_app, g
import logging

logger = logging.getLogger(__name__)

def init_supabase(app):
    """Initialize Supabase client with Flask app"""
    with app.app_context():
        try:
            url = app.config['SUPABASE_URL']
            key = app.config['SUPABASE_KEY']
            
            if not url or not key:
                raise ValueError("Supabase URL and KEY must be provided")
            
            # Create client and store in app config
            supabase_client = create_client(url, key)
            app.supabase = supabase_client
            
            logger.info("Supabase client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            raise

def get_service_supabase() -> Client:
    """Get Supabase client from Flask app context"""
    if 'supabase' not in g:
        g.supabase = current_app.supabase
    return g.supabase

def get_service_supabase() -> Client:
    """Get Supabase client with service role key for admin operations"""
    url = current_app.config['SUPABASE_URL']
    service_key = current_app.config['SUPABASE_SERVICE_ROLE_KEY']
    
    if not service_key:
        raise ValueError("Service role key not configured")
    
    return create_client(url, service_key)
