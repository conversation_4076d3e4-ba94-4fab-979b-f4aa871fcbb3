"""
Folder management service for organizing files and documents
"""

from services.supabase_client import get_service_supabase
import logging

logger = logging.getLogger(__name__)

class FolderService:
    
    @staticmethod
    def create_folder(user_id: str, name: str, parent_folder_id: str = None):
        """Create a new folder"""
        supabase = get_service_supabase()
        
        try:
            # Validate parent folder exists and belongs to user
            if parent_folder_id:
                parent_result = supabase.table('folders').select('id').eq('id', parent_folder_id).eq('user_id', user_id).execute()
                if not parent_result.data:
                    raise ValueError("Parent folder not found")
            
            folder_data = {
                'user_id': user_id,
                'name': name,
                'parent_folder_id': parent_folder_id
            }
            
            result = supabase.table('folders').insert(folder_data).execute()
            return result.data[0]
            
        except Exception as e:
            logger.error(f"Error creating folder: {e}")
            raise
    
    @staticmethod
    def get_user_folders(user_id: str, parent_folder_id: str = None):
        """Get folders for a user, optionally filtered by parent"""
        supabase = get_service_supabase()

        try:
            query = supabase.table('folders').select('*').eq('user_id', user_id)

            if parent_folder_id is not None:
                if parent_folder_id == 'all':
                    # Return all folders for the user (used for image lookup)
                    logger.info("Getting ALL folders for user (no parent filter)")
                    pass  # Don't add parent filter
                else:
                    query = query.eq('parent_folder_id', parent_folder_id)
            else:
                query = query.is_('parent_folder_id', 'null')

            result = query.order('name').execute()
            return result.data

        except Exception as e:
            logger.error(f"Error getting user folders: {e}")
            raise
    
    @staticmethod
    def get_folder(folder_id: str, user_id: str):
        """Get a specific folder"""
        supabase = get_service_supabase()
        
        try:
            result = supabase.table('folders').select('*').eq('id', folder_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error getting folder: {e}")
            raise
    
    @staticmethod
    def update_folder(folder_id: str, user_id: str, update_data: dict):
        """Update folder metadata"""
        supabase = get_service_supabase()
        
        try:
            # Remove fields that shouldn't be updated
            update_data.pop('id', None)
            update_data.pop('user_id', None)
            update_data.pop('created_at', None)
            
            # Validate parent folder if being updated
            if 'parent_folder_id' in update_data and update_data['parent_folder_id']:
                parent_result = supabase.table('folders').select('id').eq('id', update_data['parent_folder_id']).eq('user_id', user_id).execute()
                if not parent_result.data:
                    raise ValueError("Parent folder not found")
            
            result = supabase.table('folders').update(update_data).eq('id', folder_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error updating folder: {e}")
            raise
    
    @staticmethod
    def delete_folder(folder_id: str, user_id: str):
        """Delete a folder (must be empty)"""
        supabase = get_service_supabase()
        
        try:
            # Check if folder has subfolders
            subfolders = supabase.table('folders').select('id').eq('parent_folder_id', folder_id).eq('user_id', user_id).execute()
            if subfolders.data:
                raise ValueError("Cannot delete folder with subfolders")
            
            # Check if folder has files
            files = supabase.table('files').select('id').eq('folder_id', folder_id).eq('user_id', user_id).execute()
            if files.data:
                raise ValueError("Cannot delete folder with files")
            
            # Check if folder has documents
            documents = supabase.table('documents').select('id').eq('folder_id', folder_id).eq('user_id', user_id).execute()
            if documents.data:
                raise ValueError("Cannot delete folder with documents")
            
            # Delete folder
            result = supabase.table('folders').delete().eq('id', folder_id).eq('user_id', user_id).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Error deleting folder: {e}")
            raise
    
    @staticmethod
    def get_folder_contents(folder_id: str, user_id: str):
        """Get all contents of a folder (subfolders, files, documents)"""
        supabase = get_service_supabase()
        
        try:
            # Verify folder exists and belongs to user
            if folder_id:
                folder = FolderService.get_folder(folder_id, user_id)
                if not folder:
                    raise ValueError("Folder not found")
            
            # Get subfolders
            subfolders_query = supabase.table('folders').select('*').eq('user_id', user_id)
            if folder_id:
                subfolders_query = subfolders_query.eq('parent_folder_id', folder_id)
            else:
                subfolders_query = subfolders_query.is_('parent_folder_id', 'null')
            
            subfolders = subfolders_query.order('name').execute().data
            
            # Get files
            files_query = supabase.table('files').select('*').eq('user_id', user_id)
            if folder_id:
                files_query = files_query.eq('folder_id', folder_id)
            else:
                files_query = files_query.is_('folder_id', 'null')
            
            files = files_query.order('name').execute().data
            
            # Get documents
            documents_query = supabase.table('documents').select('*').eq('user_id', user_id)
            if folder_id:
                documents_query = documents_query.eq('folder_id', folder_id)
            else:
                documents_query = documents_query.is_('folder_id', 'null')
            
            documents = documents_query.order('title').execute().data
            
            return {
                'folder': FolderService.get_folder(folder_id, user_id) if folder_id else None,
                'subfolders': subfolders,
                'files': files,
                'documents': documents
            }
            
        except Exception as e:
            logger.error(f"Error getting folder contents: {e}")
            raise
    
    @staticmethod
    def get_folder_path(folder_id: str, user_id: str):
        """Get the full path to a folder"""
        supabase = get_service_supabase()

        try:
            path = []
            current_folder_id = folder_id
            visited = set()  # Prevent infinite loops

            while current_folder_id and current_folder_id not in visited:
                visited.add(current_folder_id)
                folder = FolderService.get_folder(current_folder_id, user_id)
                if not folder:
                    break

                path.insert(0, folder)
                current_folder_id = folder.get('parent_folder_id')

            return path

        except Exception as e:
            logger.error(f"Error getting folder path: {e}")
            raise
    
    @staticmethod
    def move_folder(folder_id: str, user_id: str, new_parent_id: str = None):
        """Move a folder to a new parent"""
        supabase = get_service_supabase()
        
        try:
            # Validate new parent exists and belongs to user
            if new_parent_id:
                parent_result = supabase.table('folders').select('id').eq('id', new_parent_id).eq('user_id', user_id).execute()
                if not parent_result.data:
                    raise ValueError("New parent folder not found")
                
                # Check for circular reference
                path = FolderService.get_folder_path(new_parent_id, user_id)
                if any(f['id'] == folder_id for f in path):
                    raise ValueError("Cannot move folder into its own subfolder")
            
            # Update folder
            result = supabase.table('folders').update({
                'parent_folder_id': new_parent_id
            }).eq('id', folder_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error moving folder: {e}")
            raise
