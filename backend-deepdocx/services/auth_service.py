"""
Authentication service for user management and OAuth
"""

import bcrypt
import uuid
import secrets
import requests
from datetime import datetime, timedelta
from flask import current_app
from flask_jwt_extended import create_access_token
from services.supabase_client import get_service_supabase, get_service_supabase
import logging

logger = logging.getLogger(__name__)

class AuthService:
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    @staticmethod
    def create_user(email: str, password: str, first_name: str, last_name: str, oauth_provider: str = 'email', google_id: str = None, github_id: str = None):
        """Create a new user"""
        supabase = get_service_supabase()  # Use service role for user creation
        
        try:
            # Check if user already exists
            existing_user = supabase.table('users').select('*').eq('email', email).execute()
            if existing_user.data:
                raise ValueError("User with this email already exists")
            
            # Hash password if provided (not for OAuth users)
            password_hash = None
            if password:
                password_hash = AuthService.hash_password(password)
            
            # Create user data
            user_data = {
                'email': email,
                'password_hash': password_hash,
                'first_name': first_name,
                'last_name': last_name,
                'oauth_provider': oauth_provider,
                'google_id': google_id,
                'github_id': github_id,
                'plan_type': 'free',
                'settings': {}
            }

            # Add email_verified only if the column exists (for now, consider all emails verified)
            # user_data['email_verified'] = True  # Temporarily disabled
            
            # Insert user
            result = supabase.table('users').insert(user_data).execute()
            user = result.data[0]
            
            # Generate JWT token
            token = create_access_token(identity=user['id'])
            
            # Remove sensitive data
            user.pop('password_hash', None)
            
            return {'user': user, 'token': token}
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise
    
    @staticmethod
    def authenticate_user(email: str, password: str):
        """Authenticate user with email and password"""
        supabase = get_service_supabase()  # Use service role for authentication
        
        try:
            # Get user by email
            result = supabase.table('users').select('*').eq('email', email).execute()
            
            if not result.data:
                raise ValueError("Invalid email or password")
            
            user = result.data[0]
            
            # Check if user has a password (not OAuth-only)
            if not user.get('password_hash'):
                raise ValueError("Please sign in with your OAuth provider")
            
            # Verify password
            if not AuthService.verify_password(password, user['password_hash']):
                raise ValueError("Invalid email or password")
            
            # Generate JWT token
            token = create_access_token(identity=user['id'])
            
            # Remove sensitive data
            user.pop('password_hash', None)
            
            return {'user': user, 'token': token}
            
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            raise
    
    @staticmethod
    def get_user_by_id(user_id: str):
        """Get user by ID"""
        supabase = get_service_supabase()  # Use service role for user queries
        
        try:
            result = supabase.table('users').select('*').eq('id', user_id).execute()
            
            if not result.data:
                return None
            
            user = result.data[0]
            user.pop('password_hash', None)  # Remove sensitive data
            
            return user
            
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            raise
    
    @staticmethod
    def update_user(user_id: str, update_data: dict):
        """Update user information"""
        supabase = get_service_supabase()  # Use service role for user updates
        
        try:
            # Remove sensitive fields that shouldn't be updated directly
            update_data.pop('id', None)
            update_data.pop('password_hash', None)
            update_data.pop('created_at', None)
            
            result = supabase.table('users').update(update_data).eq('id', user_id).execute()
            
            if not result.data:
                raise ValueError("User not found")
            
            user = result.data[0]
            user.pop('password_hash', None)
            
            return user
            
        except Exception as e:
            logger.error(f"Error updating user: {e}")
            raise
    
    @staticmethod
    def create_oauth_session(provider: str, redirect_url: str = None):
        """Create OAuth session for state management"""
        supabase = get_service_supabase()  # Use service role for OAuth sessions
        
        try:
            state = secrets.token_urlsafe(32)
            
            session_data = {
                'state': state,
                'provider': provider,
                'redirect_url': redirect_url,
                'expires_at': datetime.utcnow() + timedelta(minutes=10)
            }
            
            supabase.table('oauth_sessions').insert(session_data).execute()
            
            return state
            
        except Exception as e:
            logger.error(f"Error creating OAuth session: {e}")
            raise
    
    @staticmethod
    def verify_oauth_session(state: str, provider: str):
        """Verify OAuth session state"""
        supabase = get_service_supabase()  # Use service role for OAuth verification
        
        try:
            result = supabase.table('oauth_sessions').select('*').eq('state', state).eq('provider', provider).execute()
            
            if not result.data:
                return False
            
            session = result.data[0]
            
            # Check if session is expired
            expires_at = datetime.fromisoformat(session['expires_at'].replace('Z', '+00:00'))
            if datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at:
                return False
            
            # Delete used session
            supabase.table('oauth_sessions').delete().eq('state', state).execute()
            
            return True
            
        except Exception as e:
            logger.error(f"Error verifying OAuth session: {e}")
            return False
    
    @staticmethod
    def get_google_user_info(access_token: str):
        """Get user info from Google OAuth"""
        try:
            response = requests.get(
                'https://www.googleapis.com/oauth2/v2/userinfo',
                headers={'Authorization': f'Bearer {access_token}'}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting Google user info: {e}")
            raise
    
    @staticmethod
    def get_github_user_info(access_token: str):
        """Get user info from GitHub OAuth"""
        try:
            response = requests.get(
                'https://api.github.com/user',
                headers={'Authorization': f'token {access_token}'}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting GitHub user info: {e}")
            raise
    
    @staticmethod
    def find_or_create_oauth_user(provider: str, oauth_id: str, email: str, first_name: str, last_name: str):
        """Find existing OAuth user or create new one"""
        supabase = get_service_supabase()  # Use service role for OAuth user management
        
        try:
            # Try to find user by OAuth ID
            id_field = f'{provider}_id'
            result = supabase.table('users').select('*').eq(id_field, oauth_id).execute()
            
            if result.data:
                user = result.data[0]
                user.pop('password_hash', None)
                token = create_access_token(identity=user['id'])
                return {'user': user, 'token': token}
            
            # Try to find user by email
            result = supabase.table('users').select('*').eq('email', email).execute()
            
            if result.data:
                # Update existing user with OAuth ID
                user = result.data[0]
                update_data = {id_field: oauth_id, 'oauth_provider': provider}
                
                supabase.table('users').update(update_data).eq('id', user['id']).execute()
                user.update(update_data)
                user.pop('password_hash', None)
                
                token = create_access_token(identity=user['id'])
                return {'user': user, 'token': token}
            
            # Create new user
            oauth_data = {provider + '_id': oauth_id}
            return AuthService.create_user(
                email=email,
                password=None,
                first_name=first_name,
                last_name=last_name,
                oauth_provider=provider,
                **oauth_data
            )
            
        except Exception as e:
            logger.error(f"Error finding/creating OAuth user: {e}")
            raise
