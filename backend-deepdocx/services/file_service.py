"""
File management service for uploads and storage using Supabase Storage
"""

import os
import uuid
import tempfile
from werkzeug.utils import secure_filename
from flask import current_app
from services.supabase_client import get_service_supabase
from services.file_extraction_service import FileExtractionService
import logging

logger = logging.getLogger(__name__)

class FileService:

    ALLOWED_EXTENSIONS = {
        'pdf', 'png', 'jpg', 'jpeg', 'gif', 'tex', 'bib', 'txt', 'md', 'doc', 'docx', 'csv', 'xlsx', 'xls'
    }

    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

    @staticmethod
    def allowed_file(filename):
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in FileService.ALLOWED_EXTENSIONS

    @staticmethod
    def _get_content_type(file_extension):
        """Get MIME type for file extension"""
        content_types = {
            'pdf': 'application/pdf',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'tex': 'text/plain',
            'bib': 'text/plain',
            'txt': 'text/plain',
            'md': 'text/markdown',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'csv': 'text/csv',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'xls': 'application/vnd.ms-excel'
        }
        return content_types.get(file_extension.lower(), 'application/octet-stream')

    @staticmethod
    def upload_file(file, user_id: str, folder_id: str = None):
        """Upload a file to Supabase Storage and store metadata"""
        supabase = get_service_supabase()

        try:
            if not file or file.filename == '':
                raise ValueError("No file provided")

            if not FileService.allowed_file(file.filename):
                raise ValueError("File type not allowed")

            # Check file size
            file.seek(0, 2)  # Seek to end
            file_size = file.tell()
            file.seek(0)  # Reset to beginning

            if file_size > FileService.MAX_FILE_SIZE:
                raise ValueError(f"File too large. Maximum size is {FileService.MAX_FILE_SIZE // (1024*1024)}MB")

            # Generate secure filename
            original_filename = secure_filename(file.filename)
            file_extension = original_filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4()}.{file_extension}"

            # Create storage path: users/{user_id}/files/{unique_filename}
            storage_path = f"users/{user_id}/files/{unique_filename}"

            # Read file data
            file_data = file.read()
            file.seek(0)  # Reset for potential re-use

            # Upload to Supabase Storage
            try:
                result = supabase.storage.from_('files').upload(
                    storage_path,
                    file_data,
                    file_options={
                        'content-type': FileService._get_content_type(file_extension),
                        'cache-control': '3600'
                    }
                )

                # Check if upload was successful
                if hasattr(result, 'error') and result.error:
                    logger.error(f"Error uploading file to Supabase Storage: {result.error}")
                    raise Exception(f"Failed to upload file: {result.error}")

            except Exception as upload_error:
                logger.error(f"Error uploading file to Supabase Storage: {upload_error}")
                raise Exception(f"Failed to upload file: {upload_error}")

            # Get public URL
            try:
                public_url_result = supabase.storage.from_('files').get_public_url(storage_path)
                if hasattr(public_url_result, 'data') and public_url_result.data:
                    file_url = public_url_result.data.public_url
                else:
                    # Fallback: construct URL manually
                    file_url = f"{supabase.supabase_url}/storage/v1/object/public/files/{storage_path}"

                if not file_url:
                    raise Exception("Failed to get public URL for uploaded file")

            except Exception as url_error:
                logger.warning(f"Error getting public URL, using fallback: {url_error}")
                # Fallback: construct URL manually
                file_url = f"{supabase.supabase_url}/storage/v1/object/public/files/{storage_path}"

            # Extract content from file if it's a supported type
            extracted_content = None
            content_summary = None

            if file_extension in ['pdf', 'docx', 'csv', 'xlsx', 'xls', 'txt', 'md']:
                try:
                    # Create temporary file for content extraction
                    with tempfile.NamedTemporaryFile(suffix=f'.{file_extension}', delete=False) as temp_file:
                        temp_file.write(file_data)
                        temp_file_path = temp_file.name

                    try:
                        extraction_result = FileExtractionService.extract_file_content(temp_file_path, file_extension)
                        if extraction_result['success']:
                            extracted_content = extraction_result['content']
                            content_summary = extracted_content[:500] + "..." if len(extracted_content) > 500 else extracted_content
                            logger.info(f"Successfully extracted content from {original_filename}")
                        else:
                            logger.warning(f"Failed to extract content from {original_filename}: {extraction_result['error']}")
                    finally:
                        # Clean up temporary file
                        if os.path.exists(temp_file_path):
                            os.unlink(temp_file_path)

                except Exception as e:
                    logger.error(f"Error during content extraction for {original_filename}: {e}")

            # Store file metadata in database
            file_metadata = {
                'user_id': user_id,
                'folder_id': folder_id,
                'name': original_filename,
                'type': file_extension,
                'size': file_size,
                'url': file_url,
                'storage_path': storage_path,
                'metadata': {
                    'original_name': original_filename,
                    'unique_name': unique_filename,
                    'storage_bucket': 'files',
                    'extracted_content': extracted_content,
                    'content_summary': content_summary,
                    'content_extracted': extracted_content is not None
                }
            }

            result = supabase.table('files').insert(file_metadata).execute()
            file_record = result.data[0]

            logger.info(f"Successfully uploaded file {original_filename} to Supabase Storage: {file_url}")
            return file_record

        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            # Clean up from Supabase Storage if database insert failed
            if 'storage_path' in locals():
                try:
                    supabase.storage.from_('files').remove([storage_path])
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup file from storage: {cleanup_error}")
            raise
    
    @staticmethod
    def get_user_files(user_id: str, folder_id: str = None):
        """Get all files for a user, optionally filtered by folder"""
        supabase = get_service_supabase()

        try:
            query = supabase.table('files').select('*').eq('user_id', user_id)

            if folder_id:
                query = query.eq('folder_id', folder_id)
            elif folder_id == 'root':
                # Explicitly request root folder files only
                query = query.is_('folder_id', 'null')
            # If folder_id is None, return ALL files for the user (no folder filter)

            result = query.order('created_at', desc=True).execute()
            return result.data

        except Exception as e:
            logger.error(f"Error getting user files: {e}")
            raise
    
    @staticmethod
    def get_file(file_id: str, user_id: str):
        """Get a specific file"""
        supabase = get_service_supabase()
        
        try:
            result = supabase.table('files').select('*').eq('id', file_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error getting file: {e}")
            raise
    
    @staticmethod
    def update_file(file_id: str, user_id: str, update_data: dict):
        """Update file metadata"""
        supabase = get_service_supabase()
        
        try:
            # Remove fields that shouldn't be updated
            update_data.pop('id', None)
            update_data.pop('user_id', None)
            update_data.pop('url', None)
            update_data.pop('storage_path', None)
            update_data.pop('created_at', None)
            
            result = supabase.table('files').update(update_data).eq('id', file_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error updating file: {e}")
            raise
    
    @staticmethod
    def delete_file(file_id: str, user_id: str):
        """Delete a file from Supabase Storage and database"""
        supabase = get_service_supabase()

        try:
            # Get file info first
            file_info = FileService.get_file(file_id, user_id)
            if not file_info:
                return False

            # Delete from Supabase Storage
            storage_path = file_info.get('storage_path')
            if storage_path:
                try:
                    supabase.storage.from_('files').remove([storage_path])
                    logger.info(f"Successfully deleted file from storage: {storage_path}")
                except Exception as storage_error:
                    logger.warning(f"Failed to delete file from storage: {storage_error}")

            # Delete from database
            result = supabase.table('files').delete().eq('id', file_id).eq('user_id', user_id).execute()

            return len(result.data) > 0

        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            raise

    @staticmethod
    def get_file_content(file_id: str, user_id: str):
        """Get extracted content for a specific file"""
        supabase = get_service_supabase()

        try:
            result = supabase.table('files').select('*').eq('id', file_id).eq('user_id', user_id).execute()

            if not result.data:
                return None

            file_record = result.data[0]
            metadata = file_record.get('metadata', {})

            return {
                'file_name': file_record['name'],
                'file_type': file_record['type'],
                'extracted_content': metadata.get('extracted_content'),
                'content_summary': metadata.get('content_summary'),
                'content_available': metadata.get('content_extracted', False)
            }

        except Exception as e:
            logger.error(f"Error getting file content: {e}")
            raise

    @staticmethod
    def get_files_with_content(user_id: str, folder_id: str = None):
        """Get files that have extracted content available"""
        supabase = get_service_supabase()

        try:
            query = supabase.table('files').select('*').eq('user_id', user_id)

            if folder_id:
                query = query.eq('folder_id', folder_id)
            elif folder_id == 'root':
                # Explicitly request root folder files only
                query = query.is_('folder_id', 'null')
            # If folder_id is None, return ALL files for the user (no folder filter)

            result = query.order('created_at', desc=True).execute()

            # Filter files that have extracted content
            files_with_content = []
            for file_record in result.data:
                metadata = file_record.get('metadata', {})
                if metadata.get('content_extracted', False):
                    files_with_content.append({
                        'id': file_record['id'],
                        'name': file_record['name'],
                        'type': file_record['type'],
                        'size': file_record['size'],
                        'content_summary': metadata.get('content_summary'),
                        'created_at': file_record['created_at']
                    })

            return files_with_content

        except Exception as e:
            logger.error(f"Error getting files with content: {e}")
            raise
    
    @staticmethod
    def get_file_content_from_storage(file_id: str, user_id: str):
        """Get file content for text files from Supabase Storage"""
        supabase = get_service_supabase()

        try:
            file_info = FileService.get_file(file_id, user_id)
            if not file_info:
                return None

            storage_path = file_info.get('storage_path')
            if not storage_path:
                return None

            # Only read text files
            text_extensions = {'txt', 'md', 'tex', 'bib'}
            if file_info['type'].lower() not in text_extensions:
                return None

            # Download file content from Supabase Storage
            result = supabase.storage.from_('files').download(storage_path)
            if hasattr(result, 'error') and result.error:
                logger.error(f"Error downloading file from storage: {result.error}")
                return None

            # Handle different response formats
            if hasattr(result, 'data'):
                file_data = result.data
            else:
                file_data = result  # Direct bytes response

            content = file_data.decode('utf-8')

            return {
                'file_info': file_info,
                'content': content
            }

        except Exception as e:
            logger.error(f"Error getting file content: {e}")
            raise

    @staticmethod
    def search_files(user_id: str, query: str, file_type: str = None):
        """Search files by name and optionally by type"""
        supabase = get_service_supabase()

        try:
            # Build search query
            search_query = supabase.table('files').select('*').eq('user_id', user_id)

            # Add name search (case-insensitive)
            search_query = search_query.ilike('name', f'%{query}%')

            # Add file type filter if specified
            if file_type:
                search_query = search_query.eq('type', file_type)

            result = search_query.order('created_at', desc=True).execute()
            return result.data

        except Exception as e:
            logger.error(f"Error searching files: {e}")
            raise
