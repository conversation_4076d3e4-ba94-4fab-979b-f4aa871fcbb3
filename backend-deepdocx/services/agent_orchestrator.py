"""
Agent orchestrator for managing multi-agent document creation workflow
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from services.document_planner import DocumentPlanner, DocumentPlan
from services.document_classifier import DocumentClassifier
from services.file_extraction_service import FileExtractionService
import json

logger = logging.getLogger(__name__)

class AgentState(Enum):
    """States for the agent workflow"""
    PLANNING = "planning"
    CLARIFYING = "clarifying"
    WRITING = "writing"
    REVIEWING = "reviewing"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class AgentContext:
    """Context shared between agents"""
    user_request: str
    document_plan: Optional[DocumentPlan] = None
    referenced_files: List[Dict] = None
    extracted_content: Dict[str, str] = None
    clarification_questions: List[str] = None
    user_responses: Dict[str, str] = None
    current_latex: str = ""
    state: AgentState = AgentState.PLANNING
    progress: float = 0.0
    error_message: str = ""

class AgentOrchestrator:
    """Orchestrates multiple AI agents for document creation"""
    
    def __init__(self):
        self.context = None
        self.agents = {
            'planner': PlannerAgent(),
            'clarifier': ClarificationAgent(),
            'writer': WriterAgent(),
            'reviewer': ReviewerAgent(),
            'intent_analyzer': IntentAnalyzerAgent()
        }
    
    def process_document_request(
        self,
        user_request: str,
        referenced_files: List[Dict] = None,
        user_responses: Dict[str, str] = None,
        current_context: AgentContext = None,
        existing_latex: str = None,
        conversation_history: List[Dict] = None
    ) -> Dict[str, Any]:
        """
        Process a document creation request through the agent workflow

        Args:
            user_request: User's document request
            referenced_files: Files referenced by the user
            user_responses: Responses to clarification questions
            current_context: Existing context for continuing workflow
            existing_latex: Current LaTeX code for document updates
            conversation_history: Previous conversation messages

        Returns:
            Dictionary with workflow results and next steps
        """
        try:
            # Initialize or update context
            if current_context:
                self.context = current_context
                if user_responses:
                    self.context.user_responses = user_responses
                # Update with existing LaTeX if provided
                if existing_latex:
                    self.context.current_latex = existing_latex
            else:
                self.context = AgentContext(
                    user_request=user_request,
                    referenced_files=referenced_files or [],
                    user_responses=user_responses or {},
                    current_latex=existing_latex or ""
                )
            
            # Extract content from referenced files
            print(f"=== AGENT ORCHESTRATOR DEBUG ===")
            print(f"Referenced files: {self.context.referenced_files}")
            print(f"Referenced files type: {type(self.context.referenced_files)}")
            if self.context.referenced_files:
                print(f"First referenced file: {self.context.referenced_files[0] if self.context.referenced_files else 'None'}")
                print(f"First referenced file type: {type(self.context.referenced_files[0]) if self.context.referenced_files else 'None'}")

            if self.context.referenced_files and not self.context.extracted_content:
                self.context.extracted_content = self._extract_file_contents()
            
            # For existing documents, analyze intent first
            if self.context.current_latex and not current_context:
                return self._execute_intent_analysis_phase(conversation_history)

            # Execute workflow based on current state
            if self.context.state == AgentState.PLANNING:
                return self._execute_planning_phase()
            elif self.context.state == AgentState.CLARIFYING:
                return self._execute_clarification_phase()
            elif self.context.state == AgentState.WRITING:
                return self._execute_writing_phase()
            elif self.context.state == AgentState.REVIEWING:
                return self._execute_review_phase()
            else:
                return self._handle_error("Invalid agent state")
                
        except Exception as e:
            logger.error(f"Error in agent orchestrator: {e}")
            return self._handle_error(str(e))
    
    def _extract_file_contents(self) -> Dict[str, str]:
        """Extract content from all referenced files"""
        print(f"=== EXTRACTING FILE CONTENTS ===")
        print(f"Referenced files: {self.context.referenced_files}")

        extracted_content = {}

        # Check if referenced_files contains file IDs (strings) or file objects (dicts)
        if self.context.referenced_files and isinstance(self.context.referenced_files[0], str):
            print("Referenced files are file IDs, need to resolve to file objects")
            # Convert file IDs to file objects
            file_objects = self._resolve_file_ids_to_objects(self.context.referenced_files)
            print(f"Resolved file objects: {file_objects}")
        else:
            print("Referenced files are already file objects")
            file_objects = self.context.referenced_files

        for file_info in file_objects:
            try:
                print(f"Processing file: {file_info}")
                file_path = file_info.get('storage_path')
                file_type = file_info.get('type')

                print(f"File path: {file_path}, File type: {file_type}")

                if file_path and file_type:
                    extraction_result = FileExtractionService.extract_file_content(
                        file_path, file_type
                    )

                    print(f"Extraction result: {extraction_result}")

                    if extraction_result['success']:
                        file_key = file_info.get('name', file_path)
                        extracted_content[file_key] = {
                            'content': extraction_result['content'],
                            'metadata': extraction_result['metadata'],
                            'summary': FileExtractionService.get_file_summary(extraction_result)
                        }
                        print(f"Successfully extracted content for {file_key}")
                    else:
                        logger.warning(f"Failed to extract content from {file_path}: {extraction_result['error']}")

            except Exception as e:
                logger.error(f"Error extracting file content: {e}")

        print(f"Final extracted content: {extracted_content}")
        return extracted_content

    def _resolve_file_ids_to_objects(self, file_ids: List[str]) -> List[Dict]:
        """Resolve file IDs to file objects with metadata"""
        from services.supabase_client import get_service_supabase

        file_objects = []
        for file_id in file_ids:
            try:
                # Get file metadata from database
                supabase = get_service_supabase()
                print(f"Querying database for file ID: {file_id}")
                result = supabase.table('files').select('*').eq('id', file_id).execute()

                print(f"Database query result: {result}")
                print(f"Result data: {result.data}")

                if result.data:
                    file_record = result.data[0]
                    file_objects.append({
                        'id': file_record['id'],
                        'name': file_record['name'],
                        'type': file_record['type'],
                        'storage_path': file_record['storage_path'],
                        'metadata': file_record.get('metadata', {})
                    })
                    print(f"Resolved file ID {file_id} to {file_record['name']}")
                else:
                    print(f"File not found for ID: {file_id}")
                    # Let's also try to see what files are actually in the database
                    all_files_result = supabase.table('files').select('id, name, storage_path').execute()
                    print(f"All files in database: {all_files_result.data}")

            except Exception as e:
                logger.error(f"Error resolving file ID {file_id}: {e}")
                print(f"Exception details: {e}")

        return file_objects
    
    def _execute_planning_phase(self) -> Dict[str, Any]:
        """Execute the planning phase"""
        try:
            # Create document plan
            self.context.document_plan = self.agents['planner'].create_plan(
                self.context.user_request,
                self.context.referenced_files,
                self.context.extracted_content
            )
            
            # Check if clarification is needed
            clarification_questions = self.agents['clarifier'].identify_clarification_needs(
                self.context.document_plan,
                self.context.user_request,
                self.context.extracted_content
            )
            
            if clarification_questions:
                self.context.clarification_questions = clarification_questions
                self.context.state = AgentState.CLARIFYING
                self.context.progress = 0.2
                
                return {
                    'status': 'clarification_needed',
                    'questions': clarification_questions,
                    'document_plan': self._serialize_document_plan(),
                    'progress': self.context.progress,
                    'context': self.context
                }
            else:
                # Proceed directly to writing
                self.context.state = AgentState.WRITING
                return self._execute_writing_phase()
                
        except Exception as e:
            return self._handle_error(f"Planning phase error: {e}")
    
    def _execute_clarification_phase(self) -> Dict[str, Any]:
        """Execute the clarification phase"""
        try:
            # Check if we have all required responses
            missing_responses = self.agents['clarifier'].check_missing_responses(
                self.context.clarification_questions,
                self.context.user_responses
            )
            
            if missing_responses:
                return {
                    'status': 'awaiting_responses',
                    'missing_questions': missing_responses,
                    'progress': self.context.progress,
                    'context': self.context
                }
            
            # Update document plan with user responses
            self.context.document_plan = self.agents['clarifier'].update_plan_with_responses(
                self.context.document_plan,
                self.context.user_responses
            )
            
            # Proceed to writing
            self.context.state = AgentState.WRITING
            self.context.progress = 0.4
            
            return self._execute_writing_phase()
            
        except Exception as e:
            return self._handle_error(f"Clarification phase error: {e}")
    
    def _execute_writing_phase(self) -> Dict[str, Any]:
        """Execute the writing phase"""
        try:
            # Generate LaTeX document
            latex_result = self.agents['writer'].generate_document(
                self.context.document_plan,
                self.context.extracted_content,
                self.context.user_responses
            )
            
            self.context.current_latex = latex_result['latex_code']
            self.context.state = AgentState.REVIEWING
            self.context.progress = 0.8
            
            return self._execute_review_phase()
            
        except Exception as e:
            return self._handle_error(f"Writing phase error: {e}")
    
    def _execute_review_phase(self) -> Dict[str, Any]:
        """Execute the review phase"""
        try:
            # Review and potentially improve the document
            review_result = self.agents['reviewer'].review_document(
                self.context.current_latex,
                self.context.document_plan
            )
            
            if review_result['needs_improvement']:
                # Apply improvements
                self.context.current_latex = review_result['improved_latex']
            
            self.context.state = AgentState.COMPLETED
            self.context.progress = 1.0
            
            return {
                'status': 'completed',
                'latex_code': self.context.current_latex,
                'document_title': self.context.document_plan.title,
                'document_type': self.context.document_plan.document_type,
                'review_notes': review_result.get('notes', []),
                'progress': self.context.progress,
                'context': self.context
            }
            
        except Exception as e:
            return self._handle_error(f"Review phase error: {e}")

    def _execute_intent_analysis_phase(self, conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """Execute intent analysis for existing document modifications"""
        try:
            # Analyze user intent
            intent_result = self.agents['intent_analyzer'].analyze_intent(
                self.context.user_request,
                self.context.current_latex,
                conversation_history
            )

            # Based on intent, decide how to proceed
            if intent_result['intent'] == 'rewrite':
                # Complete rewrite - go through full planning process
                self.context.state = AgentState.PLANNING
                return self._execute_planning_phase()

            elif intent_result['intent'] in ['add', 'modify', 'replace']:
                # Targeted modifications - go directly to intelligent writing
                return self._execute_intelligent_modification(intent_result)

            else:
                # Default to modification
                return self._execute_intelligent_modification(intent_result)

        except Exception as e:
            return self._handle_error(f"Intent analysis phase error: {e}")

    def _execute_intelligent_modification(self, intent_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute intelligent document modification based on intent"""
        try:
            # Create a specialized writer for modifications
            modified_latex = self.agents['writer'].modify_existing_document(
                current_latex=self.context.current_latex,
                user_request=self.context.user_request,
                intent_result=intent_result,
                extracted_content=self.context.extracted_content
            )

            self.context.current_latex = modified_latex
            self.context.state = AgentState.COMPLETED
            self.context.progress = 1.0

            return {
                'status': 'completed',
                'latex_code': self.context.current_latex,
                'document_title': None,  # Keep existing title
                'document_type': 'modification',
                'modification_intent': intent_result,
                'progress': self.context.progress,
                'context': self.context
            }

        except Exception as e:
            return self._handle_error(f"Intelligent modification error: {e}")
    
    def _handle_error(self, error_message: str) -> Dict[str, Any]:
        """Handle errors in the workflow"""
        self.context.state = AgentState.ERROR
        self.context.error_message = error_message
        
        return {
            'status': 'error',
            'error': error_message,
            'progress': self.context.progress,
            'context': self.context
        }
    
    def _serialize_document_plan(self) -> Dict[str, Any]:
        """Serialize document plan for JSON response"""
        if not self.context.document_plan:
            return {}
        
        plan = self.context.document_plan
        return {
            'title': plan.title,
            'document_type': plan.document_type,
            'estimated_pages': plan.estimated_pages,
            'sections': [
                {
                    'title': section.title,
                    'description': section.description,
                    'estimated_length': section.estimated_length,
                    'content_type': section.content_type,
                    'requirements': section.requirements
                }
                for section in plan.sections
            ],
            'special_requirements': plan.special_requirements
        }

class PlannerAgent:
    """Agent responsible for document planning"""
    
    def create_plan(
        self,
        user_request: str,
        referenced_files: List[Dict] = None,
        extracted_content: Dict[str, str] = None
    ) -> DocumentPlan:
        """Create a document plan"""
        return DocumentPlanner.create_document_plan(
            user_request=user_request,
            referenced_files=referenced_files,
            extracted_content=extracted_content
        )

class ClarificationAgent:
    """Agent responsible for identifying clarification needs"""
    
    def identify_clarification_needs(
        self,
        document_plan: DocumentPlan,
        user_request: str,
        extracted_content: Dict[str, str] = None
    ) -> List[str]:
        """Identify what clarifications are needed from the user"""
        questions = []
        
        # Check for ambiguous requirements
        if 'detailed' not in user_request.lower() and 'brief' not in user_request.lower():
            if document_plan.document_type in ['research_paper', 'business_report']:
                questions.append(
                    f"How detailed should this {document_plan.document_type.replace('_', ' ')} be? "
                    f"(Brief overview, moderate detail, or comprehensive analysis?)"
                )
        
        # Check for missing context
        if document_plan.document_type == 'invoice':
            if not any('client' in req.lower() for req in user_request.split()):
                questions.append("What are the client details (name, address, contact information)?")
            if not any('service' in req.lower() or 'product' in req.lower() for req in user_request.split()):
                questions.append("What services or products should be included in the invoice?")
        
        # Check for technical requirements
        if 'mathematical' in user_request.lower() or 'equation' in user_request.lower():
            questions.append("What specific mathematical concepts or equations should be included?")
        
        # Check file-related questions
        if extracted_content:
            for file_name, content_info in extracted_content.items():
                if content_info['metadata'].get('has_tables'):
                    questions.append(f"How should the data from {file_name} be incorporated into the document?")
        
        return questions[:3]  # Limit to 3 questions to avoid overwhelming user
    
    def check_missing_responses(
        self,
        questions: List[str],
        responses: Dict[str, str]
    ) -> List[str]:
        """Check which questions still need responses"""
        if not responses:
            return questions
        
        # Simple check - in a real implementation, you'd match questions to responses more intelligently
        answered_count = len(responses)
        if answered_count >= len(questions):
            return []
        
        return questions[answered_count:]
    
    def update_plan_with_responses(
        self,
        document_plan: DocumentPlan,
        user_responses: Dict[str, str]
    ) -> DocumentPlan:
        """Update the document plan based on user responses"""
        # This is a simplified implementation
        # In practice, you'd parse responses and update the plan accordingly
        
        for response in user_responses.values():
            if 'detailed' in response.lower() or 'comprehensive' in response.lower():
                # Increase estimated length
                min_pages, max_pages = document_plan.estimated_pages
                document_plan.estimated_pages = (min_pages + 2, max_pages + 5)
            elif 'brief' in response.lower() or 'short' in response.lower():
                # Decrease estimated length
                min_pages, max_pages = document_plan.estimated_pages
                document_plan.estimated_pages = (max(1, min_pages - 2), max(2, max_pages - 3))
        
        return document_plan

class WriterAgent:
    """Agent responsible for document writing with comprehensive content generation"""

    def __init__(self):
        # Import here to avoid circular imports
        from services.comprehensive_writers import (
            ResearchPaperWriter, BusinessReportWriter, BaseWriter
        )

        self.specialized_writers = {
            'research_paper': ResearchPaperWriter(),
            'business_report': BusinessReportWriter(),
            'invoice': BaseWriter(),  # Use base writer for simpler documents
            'exam': BaseWriter(),
            'thesis': ResearchPaperWriter(),  # Thesis uses research paper writer
            'letter': BaseWriter()
        }

    def generate_document(
        self,
        document_plan: DocumentPlan,
        extracted_content: Dict[str, str] = None,
        user_responses: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive LaTeX document with detailed content"""

        # Get specialized writer for document type
        writer = self.specialized_writers.get(
            document_plan.document_type,
            self.specialized_writers['research_paper']
        )

        # Generate comprehensive content
        latex_code = writer.generate_comprehensive_document(
            document_plan, extracted_content, user_responses
        )

        return {
            'latex_code': latex_code,
            'sections_generated': len(document_plan.sections),
            'estimated_pages': document_plan.estimated_pages,
            'content_depth': 'comprehensive',
            'word_count_estimate': len(latex_code.split()) * 0.7  # Approximate word count
        }

    def modify_existing_document(
        self,
        current_latex: str,
        user_request: str,
        intent_result: Dict[str, Any],
        extracted_content: Dict[str, str] = None
    ) -> str:
        """Intelligently modify existing LaTeX document based on user intent"""
        try:
            from services.ai_service import AIService
            client = AIService._get_openai_client()

            # Build context about the modification
            intent = intent_result['intent']
            target_sections = intent_result.get('target_sections', [])
            modification_type = intent_result.get('modification_type', 'content')
            scope = intent_result.get('scope', 'specific')

            # Build file context if available
            file_context = ""
            if extracted_content:
                file_context = "\n\nExtracted content from referenced files:\n"
                for filename, content in extracted_content.items():
                    file_context += f"\n{filename}:\n{content[:500]}...\n"

            # Create intelligent prompt based on intent
            if intent == 'add':
                prompt = f"""
                Add new content to this LaTeX document based on the user's request.

                Current LaTeX document:
                {current_latex}

                User request: "{user_request}"

                Intent analysis:
                - Action: Add new content
                - Target sections: {', '.join(target_sections) if target_sections else 'Determine appropriate location'}
                - Modification type: {modification_type}
                - Scope: {scope}

                {file_context}

                Instructions:
                1. PRESERVE all existing content and structure
                2. Add the requested content in the most appropriate location
                3. If adding new sections, place them logically within the document structure
                4. If adding content to existing sections, integrate it naturally
                5. Maintain consistent formatting and style
                6. Ensure the document remains compilable
                7. Add necessary packages if new features are used

                Return the complete modified LaTeX document.
                """

            elif intent == 'modify':
                prompt = f"""
                Modify existing content in this LaTeX document based on the user's request.

                Current LaTeX document:
                {current_latex}

                User request: "{user_request}"

                Intent analysis:
                - Action: Modify existing content
                - Target sections: {', '.join(target_sections) if target_sections else 'Determine from context'}
                - Modification type: {modification_type}
                - Scope: {scope}

                {file_context}

                Instructions:
                1. Keep the overall document structure intact
                2. Modify only the content that needs to be changed based on the request
                3. Preserve other sections and content unchanged
                4. Maintain consistent formatting and style
                5. Ensure the document remains compilable
                6. If the request is vague, make reasonable improvements to relevant sections

                Return the complete modified LaTeX document.
                """

            elif intent == 'replace':
                prompt = f"""
                Replace specific sections or content in this LaTeX document based on the user's request.

                Current LaTeX document:
                {current_latex}

                User request: "{user_request}"

                Intent analysis:
                - Action: Replace specific content
                - Target sections: {', '.join(target_sections) if target_sections else 'Determine from context'}
                - Modification type: {modification_type}
                - Scope: {scope}

                {file_context}

                Instructions:
                1. Identify the sections or content to be replaced
                2. Replace them with new content based on the user's request
                3. Keep all other sections and content unchanged
                4. Maintain the document structure and formatting
                5. Ensure the document remains compilable
                6. Make the replacement content comprehensive and well-written

                Return the complete modified LaTeX document.
                """

            else:  # Default modification
                prompt = f"""
                Intelligently modify this LaTeX document based on the user's request.

                Current LaTeX document:
                {current_latex}

                User request: "{user_request}"

                {file_context}

                Instructions:
                1. Understand what the user wants to change or improve
                2. Make the requested modifications while preserving the document structure
                3. Keep existing content that's not affected by the request
                4. Ensure the document remains compilable and well-formatted
                5. Add comprehensive content where requested

                Return the complete modified LaTeX document.
                """

            response = client.ChatCompletion.create(
                model='gpt-4',  # Use GPT-4 for better document understanding
                messages=[
                    {"role": "system", "content": "You are an expert LaTeX document editor. You understand document structure and can make precise modifications while preserving existing content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,
                temperature=0.2
            )

            latex_code = response.choices[0].message.content.strip()

            # Clean up the response
            import re
            latex_code = re.sub(r'^```latex\s*\n?', '', latex_code)
            latex_code = re.sub(r'\n?```$', '', latex_code)

            return latex_code

        except Exception as e:
            logger.error(f"Error modifying existing document: {e}")
            return current_latex  # Return original if modification fails











class ReviewerAgent:
    """Agent responsible for document review and quality assurance"""
    
    def review_document(
        self,
        latex_code: str,
        document_plan: DocumentPlan
    ) -> Dict[str, Any]:
        """Review the generated document"""
        
        issues = []
        improvements = []
        
        # Basic checks
        if '\\maketitle' not in latex_code:
            issues.append("Missing title generation")
        
        if len(latex_code.split('\\section')) < len(document_plan.sections):
            issues.append("Missing sections")
        
        # For now, assume no improvements needed
        return {
            'needs_improvement': False,
            'improved_latex': latex_code,
            'issues': issues,
            'notes': improvements
        }

class IntentAnalyzerAgent:
    """Agent responsible for analyzing user intent for document modifications"""

    def analyze_intent(self, user_request: str, current_latex: str, conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """Analyze user intent to determine how to modify the document"""
        try:
            from services.ai_service import AIService
            client = AIService._get_openai_client()

            # Build conversation context
            context = ""
            if conversation_history:
                recent_messages = conversation_history[-4:]
                for msg in recent_messages:
                    role = "User" if msg['role'] == 'user' else "Assistant"
                    context += f"{role}: {msg['content'][:150]}...\n"

            # Analyze the current document structure
            sections = self._extract_sections(current_latex)

            prompt = f"""
            Analyze the user's intent for modifying this LaTeX document:

            Current document sections: {', '.join(sections)}

            Conversation context:
            {context}

            User request: "{user_request}"

            Determine the user's intent and respond with a JSON object containing:
            {{
                "intent": "add|modify|replace|rewrite",
                "target_sections": ["section1", "section2"],
                "modification_type": "content|structure|both",
                "scope": "specific|entire_document",
                "description": "Brief description of what the user wants"
            }}

            Intent definitions:
            - add: User wants to add new content/sections
            - modify: User wants to change existing content
            - replace: User wants to replace specific sections
            - rewrite: User wants to completely rewrite the document

            Return only the JSON object, no explanations.
            """

            response = client.ChatCompletion.create(
                model='gpt-3.5-turbo',
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing user intent for document modifications. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.1
            )

            import json
            intent_data = json.loads(response.choices[0].message.content.strip())

            return {
                'intent': intent_data.get('intent', 'modify'),
                'target_sections': intent_data.get('target_sections', []),
                'modification_type': intent_data.get('modification_type', 'content'),
                'scope': intent_data.get('scope', 'specific'),
                'description': intent_data.get('description', 'Document modification'),
                'current_sections': sections
            }

        except Exception as e:
            logger.error(f"Error analyzing intent: {e}")
            # Return default intent
            return {
                'intent': 'modify',
                'target_sections': [],
                'modification_type': 'content',
                'scope': 'specific',
                'description': 'Document modification',
                'current_sections': self._extract_sections(current_latex)
            }

    def _extract_sections(self, latex_code: str) -> List[str]:
        """Extract section names from LaTeX code"""
        import re
        sections = []

        # Find all section commands
        section_patterns = [
            r'\\chapter\{([^}]+)\}',
            r'\\section\{([^}]+)\}',
            r'\\subsection\{([^}]+)\}',
            r'\\subsubsection\{([^}]+)\}'
        ]

        for pattern in section_patterns:
            matches = re.findall(pattern, latex_code)
            sections.extend(matches)

        return sections
