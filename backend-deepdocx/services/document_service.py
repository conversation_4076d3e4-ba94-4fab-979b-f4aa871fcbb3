"""
Document management service for LaTeX documents and versioning
"""

import uuid
from datetime import datetime
from flask import current_app
from services.supabase_client import get_service_supabase
from services.ai_service import AIService
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    
    @staticmethod
    def create_document(user_id: str, title: str = None, description: str = None, folder_id: str = None, document_type: str = 'research_paper'):
        """Create a new document"""
        supabase = get_service_supabase()

        try:
            # If no title provided, generate one
            if not title:
                title = f"Untitled Document {datetime.now().strftime('%Y-%m-%d %H:%M')}"

            document_data = {
                'user_id': user_id,
                'folder_id': folder_id,
                'title': title,
                'description': description,
                'type': document_type,
                'status': 'draft',
                'metadata': {}
            }

            # Create document
            result = supabase.table('documents').insert(document_data).execute()
            document = result.data[0]

            # Create initial conversation
            conversation_data = {
                'document_id': document['id'],
                'user_id': user_id,
                'title': f"Conversation for {title}"
            }

            conv_result = supabase.table('conversations').insert(conversation_data).execute()
            conversation = conv_result.data[0]

            return {
                'document': document,
                'conversation': conversation
            }

        except Exception as e:
            logger.error(f"Error creating document: {e}")
            raise

    @staticmethod
    def create_document_with_first_message(user_id: str, message_content: str, folder_id: str = None, document_type: str = 'research_paper', referenced_files: list = None):
        """Create a new document with AI-generated name and LaTeX from first message"""
        supabase = get_service_supabase()

        try:
            # Generate document title using AI
            suggested_title = AIService.generate_document_title(message_content)

            # Create document with AI-generated title
            document_data = {
                'user_id': user_id,
                'folder_id': folder_id,
                'title': suggested_title,
                'description': message_content[:500],  # Use first part of message as description
                'type': document_type,
                'status': 'draft',
                'metadata': {}
            }

            # Create document
            result = supabase.table('documents').insert(document_data).execute()
            document = result.data[0]

            # Create initial conversation
            conversation_data = {
                'document_id': document['id'],
                'user_id': user_id,
                'title': f"Conversation for {suggested_title}"
            }

            conv_result = supabase.table('conversations').insert(conversation_data).execute()
            conversation = conv_result.data[0]

            # Generate initial LaTeX code using AI
            ai_response = AIService.process_document_message(
                message_content=message_content,
                document_title=suggested_title,
                current_latex=None,
                conversation_history=[],
                referenced_files=referenced_files
            )

            # Create user message
            from services.conversation_service import ConversationService
            user_message = ConversationService.create_message(
                conversation_id=conversation['id'],
                role='user',
                content=message_content,
                metadata={'referenced_files': referenced_files or []}
            )

            # Create AI response message
            ai_message = ConversationService.create_message(
                conversation_id=conversation['id'],
                role='assistant',
                content=ai_response['response'],
                metadata={'latex_generated': True}
            )

            # Create first document version with AI-generated LaTeX
            first_version = None
            if ai_response.get('latex_code'):
                first_version = DocumentService.create_document_version(
                    document_id=document['id'],
                    latex_code=ai_response['latex_code'],
                    description="Initial version created by AI",
                    created_by_message_id=user_message['id']
                )

            return {
                'document': document,
                'conversation': conversation,
                'first_version': first_version,
                'user_message': user_message,
                'ai_message': ai_message,
                'latex_code': ai_response.get('latex_code')
            }

        except Exception as e:
            logger.error(f"Error creating document with first message: {e}")
            raise
    
    @staticmethod
    def get_user_documents(user_id: str, folder_id: str = None):
        """Get all documents for a user, optionally filtered by folder"""
        supabase = get_service_supabase()
        
        try:
            query = supabase.table('documents').select('*').eq('user_id', user_id)
            
            if folder_id:
                query = query.eq('folder_id', folder_id)
            else:
                query = query.is_('folder_id', 'null')
            
            result = query.order('updated_at', desc=True).execute()
            return result.data
            
        except Exception as e:
            logger.error(f"Error getting user documents: {e}")
            raise
    
    @staticmethod
    def get_document(document_id: str, user_id: str):
        """Get a specific document with current version"""
        supabase = get_service_supabase()
        
        try:
            # Get document
            doc_result = supabase.table('documents').select('*').eq('id', document_id).eq('user_id', user_id).execute()
            
            if not doc_result.data:
                return None
            
            document = doc_result.data[0]
            
            # Get current version
            version_result = supabase.table('document_versions').select('*').eq('document_id', document_id).eq('is_current', True).execute()
            
            current_version = version_result.data[0] if version_result.data else None
            
            # Get conversation
            conv_result = supabase.table('conversations').select('*').eq('document_id', document_id).eq('user_id', user_id).execute()
            
            conversation = conv_result.data[0] if conv_result.data else None
            
            return {
                'document': document,
                'current_version': current_version,
                'conversation': conversation
            }
            
        except Exception as e:
            logger.error(f"Error getting document: {e}")
            raise
    
    @staticmethod
    def update_document(document_id: str, user_id: str, update_data: dict):
        """Update document metadata"""
        supabase = get_service_supabase()
        
        try:
            # Remove fields that shouldn't be updated directly
            update_data.pop('id', None)
            update_data.pop('user_id', None)
            update_data.pop('created_at', None)
            
            result = supabase.table('documents').update(update_data).eq('id', document_id).eq('user_id', user_id).execute()
            
            if not result.data:
                return None
            
            return result.data[0]
            
        except Exception as e:
            logger.error(f"Error updating document: {e}")
            raise
    
    @staticmethod
    def delete_document(document_id: str, user_id: str):
        """Delete a document and all its versions"""
        supabase = get_service_supabase()
        
        try:
            # Delete document (cascades to versions, conversations, messages)
            result = supabase.table('documents').delete().eq('id', document_id).eq('user_id', user_id).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            raise
    
    @staticmethod
    def create_document_version(document_id: str, latex_code: str, description: str = None, created_by_message_id: str = None):
        """Create a new version of a document (LaTeX code only, no compilation)"""
        supabase = get_service_supabase()

        try:
            # Get current highest version number
            result = supabase.table('document_versions').select('version_number').eq('document_id', document_id).order('version_number', desc=True).limit(1).execute()

            next_version = 1
            if result.data:
                next_version = result.data[0]['version_number'] + 1

            # Create new version (without PDF compilation)
            version_data = {
                'document_id': document_id,
                'version_number': next_version,
                'latex_code': latex_code,
                'is_current': True,
                'description': description,
                'created_by_message_id': created_by_message_id
            }

            # Insert new version (trigger will handle setting is_current)
            version_result = supabase.table('document_versions').insert(version_data).execute()
            version = version_result.data[0]

            # Update document status
            supabase.table('documents').update({'status': 'draft'}).eq('id', document_id).execute()

            return version

        except Exception as e:
            logger.error(f"Error creating document version: {e}")
            raise
    
    @staticmethod
    def get_document_versions(document_id: str, user_id: str):
        """Get all versions of a document"""
        supabase = get_service_supabase()
        
        try:
            # Verify user owns the document
            doc_result = supabase.table('documents').select('id').eq('id', document_id).eq('user_id', user_id).execute()
            
            if not doc_result.data:
                return []
            
            # Get all versions
            result = supabase.table('document_versions').select('*').eq('document_id', document_id).order('version_number', desc=True).execute()
            
            return result.data
            
        except Exception as e:
            logger.error(f"Error getting document versions: {e}")
            raise
    
    @staticmethod
    def get_document_version(version_id: str, user_id: str):
        """Get a specific document version"""
        supabase = get_service_supabase()

        try:
            # First get the version
            version_result = supabase.table('document_versions').select('*').eq('id', version_id).execute()

            if not version_result.data:
                return None

            version = version_result.data[0]
            document_id = version['document_id']

            # Check if user owns the document
            doc_result = supabase.table('documents').select('user_id').eq('id', document_id).execute()

            if not doc_result.data or doc_result.data[0]['user_id'] != user_id:
                return None

            return version

        except Exception as e:
            logger.error(f"Error getting document version: {e}")
            raise
    
    @staticmethod
    def set_current_version(version_id: str, user_id: str):
        """Set a specific version as current"""
        supabase = get_service_supabase()

        try:
            # First get the version
            version_result = supabase.table('document_versions').select('*').eq('id', version_id).execute()

            if not version_result.data:
                logger.error(f"Version not found: {version_id}")
                return None

            version = version_result.data[0]
            document_id = version['document_id']

            # Check if user owns the document
            doc_result = supabase.table('documents').select('user_id').eq('id', document_id).execute()

            if not doc_result.data or doc_result.data[0]['user_id'] != user_id:
                logger.error(f"User {user_id} does not own document {document_id}")
                return None

            # Update all versions to not current
            supabase.table('document_versions').update({'is_current': False}).eq('document_id', document_id).execute()

            # Set this version as current
            result = supabase.table('document_versions').update({'is_current': True}).eq('id', version_id).execute()

            if result.data:
                logger.info(f"Successfully set version {version_id} as current for document {document_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to update version {version_id} as current")
                return None

        except Exception as e:
            logger.error(f"Error setting current version: {e}")
            raise
    
    @staticmethod
    def process_user_message_and_create_version(document_id: str, user_id: str, message_content: str, referenced_files: list = None):
        """Process user message with AI and create new document version"""
        try:
            # Get current document and version
            doc_info = DocumentService.get_document(document_id, user_id)
            if not doc_info:
                raise ValueError("Document not found")
            
            document = doc_info['document']
            current_version = doc_info['current_version']
            conversation = doc_info['conversation']
            
            # Get conversation history
            from services.conversation_service import ConversationService
            messages = ConversationService.get_conversation_messages(conversation['id'], user_id)
            
            # Process with AI using agent system
            print(f"=== DOCUMENT SERVICE DEBUG ===")
            print(f"Message: {message_content}")
            print(f"Referenced files: {referenced_files}")
            print(f"Referenced files type: {type(referenced_files)}")

            ai_response = AIService.process_document_message_with_agents(
                message_content=message_content,
                document_title=document['title'],
                current_latex=current_version['latex_code'] if current_version else None,
                conversation_history=messages,
                referenced_files=referenced_files
            )
            
            # Create user message
            user_message = ConversationService.create_message(
                conversation_id=conversation['id'],
                role='user',
                content=message_content,
                metadata={'referenced_files': referenced_files or []}
            )
            
            # Create new document version if AI provided LaTeX
            new_version = None
            if ai_response.get('latex_code'):
                new_version = DocumentService.create_document_version(
                    document_id=document_id,
                    latex_code=ai_response['latex_code'],
                    description=f"Updated via AI: {message_content[:100]}...",
                    created_by_message_id=user_message['id']
                )
            
            # Create AI response message
            ai_message = ConversationService.create_message(
                conversation_id=conversation['id'],
                role='assistant',
                content=ai_response['response'],
                metadata={
                    'created_version_id': new_version['id'] if new_version else None,
                    'model': current_app.config.get('OPENAI_MODEL', 'gpt-4')
                }
            )
            
            # Update document title if this is the first message
            if not messages and ai_response.get('suggested_title'):
                DocumentService.update_document(
                    document_id=document_id,
                    user_id=user_id,
                    update_data={'title': ai_response['suggested_title']}
                )
            
            return {
                'user_message': user_message,
                'ai_message': ai_message,
                'new_version': new_version,
                'updated_document': DocumentService.get_document(document_id, user_id)
            }
            
        except Exception as e:
            logger.error(f"Error processing user message: {e}")
            raise
