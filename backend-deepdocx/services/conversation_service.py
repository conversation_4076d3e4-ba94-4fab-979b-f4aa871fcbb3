"""
Conversation service for managing AI chat sessions
"""

from services.supabase_client import get_service_supabase
import logging

logger = logging.getLogger(__name__)

class ConversationService:
    
    @staticmethod
    def create_conversation(document_id: str, user_id: str, title: str = None):
        """Create a new conversation for a document"""
        supabase = get_service_supabase()
        
        try:
            conversation_data = {
                'document_id': document_id,
                'user_id': user_id,
                'title': title or f"Conversation for Document"
            }
            
            result = supabase.table('conversations').insert(conversation_data).execute()
            return result.data[0]
            
        except Exception as e:
            logger.error(f"Error creating conversation: {e}")
            raise
    
    @staticmethod
    def get_conversation(conversation_id: str, user_id: str):
        """Get a conversation by ID"""
        supabase = get_service_supabase()
        
        try:
            result = supabase.table('conversations').select('*').eq('id', conversation_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error getting conversation: {e}")
            raise
    
    @staticmethod
    def get_document_conversation(document_id: str, user_id: str):
        """Get the conversation for a specific document"""
        supabase = get_service_supabase()
        
        try:
            result = supabase.table('conversations').select('*').eq('document_id', document_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error getting document conversation: {e}")
            raise
    
    @staticmethod
    def create_message(conversation_id: str, role: str, content: str, metadata: dict = None):
        """Create a new message in a conversation"""
        supabase = get_service_supabase()
        
        try:
            message_data = {
                'conversation_id': conversation_id,
                'role': role,
                'content': content,
                'metadata': metadata or {}
            }
            
            result = supabase.table('messages').insert(message_data).execute()
            return result.data[0]
            
        except Exception as e:
            logger.error(f"Error creating message: {e}")
            raise
    
    @staticmethod
    def get_conversation_messages(conversation_id: str, user_id: str, limit: int = 50):
        """Get messages for a conversation"""
        supabase = get_service_supabase()
        
        try:
            # Verify user owns the conversation
            conv_result = supabase.table('conversations').select('id').eq('id', conversation_id).eq('user_id', user_id).execute()
            
            if not conv_result.data:
                return []
            
            # Get messages
            result = supabase.table('messages').select('*').eq('conversation_id', conversation_id).order('created_at', desc=False).limit(limit).execute()
            
            return result.data
            
        except Exception as e:
            logger.error(f"Error getting conversation messages: {e}")
            raise
    
    @staticmethod
    def update_conversation(conversation_id: str, user_id: str, update_data: dict):
        """Update conversation metadata"""
        supabase = get_service_supabase()
        
        try:
            # Remove fields that shouldn't be updated
            update_data.pop('id', None)
            update_data.pop('user_id', None)
            update_data.pop('document_id', None)
            update_data.pop('created_at', None)
            
            result = supabase.table('conversations').update(update_data).eq('id', conversation_id).eq('user_id', user_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error updating conversation: {e}")
            raise
    
    @staticmethod
    def delete_conversation(conversation_id: str, user_id: str):
        """Delete a conversation and all its messages"""
        supabase = get_service_supabase()
        
        try:
            result = supabase.table('conversations').delete().eq('id', conversation_id).eq('user_id', user_id).execute()
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Error deleting conversation: {e}")
            raise
