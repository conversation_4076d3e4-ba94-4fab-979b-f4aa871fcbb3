"""
File content extraction service for various file types
"""

import os
import logging
from typing import Dict, Any, Optional
import pandas as pd

logger = logging.getLogger(__name__)

class FileExtractionService:
    """Service for extracting content from various file types"""
    
    @staticmethod
    def extract_file_content(file_path: str, file_type: str) -> Dict[str, Any]:
        """
        Extract content from a file based on its type
        
        Args:
            file_path: Path to the file
            file_type: Type of file (pdf, docx, csv, etc.)
            
        Returns:
            Dictionary containing extracted content and metadata
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            extraction_result = {
                'success': False,
                'content': '',
                'metadata': {},
                'error': None
            }
            
            file_type = file_type.lower()
            
            if file_type == 'pdf':
                extraction_result = FileExtractionService._extract_pdf_content(file_path)
            elif file_type == 'docx':
                extraction_result = FileExtractionService._extract_docx_content(file_path)
            elif file_type in ['csv', 'xlsx', 'xls']:
                extraction_result = FileExtractionService._extract_spreadsheet_content(file_path, file_type)
            elif file_type in ['txt', 'md']:
                extraction_result = FileExtractionService._extract_text_content(file_path)
            else:
                extraction_result['error'] = f"Unsupported file type: {file_type}"
                
            return extraction_result
            
        except Exception as e:
            logger.error(f"Error extracting content from {file_path}: {e}")
            return {
                'success': False,
                'content': '',
                'metadata': {},
                'error': str(e)
            }
    
    @staticmethod
    def _extract_pdf_content(file_path: str) -> Dict[str, Any]:
        """Extract text content from PDF files"""
        try:
            import PyPDF2
            import pdfplumber
            
            content = ""
            metadata = {}
            
            # Try pdfplumber first (better for complex layouts)
            try:
                with pdfplumber.open(file_path) as pdf:
                    metadata['pages'] = len(pdf.pages)
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            content += page_text + "\n\n"
                    
                    # Extract tables if any
                    tables = []
                    for page in pdf.pages:
                        page_tables = page.extract_tables()
                        if page_tables:
                            tables.extend(page_tables)
                    
                    if tables:
                        metadata['tables_count'] = len(tables)
                        metadata['has_tables'] = True
                        
            except Exception as e:
                logger.warning(f"pdfplumber failed, trying PyPDF2: {e}")
                
                # Fallback to PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    metadata['pages'] = len(pdf_reader.pages)
                    
                    for page in pdf_reader.pages:
                        content += page.extract_text() + "\n\n"
            
            return {
                'success': True,
                'content': content.strip(),
                'metadata': metadata,
                'error': None
            }
            
        except ImportError as e:
            return {
                'success': False,
                'content': '',
                'metadata': {},
                'error': f"PDF extraction libraries not installed: {e}"
            }
        except Exception as e:
            return {
                'success': False,
                'content': '',
                'metadata': {},
                'error': f"Error extracting PDF: {e}"
            }
    
    @staticmethod
    def _extract_docx_content(file_path: str) -> Dict[str, Any]:
        """Extract text content from DOCX files"""
        try:
            from docx import Document
            
            doc = Document(file_path)
            content = ""
            
            # Extract paragraphs
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            
            # Extract tables
            tables_content = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                tables_content.append(table_data)
            
            metadata = {
                'paragraphs_count': len(doc.paragraphs),
                'tables_count': len(doc.tables),
                'has_tables': len(doc.tables) > 0
            }
            
            if tables_content:
                metadata['tables'] = tables_content
            
            return {
                'success': True,
                'content': content.strip(),
                'metadata': metadata,
                'error': None
            }
            
        except ImportError as e:
            return {
                'success': False,
                'content': '',
                'metadata': {},
                'error': f"DOCX extraction library not installed: {e}"
            }
        except Exception as e:
            return {
                'success': False,
                'content': '',
                'metadata': {},
                'error': f"Error extracting DOCX: {e}"
            }
    
    @staticmethod
    def _extract_spreadsheet_content(file_path: str, file_type: str) -> Dict[str, Any]:
        """Extract content from spreadsheet files (CSV, Excel)"""
        try:
            if file_type == 'csv':
                df = pd.read_csv(file_path)
            else:  # xlsx, xls
                df = pd.read_excel(file_path)
            
            # Convert to text representation
            content = df.to_string(index=False)
            
            metadata = {
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': df.columns.tolist(),
                'data_types': df.dtypes.to_dict()
            }
            
            # Add summary statistics for numeric columns
            numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
            if numeric_columns:
                metadata['numeric_columns'] = numeric_columns
                metadata['summary_stats'] = df[numeric_columns].describe().to_dict()
            
            return {
                'success': True,
                'content': content,
                'metadata': metadata,
                'error': None
            }
            
        except Exception as e:
            return {
                'success': False,
                'content': '',
                'metadata': {},
                'error': f"Error extracting spreadsheet: {e}"
            }
    
    @staticmethod
    def _extract_text_content(file_path: str) -> Dict[str, Any]:
        """Extract content from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            metadata = {
                'lines': len(content.split('\n')),
                'characters': len(content),
                'words': len(content.split())
            }
            
            return {
                'success': True,
                'content': content,
                'metadata': metadata,
                'error': None
            }
            
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    content = file.read()
                
                metadata = {
                    'lines': len(content.split('\n')),
                    'characters': len(content),
                    'words': len(content.split()),
                    'encoding': 'latin-1'
                }
                
                return {
                    'success': True,
                    'content': content,
                    'metadata': metadata,
                    'error': None
                }
            except Exception as e:
                return {
                    'success': False,
                    'content': '',
                    'metadata': {},
                    'error': f"Error reading text file: {e}"
                }
        except Exception as e:
            return {
                'success': False,
                'content': '',
                'metadata': {},
                'error': f"Error extracting text: {e}"
            }
    
    @staticmethod
    def get_file_summary(extraction_result: Dict[str, Any]) -> str:
        """Generate a human-readable summary of extracted file content"""
        if not extraction_result['success']:
            return f"Failed to extract content: {extraction_result['error']}"
        
        content = extraction_result['content']
        metadata = extraction_result['metadata']
        
        summary_parts = []
        
        # Basic content info
        word_count = len(content.split())
        summary_parts.append(f"Content: {word_count} words")
        
        # Type-specific metadata
        if 'pages' in metadata:
            summary_parts.append(f"{metadata['pages']} pages")
        
        if 'tables_count' in metadata and metadata['tables_count'] > 0:
            summary_parts.append(f"{metadata['tables_count']} tables")
        
        if 'rows' in metadata and 'columns' in metadata:
            summary_parts.append(f"{metadata['rows']} rows × {metadata['columns']} columns")
        
        # Content preview (first 200 characters)
        preview = content[:200] + "..." if len(content) > 200 else content
        summary_parts.append(f"Preview: {preview}")
        
        return " | ".join(summary_parts)
