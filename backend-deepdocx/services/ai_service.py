"""
AI service for LaTeX document generation and enhancement
"""

import openai
import re
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class AIService:

    @staticmethod
    def _get_openai_client():
        """Get configured OpenAI client"""
        api_key = current_app.config.get('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not configured")
        openai.api_key = api_key
        return openai
    
    @staticmethod
    def generate_document_title(message_content: str) -> str:
        """Generate a document title based on the first user message"""
        try:
            client = AIService._get_openai_client()
            
            prompt = f"""
            Based on the following user request for a LaTeX document, generate a concise, professional title (max 60 characters):
            
            User request: "{message_content}"
            
            Return only the title, nothing else.
            """
            
            response = client.ChatCompletion.create(
                model=current_app.config.get('OPENAI_MODEL', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that generates document titles."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=50,
                temperature=0.7,
                request_timeout=30  # 30 second timeout
            )
            
            title = response.choices[0].message.content.strip()
            # Remove quotes if present
            title = title.strip('"\'')
            
            return title[:60]  # Ensure max length
            
        except Exception as e:
            logger.error(f"Error generating document title: {e}")
            return "Untitled Document"
    
    @staticmethod
    def generate_initial_latex(message_content: str, document_title: str, referenced_files: list = None) -> str:
        """Generate initial LaTeX code based on user request"""
        try:
            client = AIService._get_openai_client()
            
            # Build context about referenced files
            file_context = ""
            if referenced_files:
                file_context = f"\n\nReferenced files: {', '.join(referenced_files)}"
                file_context += "\nPlease incorporate these files appropriately in the LaTeX document."
                file_context += "\nFor images (png, jpg, jpeg, gif), use \\includegraphics{filename} with proper figure environment."
                file_context += "\nFor PDFs, add them to bibliography or reference section."
            
            prompt = f"""
            Create a complete LaTeX document based on the following request:
            
            Title: {document_title}
            User request: "{message_content}"{file_context}
            
            Requirements:
            1. Create a complete, compilable LaTeX document
            2. Use appropriate document class (article, report, etc.)
            3. Include necessary packages (graphicx for images)
            4. Structure the document with sections as appropriate
            5. Add placeholder content where needed
            6. If images are referenced, use \\includegraphics{{filename}} with proper figure environment
            7. For user-uploaded images, reference them directly by filename (e.g., \\includegraphics{{image.png}})
            8. If papers/PDFs are referenced, add them to bibliography
            
            Return only the LaTeX code, no explanations.
            """
            
            response = client.ChatCompletion.create(
                model=current_app.config.get('OPENAI_MODEL', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "system", "content": "You are an expert LaTeX document creator. Generate clean, well-structured LaTeX code."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.3,
                request_timeout=60  # 60 second timeout for longer responses
            )
            
            latex_code = response.choices[0].message.content.strip()
            
            # Clean up the response (remove markdown code blocks if present)
            latex_code = re.sub(r'^```latex\s*\n?', '', latex_code)
            latex_code = re.sub(r'\n?```$', '', latex_code)
            
            return latex_code
            
        except Exception as e:
            logger.error(f"Error generating initial LaTeX: {e}")
            # Return a basic template as fallback
            return AIService._get_basic_latex_template(document_title)
    
    @staticmethod
    def enhance_latex(current_latex: str, message_content: str, conversation_history: list, referenced_files: list = None) -> str:
        """Enhance existing LaTeX code based on user request"""
        try:
            client = AIService._get_openai_client()
            
            # Build conversation context
            context = ""
            if conversation_history:
                recent_messages = conversation_history[-6:]  # Last 6 messages for context
                for msg in recent_messages:
                    role = "User" if msg['role'] == 'user' else "Assistant"
                    context += f"{role}: {msg['content'][:200]}...\n"
            
            # Build file context
            file_context = ""
            if referenced_files:
                file_context = f"\n\nReferenced files: {', '.join(referenced_files)}"
                file_context += "\nIncorporate these files appropriately."
                file_context += "\nFor images, use \\includegraphics{filename} with proper figure environment."
                file_context += "\nFor PDFs, add them to bibliography or reference section."
            
            prompt = f"""
            Modify the following LaTeX document based on the user's request:
            
            Current LaTeX:
            {current_latex}
            
            Conversation context:
            {context}
            
            User request: "{message_content}"{file_context}
            
            Requirements:
            1. Maintain the document structure and existing content
            2. Make the requested modifications
            3. Ensure the document remains compilable
            4. Add necessary packages if new features are used
            5. If images are referenced, include them properly
            6. If papers are referenced, add to bibliography
            
            Return only the modified LaTeX code, no explanations.
            """
            
            response = client.ChatCompletion.create(
                model=current_app.config.get('OPENAI_MODEL', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "system", "content": "You are an expert LaTeX editor. Modify LaTeX documents precisely based on user requests."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=3000,
                temperature=0.3,
                request_timeout=60  # 60 second timeout
            )
            
            latex_code = response.choices[0].message.content.strip()
            
            # Clean up the response
            latex_code = re.sub(r'^```latex\s*\n?', '', latex_code)
            latex_code = re.sub(r'\n?```$', '', latex_code)
            
            return latex_code
            
        except Exception as e:
            logger.error(f"Error enhancing LaTeX: {e}")
            return current_latex  # Return original if enhancement fails
    
    @staticmethod
    def generate_ai_response(message_content: str, latex_generated: bool, conversation_history: list) -> str:
        """Generate AI response to user message"""
        try:
            client = AIService._get_openai_client()
            
            # Build conversation context
            context = ""
            if conversation_history:
                recent_messages = conversation_history[-4:]  # Last 4 messages
                for msg in recent_messages:
                    role = "User" if msg['role'] == 'user' else "Assistant"
                    context += f"{role}: {msg['content'][:150]}...\n"
            
            action_taken = "I've updated your LaTeX document" if latex_generated else "I understand your request"
            
            prompt = f"""
            You are an AI assistant helping with LaTeX document creation. 
            
            Conversation context:
            {context}
            
            User message: "{message_content}"
            
            {action_taken} based on your request. Provide a helpful, concise response (max 200 words) that:
            1. Acknowledges what you've done
            2. Explains any changes made
            3. Offers next steps or suggestions
            4. Maintains a professional, helpful tone
            
            Do not include LaTeX code in your response.
            """
            
            response = client.ChatCompletion.create(
                model=current_app.config.get('OPENAI_MODEL', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "system", "content": "You are a helpful LaTeX document assistant."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.7,
                request_timeout=30  # 30 second timeout
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return "I've processed your request. Please let me know if you need any adjustments!"
    
    @staticmethod
    def process_document_message(message_content: str, document_title: str, current_latex: str = None, conversation_history: list = None, referenced_files: list = None):
        """Process a user message and return AI response with LaTeX if needed"""
        try:
            conversation_history = conversation_history or []

            # Determine if this is the first message (document creation)
            is_first_message = not current_latex and not conversation_history

            # Generate or enhance LaTeX
            latex_code = None
            suggested_title = None

            if is_first_message:
                # Generate title and initial LaTeX
                try:
                    suggested_title = AIService.generate_document_title(message_content)
                except Exception as e:
                    logger.warning(f"Failed to generate title with AI, using fallback: {e}")
                    suggested_title = f"Document: {message_content[:50]}..."

                try:
                    latex_code = AIService.generate_initial_latex(message_content, suggested_title, referenced_files)
                except Exception as e:
                    logger.warning(f"Failed to generate LaTeX with AI, using template: {e}")
                    latex_code = AIService._get_basic_latex_template(suggested_title)
            else:
                # Enhance existing LaTeX
                try:
                    latex_code = AIService.enhance_latex(current_latex, message_content, conversation_history, referenced_files)
                except Exception as e:
                    logger.warning(f"Failed to enhance LaTeX with AI, keeping current: {e}")
                    latex_code = current_latex

            # Generate AI response
            try:
                ai_response = AIService.generate_ai_response(message_content, bool(latex_code), conversation_history)
            except Exception as e:
                logger.warning(f"Failed to generate AI response, using fallback: {e}")
                ai_response = "I've processed your request. The document has been updated."

            return {
                'response': ai_response,
                'latex_code': latex_code,
                'suggested_title': suggested_title
            }

        except Exception as e:
            logger.error(f"Error processing document message: {e}")
            # Return a basic fallback response
            fallback_latex = current_latex or AIService._get_basic_latex_template(document_title)
            return {
                'response': "I apologize, but I encountered an error processing your request. I've provided a basic document template.",
                'latex_code': fallback_latex,
                'suggested_title': None
            }

    @staticmethod
    def process_document_message_with_agents(
        message_content: str,
        document_title: str = None,
        current_latex: str = None,
        conversation_history: list = None,
        referenced_files: list = None,
        agent_context: dict = None,
        user_responses: dict = None
    ):
        """Process a document message using the agent orchestrator system"""
        try:
            # Import here to avoid circular imports
            from services.agent_orchestrator import AgentOrchestrator, AgentContext, AgentState

            orchestrator = AgentOrchestrator()

            # Convert agent_context dict back to AgentContext object if provided
            current_context = None
            if agent_context:
                current_context = AgentContext(
                    user_request=agent_context.get('user_request', message_content),
                    document_plan=agent_context.get('document_plan'),
                    referenced_files=agent_context.get('referenced_files', []),
                    extracted_content=agent_context.get('extracted_content', {}),
                    clarification_questions=agent_context.get('clarification_questions', []),
                    user_responses=agent_context.get('user_responses', {}),
                    current_latex=agent_context.get('current_latex', ''),
                    state=AgentState(agent_context.get('state', 'planning')),
                    progress=agent_context.get('progress', 0.0),
                    error_message=agent_context.get('error_message', '')
                )

            # Process the request through the agent system
            print(f"=== AI SERVICE DEBUG ===")
            print(f"Message: {message_content}")
            print(f"Referenced files: {referenced_files}")
            print(f"Referenced files type: {type(referenced_files)}")

            result = orchestrator.process_document_request(
                user_request=message_content,
                referenced_files=referenced_files,
                user_responses=user_responses,
                current_context=current_context,
                existing_latex=current_latex,
                conversation_history=conversation_history
            )

            # Convert result to the expected format
            if result['status'] == 'completed':
                # Generate contextual response based on document type
                if result.get('document_type') == 'modification':
                    intent_info = result.get('modification_intent', {})
                    intent = intent_info.get('intent', 'modified')
                    description = intent_info.get('description', 'your document')

                    if intent == 'add':
                        response_text = f"I've added the requested content to your document. {description}"
                    elif intent == 'modify':
                        response_text = f"I've modified your document as requested. {description}"
                    elif intent == 'replace':
                        response_text = f"I've replaced the specified sections in your document. {description}"
                    else:
                        response_text = f"I've updated your document based on your request. {description}"
                else:
                    response_text = f"I've successfully created your {result['document_type'].replace('_', ' ')}. The document includes all the sections you requested and follows the appropriate academic/professional standards."

                return {
                    'response': response_text,
                    'latex_code': result['latex_code'],
                    'suggested_title': result['document_title'],
                    'agent_result': result
                }
            elif result['status'] == 'clarification_needed':
                questions_text = "\n".join([f"• {q}" for q in result['questions']])
                return {
                    'response': f"I need some clarification to create the best document for you:\n\n{questions_text}\n\nPlease provide answers to these questions so I can tailor the document to your specific needs.",
                    'latex_code': None,
                    'suggested_title': None,
                    'agent_result': result,
                    'needs_clarification': True
                }
            elif result['status'] == 'awaiting_responses':
                missing_questions = "\n".join([f"• {q}" for q in result['missing_questions']])
                return {
                    'response': f"I'm still waiting for responses to these questions:\n\n{missing_questions}\n\nOnce you provide these details, I'll complete your document.",
                    'latex_code': None,
                    'suggested_title': None,
                    'agent_result': result,
                    'needs_clarification': True
                }
            else:
                # Error or other status - fall back to simple processing
                logger.warning(f"Agent processing returned status: {result['status']}, falling back to simple processing")
                return AIService.process_document_message(
                    message_content, document_title, current_latex, conversation_history, referenced_files
                )

        except Exception as e:
            logger.error(f"Error in agent-based processing: {e}")
            # Fall back to the original processing method
            return AIService.process_document_message(
                message_content, document_title, current_latex, conversation_history, referenced_files
            )
    
    @staticmethod
    def _get_basic_latex_template(title: str) -> str:
        """Get a basic LaTeX template as fallback"""
        return f"""\\documentclass[12pt]{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath}}
\\usepackage{{amsfonts}}
\\usepackage{{amssymb}}
\\usepackage{{graphicx}}
\\usepackage{{geometry}}
\\geometry{{margin=1in}}

\\title{{{title}}}
\\author{{Your Name}}
\\date{{\\today}}

\\begin{{document}}

\\maketitle

\\section{{Introduction}}

This is the beginning of your document. Please provide more details about what you'd like to include.

\\section{{Main Content}}

Add your main content here.

\\section{{Conclusion}}

Summarize your findings or conclusions here.

\\end{{document}}"""
