"""
Document type classification service
"""

import re
import logging
from typing import Dict, List, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class DocumentType:
    """Document type configuration"""
    name: str
    description: str
    typical_length: Tuple[int, int]  # (min_pages, max_pages)
    sections: List[str]
    style_requirements: Dict[str, str]
    latex_packages: List[str]

class DocumentClassifier:
    """Service for classifying document types and providing specifications"""
    
    # Document type definitions
    DOCUMENT_TYPES = {
        'research_paper': DocumentType(
            name='Research Paper',
            description='Academic research paper with technical depth',
            typical_length=(10, 50),
            sections=[
                'Abstract',
                'Introduction', 
                'Literature Review',
                'Methodology',
                'Results',
                'Discussion',
                'Conclusion',
                'References'
            ],
            style_requirements={
                'tone': 'formal_academic',
                'depth': 'technical_detailed',
                'citations': 'required',
                'figures': 'encouraged',
                'mathematical_notation': 'as_needed'
            },
            latex_packages=[
                'amsmath', 'amsfonts', 'amssymb', 'graphicx', 'cite', 
                'algorithm', 'algorithmic', 'booktabs', 'multirow'
            ]
        ),
        
        'business_report': DocumentType(
            name='Business Report',
            description='Professional business analysis and reporting',
            typical_length=(5, 20),
            sections=[
                'Executive Summary',
                'Introduction',
                'Analysis',
                'Findings',
                'Recommendations',
                'Conclusion',
                'Appendices'
            ],
            style_requirements={
                'tone': 'professional_business',
                'depth': 'analytical_practical',
                'citations': 'optional',
                'figures': 'charts_graphs',
                'mathematical_notation': 'minimal'
            },
            latex_packages=[
                'graphicx', 'booktabs', 'multirow', 'xcolor', 'tikz', 'pgfplots'
            ]
        ),
        
        'invoice': DocumentType(
            name='Invoice',
            description='Professional invoice or billing document',
            typical_length=(1, 3),
            sections=[
                'Header Information',
                'Client Details',
                'Service/Product List',
                'Calculations',
                'Payment Terms',
                'Footer'
            ],
            style_requirements={
                'tone': 'formal_business',
                'depth': 'concise_clear',
                'citations': 'none',
                'figures': 'tables_only',
                'mathematical_notation': 'calculations'
            },
            latex_packages=[
                'booktabs', 'multirow', 'xcolor', 'geometry', 'fancyhdr'
            ]
        ),
        
        'exam': DocumentType(
            name='Exam/Test',
            description='Academic examination or test document',
            typical_length=(2, 10),
            sections=[
                'Instructions',
                'Multiple Choice Questions',
                'Short Answer Questions',
                'Essay Questions',
                'Problem Sets',
                'Answer Key (optional)'
            ],
            style_requirements={
                'tone': 'clear_instructional',
                'depth': 'varied_by_question',
                'citations': 'rare',
                'figures': 'diagrams_as_needed',
                'mathematical_notation': 'subject_dependent'
            },
            latex_packages=[
                'amsmath', 'amsfonts', 'amssymb', 'graphicx', 'enumerate', 'multicol'
            ]
        ),
        
        'letter': DocumentType(
            name='Letter',
            description='Formal or informal correspondence',
            typical_length=(1, 3),
            sections=[
                'Header/Letterhead',
                'Date',
                'Recipient Address',
                'Salutation',
                'Body',
                'Closing',
                'Signature'
            ],
            style_requirements={
                'tone': 'personal_or_formal',
                'depth': 'conversational',
                'citations': 'rare',
                'figures': 'minimal',
                'mathematical_notation': 'none'
            },
            latex_packages=[
                'geometry', 'fancyhdr'
            ]
        ),
        
        'thesis': DocumentType(
            name='Thesis/Dissertation',
            description='Extended academic thesis or dissertation',
            typical_length=(50, 300),
            sections=[
                'Title Page',
                'Abstract',
                'Table of Contents',
                'List of Figures',
                'List of Tables',
                'Introduction',
                'Literature Review',
                'Methodology',
                'Results',
                'Discussion',
                'Conclusion',
                'References',
                'Appendices'
            ],
            style_requirements={
                'tone': 'formal_academic',
                'depth': 'comprehensive_detailed',
                'citations': 'extensive',
                'figures': 'required',
                'mathematical_notation': 'as_needed'
            },
            latex_packages=[
                'amsmath', 'amsfonts', 'amssymb', 'graphicx', 'cite', 
                'algorithm', 'algorithmic', 'booktabs', 'multirow',
                'tocloft', 'fancyhdr', 'setspace'
            ]
        ),
        
        'presentation': DocumentType(
            name='Presentation',
            description='Slide-based presentation document',
            typical_length=(5, 30),
            sections=[
                'Title Slide',
                'Outline',
                'Introduction',
                'Main Content Slides',
                'Conclusion',
                'Questions',
                'References'
            ],
            style_requirements={
                'tone': 'engaging_clear',
                'depth': 'concise_visual',
                'citations': 'minimal',
                'figures': 'visual_emphasis',
                'mathematical_notation': 'simplified'
            },
            latex_packages=[
                'beamer', 'graphicx', 'tikz', 'pgfplots', 'xcolor'
            ]
        )
    }
    
    @staticmethod
    def classify_document_type(user_request: str, referenced_files: List[Dict] = None) -> str:
        """
        Classify the document type based on user request and context
        
        Args:
            user_request: User's description of what they want to create
            referenced_files: List of files referenced by the user
            
        Returns:
            Document type key
        """
        request_lower = user_request.lower()
        
        # Keywords for each document type
        type_keywords = {
            'research_paper': [
                'research', 'paper', 'study', 'analysis', 'findings', 'methodology',
                'literature review', 'academic', 'journal', 'publication', 'hypothesis'
            ],
            'business_report': [
                'report', 'business', 'analysis', 'quarterly', 'annual', 'performance',
                'market', 'financial', 'strategic', 'executive summary'
            ],
            'invoice': [
                'invoice', 'bill', 'billing', 'payment', 'charge', 'cost',
                'service', 'product', 'client', 'customer', 'due'
            ],
            'exam': [
                'exam', 'test', 'quiz', 'assessment', 'questions', 'multiple choice',
                'essay questions', 'problem set', 'midterm', 'final'
            ],
            'letter': [
                'letter', 'correspondence', 'dear', 'sincerely', 'regards',
                'formal letter', 'business letter', 'cover letter'
            ],
            'thesis': [
                'thesis', 'dissertation', 'phd', 'masters', 'doctoral',
                'comprehensive', 'extensive research', 'chapters'
            ],
            'presentation': [
                'presentation', 'slides', 'powerpoint', 'beamer', 'talk',
                'conference', 'seminar', 'lecture'
            ]
        }
        
        # Score each document type
        scores = {}
        for doc_type, keywords in type_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in request_lower:
                    score += 1
            scores[doc_type] = score
        
        # Additional context from referenced files
        if referenced_files:
            for file_info in referenced_files:
                file_name = file_info.get('name', '').lower()
                if 'data' in file_name or file_name.endswith('.csv'):
                    scores['business_report'] += 2
                    scores['research_paper'] += 1
                elif 'invoice' in file_name or 'bill' in file_name:
                    scores['invoice'] += 3
        
        # Return the highest scoring type, default to research_paper
        if not scores or max(scores.values()) == 0:
            return 'research_paper'
        
        return max(scores, key=scores.get)
    
    @staticmethod
    def get_document_specification(doc_type: str) -> DocumentType:
        """Get the specification for a document type"""
        return DocumentClassifier.DOCUMENT_TYPES.get(doc_type, 
                                                     DocumentClassifier.DOCUMENT_TYPES['research_paper'])
    
    @staticmethod
    def estimate_content_requirements(doc_type: str, user_request: str) -> Dict[str, any]:
        """
        Estimate content requirements based on document type and user request
        
        Returns:
            Dictionary with content planning information
        """
        spec = DocumentClassifier.get_document_specification(doc_type)
        request_lower = user_request.lower()
        
        # Estimate length based on request complexity
        base_min, base_max = spec.typical_length
        
        # Adjust based on request indicators
        length_indicators = {
            'brief': 0.5,
            'short': 0.7,
            'detailed': 1.5,
            'comprehensive': 2.0,
            'extensive': 2.5,
            'in-depth': 2.0,
            'thorough': 1.8
        }
        
        length_multiplier = 1.0
        for indicator, multiplier in length_indicators.items():
            if indicator in request_lower:
                length_multiplier = max(length_multiplier, multiplier)
        
        estimated_min = max(1, int(base_min * length_multiplier))
        estimated_max = int(base_max * length_multiplier)
        
        return {
            'document_type': doc_type,
            'estimated_pages': (estimated_min, estimated_max),
            'required_sections': spec.sections,
            'style_requirements': spec.style_requirements,
            'latex_packages': spec.latex_packages,
            'complexity_level': DocumentClassifier._assess_complexity(user_request),
            'special_requirements': DocumentClassifier._identify_special_requirements(user_request)
        }
    
    @staticmethod
    def _assess_complexity(user_request: str) -> str:
        """Assess the complexity level of the requested document"""
        request_lower = user_request.lower()
        
        high_complexity_indicators = [
            'mathematical', 'statistical', 'algorithm', 'technical', 'advanced',
            'complex', 'detailed analysis', 'comprehensive study'
        ]
        
        medium_complexity_indicators = [
            'analysis', 'research', 'study', 'investigation', 'comparison'
        ]
        
        for indicator in high_complexity_indicators:
            if indicator in request_lower:
                return 'high'
        
        for indicator in medium_complexity_indicators:
            if indicator in request_lower:
                return 'medium'
        
        return 'low'
    
    @staticmethod
    def _identify_special_requirements(user_request: str) -> List[str]:
        """Identify special requirements from the user request"""
        request_lower = user_request.lower()
        requirements = []
        
        requirement_patterns = {
            'tables': ['table', 'data', 'statistics', 'numbers'],
            'figures': ['figure', 'chart', 'graph', 'diagram', 'image'],
            'mathematics': ['equation', 'formula', 'mathematical', 'calculation'],
            'code': ['code', 'algorithm', 'programming', 'implementation'],
            'citations': ['reference', 'citation', 'bibliography', 'source'],
            'appendix': ['appendix', 'supplementary', 'additional data']
        }
        
        for requirement, keywords in requirement_patterns.items():
            if any(keyword in request_lower for keyword in keywords):
                requirements.append(requirement)
        
        return requirements
