#!/usr/bin/env python3
"""
<PERSON>ript to create Supabase Storage buckets programmatically
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables first
load_dotenv()

# Import Flask app and services
from app import create_app
from services.supabase_client import get_service_supabase

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app instance
app = create_app()

def create_buckets():
    """Create required storage buckets"""
    with app.app_context():
        try:
            supabase = get_service_supabase()
            
            # Define buckets to create
            buckets_to_create = [
                {
                    'id': 'files',
                    'name': 'files',
                    'public': True,
                    'file_size_limit': 52428800,  # 50MB
                    'allowed_mime_types': [
                        'application/pdf',
                        'image/png',
                        'image/jpeg',
                        'image/gif',
                        'text/plain',
                        'text/markdown',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'text/csv',
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'application/vnd.ms-excel'
                    ]
                },
                {
                    'id': 'documents',
                    'name': 'documents',
                    'public': True,
                    'file_size_limit': 52428800,  # 50MB
                    'allowed_mime_types': [
                        'application/pdf',
                        'text/plain'
                    ]
                }
            ]
            
            # Get existing buckets
            existing_buckets = supabase.storage.list_buckets()
            if hasattr(existing_buckets, 'data'):
                existing_bucket_names = [bucket.name for bucket in existing_buckets.data]
            else:
                existing_bucket_names = [bucket.name for bucket in existing_buckets]
            
            logger.info(f"Existing buckets: {existing_bucket_names}")
            
            # Create missing buckets
            for bucket_config in buckets_to_create:
                bucket_name = bucket_config['name']
                
                if bucket_name in existing_bucket_names:
                    logger.info(f"✅ Bucket '{bucket_name}' already exists")
                    continue
                
                try:
                    # Create bucket
                    result = supabase.storage.create_bucket(
                        bucket_config['id'],
                        options={
                            'public': bucket_config['public'],
                            'file_size_limit': bucket_config['file_size_limit'],
                            'allowed_mime_types': bucket_config['allowed_mime_types']
                        }
                    )
                    
                    if hasattr(result, 'error') and result.error:
                        logger.error(f"❌ Failed to create bucket '{bucket_name}': {result.error}")
                    else:
                        logger.info(f"✅ Created bucket '{bucket_name}' successfully")
                        
                except Exception as e:
                    logger.error(f"❌ Error creating bucket '{bucket_name}': {e}")
            
            # Verify all buckets exist
            updated_buckets = supabase.storage.list_buckets()
            if hasattr(updated_buckets, 'data'):
                final_bucket_names = [bucket.name for bucket in updated_buckets.data]
            else:
                final_bucket_names = [bucket.name for bucket in updated_buckets]
            
            logger.info(f"Final buckets: {final_bucket_names}")
            
            required_buckets = ['files', 'documents']
            missing_buckets = [bucket for bucket in required_buckets if bucket not in final_bucket_names]
            
            if missing_buckets:
                logger.error(f"❌ Still missing buckets: {missing_buckets}")
                logger.info("You may need to create them manually in the Supabase dashboard")
                return False
            else:
                logger.info("✅ All required buckets are now available")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to create buckets: {e}")
            return False

if __name__ == '__main__':
    success = create_buckets()
    if not success:
        sys.exit(1)
