#!/usr/bin/env python3
"""
Test script to check which LaTeX packages are available in the current system
"""

import subprocess
import tempfile
import os
import sys

def test_latex_package(package_name):
    """Test if a LaTeX package is available by trying to compile a minimal document"""
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            test_tex = os.path.join(temp_dir, 'test.tex')
            test_content = f'''\\documentclass{{article}}
\\usepackage{{{package_name}}}
\\begin{{document}}
Test document for package {package_name}
\\end{{document}}
'''
            with open(test_tex, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            result = subprocess.run([
                'pdflatex',
                '-output-directory', temp_dir,
                '-interaction=nonstopmode',
                '-halt-on-error',
                test_tex
            ], capture_output=True, text=True, timeout=15)
            
            return result.returncode == 0, result.stderr
            
    except Exception as e:
        return False, str(e)

def main():
    # List of packages to test (comprehensive list like Overleaf)
    packages_to_test = [
        # Core math and symbols
        'amsmath', 'amsfonts', 'amssymb', 'amsthm', 'mathtools',
        
        # Graphics and figures
        'graphicx', 'float', 'caption', 'subcaption', 'wrapfig',
        'tikz', 'pgfplots', 'pgf', 'pgfkeys',
        
        # Page layout and formatting
        'geometry', 'fancyhdr', 'setspace', 'parskip', 'titlesec',
        
        # Colors and boxes
        'xcolor', 'tcolorbox', 'framed', 'mdframed',
        
        # Tables
        'booktabs', 'array', 'longtable', 'tabularx', 'multirow',
        
        # Lists and enumerations
        'enumitem', 'paralist',
        
        # Code and algorithms
        'listings', 'verbatim', 'fancyvrb', 'algorithm', 'algorithmic', 
        'algorithmicx', 'algpseudocode', 'algorithm2e',
        
        # Bibliography and references
        'natbib', 'biblatex', 'cite',
        
        # Links and URLs
        'hyperref', 'url', 'xurl',
        
        # Text and fonts
        'lipsum', 'blindtext', 'inputenc', 'fontenc',
        
        # Advanced packages
        'minted', 'beamer', 'standalone', 'subfig', 'subfigure',
        'ifthen', 'calc', 'etoolbox', 'xparse', 'expl3',
        
        # Scientific packages
        'siunitx', 'chemfig', 'mhchem', 'physics',
        
        # Drawing and diagrams
        'circuitikz', 'pgfgantt', 'forest', 'qtree'
    ]
    
    print("Testing LaTeX packages availability...")
    print("=" * 60)
    
    available_packages = []
    missing_packages = []
    
    for i, package in enumerate(packages_to_test, 1):
        print(f"[{i:2d}/{len(packages_to_test)}] Testing {package}...", end=" ")
        sys.stdout.flush()
        
        is_available, error = test_latex_package(package)
        
        if is_available:
            print("✓ AVAILABLE")
            available_packages.append(package)
        else:
            print("✗ MISSING")
            missing_packages.append((package, error))
    
    print("\n" + "=" * 60)
    print(f"SUMMARY: {len(available_packages)} available, {len(missing_packages)} missing")
    print("=" * 60)
    
    print("\n✓ AVAILABLE PACKAGES:")
    for package in sorted(available_packages):
        print(f"  {package}")
    
    print("\n✗ MISSING PACKAGES:")
    for package, error in sorted(missing_packages):
        print(f"  {package}")
        if "not found" not in error.lower():
            # Show first line of error for debugging
            first_error_line = error.split('\n')[0] if error else "Unknown error"
            print(f"    Error: {first_error_line}")
    
    # Generate updated package lists for the service
    print("\n" + "=" * 60)
    print("RECOMMENDED UPDATES FOR latex_service.py:")
    print("=" * 60)
    
    print("\n# Update AVAILABLE_PACKAGES list:")
    print("AVAILABLE_PACKAGES = [")
    for package in sorted(available_packages):
        print(f"    '{package}',")
    print("]")
    
    print("\n# Update problematic_packages dict (only truly missing packages):")
    print("problematic_packages = {")
    for package, _ in sorted(missing_packages):
        print(f"    '{package}': '',  # Not available in current TeX Live installation")
    print("}")

if __name__ == "__main__":
    main()
