# DeepDocX Backend Deployment Guide

## Quick Start (Development)

```bash
# 1. Navigate to backend directory
cd backend-deepdocx

# 2. Run setup script
./setup.sh

# 3. Configure environment
nano .env  # Add your Supabase and OpenAI credentials

# 4. Setup database
# Copy SQL from database_schema.sql and run in Supabase SQL editor

# 5. Test setup
python test_setup.py

# 6. Start development server
python run.py
```

## Production Deployment

### Option 1: Docker (Recommended)

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build manually
docker build -t deepdocx-backend .
docker run -p 5000:5000 --env-file .env deepdocx-backend
```

### Option 2: Traditional Server

```bash
# Install production dependencies
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 --timeout 120 app:create_app()
```

### Option 3: Cloud Platforms

#### Heroku
```bash
# Create Procfile
echo "web: gunicorn app:create_app()" > Procfile

# Deploy
heroku create your-app-name
heroku config:set SUPABASE_URL=your-url
heroku config:set SUPABASE_KEY=your-key
# ... set other env vars
git push heroku main
```

#### Railway
```bash
# railway.json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "gunicorn app:create_app()",
    "healthcheckPath": "/api/health"
  }
}
```

#### DigitalOcean App Platform
```yaml
# .do/app.yaml
name: deepdocx-backend
services:
- name: api
  source_dir: /
  github:
    repo: your-username/your-repo
    branch: main
  run_command: gunicorn app:create_app()
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: SUPABASE_URL
    value: your-supabase-url
  # ... other env vars
```

## Environment Variables

### Required
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key
- `OPENAI_API_KEY`: OpenAI API key
- `SECRET_KEY`: Flask secret (generate random string)
- `JWT_SECRET_KEY`: JWT secret (generate random string)

### Optional
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `GITHUB_CLIENT_ID`: GitHub OAuth client ID
- `GITHUB_CLIENT_SECRET`: GitHub OAuth client secret
- `FRONTEND_URL`: Frontend URL for CORS (default: http://localhost:3000)

## Database Setup

1. Create Supabase project
2. Run SQL from `database_schema.sql`
3. Configure RLS policies (included in schema)
4. Note your connection details

## OAuth Setup

### Google OAuth
1. Google Cloud Console → APIs & Services → Credentials
2. Create OAuth 2.0 Client ID
3. Add authorized redirect URI: `{your-backend-url}/api/auth/google/callback`
4. Add client ID and secret to environment

### GitHub OAuth
1. GitHub Settings → Developer settings → OAuth Apps
2. Create new OAuth App
3. Set callback URL: `{your-backend-url}/api/auth/github/callback`
4. Add client ID and secret to environment

## Security Checklist

- [ ] Use HTTPS in production
- [ ] Set secure secret keys
- [ ] Configure CORS properly
- [ ] Enable Supabase RLS
- [ ] Set up rate limiting
- [ ] Configure file upload limits
- [ ] Use environment variables for secrets
- [ ] Set up monitoring and logging

## Monitoring

### Health Check
- Endpoint: `GET /api/health`
- Returns: `{"status": "healthy", "service": "DeepDocX Backend", "version": "1.0.0"}`

### Logging
- Logs are written to `logs/app.log`
- Configure log level with `LOG_LEVEL` environment variable
- Use structured logging for production

### Metrics (Optional)
```python
# Add to app.py for metrics
from prometheus_flask_exporter import PrometheusMetrics

metrics = PrometheusMetrics(app)
```

## Troubleshooting

### Common Issues

1. **Supabase Connection Failed**
   - Check URL and keys
   - Verify network connectivity
   - Check RLS policies

2. **OpenAI API Errors**
   - Verify API key
   - Check rate limits
   - Monitor usage

3. **File Upload Issues**
   - Check upload directory permissions
   - Verify file size limits
   - Check allowed file types

4. **OAuth Redirect Issues**
   - Verify callback URLs
   - Check client IDs and secrets
   - Ensure HTTPS in production

### Debug Mode
```bash
export FLASK_DEBUG=True
export LOG_LEVEL=DEBUG
python run.py
```

## Performance Optimization

### Database
- Use connection pooling
- Optimize queries with indexes
- Monitor slow queries

### Caching
- Add Redis for session storage
- Cache AI responses
- Use CDN for file uploads

### Scaling
- Use multiple workers with Gunicorn
- Load balance across instances
- Consider async workers for I/O heavy operations

## Backup Strategy

### Database
- Supabase provides automatic backups
- Consider additional backup strategy for critical data

### Files
- Backup uploaded files regularly
- Consider cloud storage (S3, GCS) for production

### Configuration
- Version control environment templates
- Document deployment procedures
- Maintain rollback procedures
