"""
Conversation management routes
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.conversation_service import ConversationService
import logging

logger = logging.getLogger(__name__)

conversations_bp = Blueprint('conversations', __name__)

@conversations_bp.route('/<conversation_id>', methods=['GET'])
@jwt_required()
def get_conversation(conversation_id):
    """Get a conversation by ID"""
    try:
        user_id = get_jwt_identity()
        
        conversation = ConversationService.get_conversation(conversation_id, user_id)
        
        if not conversation:
            return jsonify({'error': 'Conversation not found'}), 404
        
        return jsonify(conversation), 200
        
    except Exception as e:
        logger.error(f"Error getting conversation: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@conversations_bp.route('/<conversation_id>/messages', methods=['GET'])
@jwt_required()
def get_conversation_messages(conversation_id):
    """Get messages for a conversation"""
    try:
        user_id = get_jwt_identity()
        limit = request.args.get('limit', 50, type=int)
        
        messages = ConversationService.get_conversation_messages(conversation_id, user_id, limit)
        
        return jsonify(messages), 200
        
    except Exception as e:
        logger.error(f"Error getting conversation messages: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@conversations_bp.route('/<conversation_id>/messages', methods=['POST'])
@jwt_required()
def create_message(conversation_id):
    """Create a new message in a conversation"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('content'):
            return jsonify({'error': 'Message content is required'}), 400
        
        # Verify user owns the conversation
        conversation = ConversationService.get_conversation(conversation_id, user_id)
        if not conversation:
            return jsonify({'error': 'Conversation not found'}), 404
        
        message = ConversationService.create_message(
            conversation_id=conversation_id,
            role=data.get('role', 'user'),
            content=data['content'],
            metadata=data.get('metadata', {})
        )
        
        return jsonify(message), 201
        
    except Exception as e:
        logger.error(f"Error creating message: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@conversations_bp.route('/<conversation_id>', methods=['PATCH'])
@jwt_required()
def update_conversation(conversation_id):
    """Update conversation metadata"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updated_conversation = ConversationService.update_conversation(conversation_id, user_id, data)
        
        if not updated_conversation:
            return jsonify({'error': 'Conversation not found'}), 404
        
        return jsonify(updated_conversation), 200
        
    except Exception as e:
        logger.error(f"Error updating conversation: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@conversations_bp.route('/<conversation_id>', methods=['DELETE'])
@jwt_required()
def delete_conversation(conversation_id):
    """Delete a conversation"""
    try:
        user_id = get_jwt_identity()
        
        success = ConversationService.delete_conversation(conversation_id, user_id)
        
        if not success:
            return jsonify({'error': 'Conversation not found'}), 404
        
        return jsonify({'message': 'Conversation deleted successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        return jsonify({'error': 'Internal server error'}), 500
