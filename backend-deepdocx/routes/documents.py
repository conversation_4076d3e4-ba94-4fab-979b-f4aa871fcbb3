"""
Document management routes
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.document_service import DocumentService
import logging

logger = logging.getLogger(__name__)

documents_bp = Blueprint('documents', __name__)

@documents_bp.route('', methods=['GET'])
@jwt_required()
def get_documents():
    """Get all documents for the current user"""
    try:
        user_id = get_jwt_identity()
        folder_id = request.args.get('folder_id')
        
        documents = DocumentService.get_user_documents(user_id, folder_id)
        
        return jsonify(documents), 200
        
    except Exception as e:
        logger.error(f"Error getting documents: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('', methods=['POST'])
@jwt_required()
def create_document():
    """Create a new document"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}
        
        result = DocumentService.create_document(
            user_id=user_id,
            title=data.get('title'),
            description=data.get('description'),
            folder_id=data.get('folder_id'),
            document_type=data.get('type', 'research_paper')
        )
        
        return jsonify(result), 201
        
    except Exception as e:
        logger.error(f"Error creating document: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>', methods=['GET'])
@jwt_required()
def get_document(document_id):
    """Get a specific document with current version"""
    try:
        user_id = get_jwt_identity()
        
        document_info = DocumentService.get_document(document_id, user_id)
        
        if not document_info:
            return jsonify({'error': 'Document not found'}), 404
        
        return jsonify(document_info), 200
        
    except Exception as e:
        logger.error(f"Error getting document: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>', methods=['PATCH'])
@jwt_required()
def update_document(document_id):
    """Update document metadata"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updated_document = DocumentService.update_document(document_id, user_id, data)
        
        if not updated_document:
            return jsonify({'error': 'Document not found'}), 404
        
        return jsonify(updated_document), 200
        
    except Exception as e:
        logger.error(f"Error updating document: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>', methods=['DELETE'])
@jwt_required()
def delete_document(document_id):
    """Delete a document"""
    try:
        user_id = get_jwt_identity()
        
        success = DocumentService.delete_document(document_id, user_id)
        
        if not success:
            return jsonify({'error': 'Document not found'}), 404
        
        return jsonify({'message': 'Document deleted successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>/versions', methods=['GET'])
@jwt_required()
def get_document_versions(document_id):
    """Get all versions of a document"""
    try:
        user_id = get_jwt_identity()
        
        versions = DocumentService.get_document_versions(document_id, user_id)
        
        return jsonify(versions), 200
        
    except Exception as e:
        logger.error(f"Error getting document versions: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>/versions', methods=['POST'])
@jwt_required()
def create_document_version(document_id):
    """Create a new version of a document"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('latex_code'):
            return jsonify({'error': 'LaTeX code is required'}), 400
        
        version = DocumentService.create_document_version(
            document_id=document_id,
            latex_code=data['latex_code'],
            description=data.get('description')
        )
        
        return jsonify(version), 201
        
    except Exception as e:
        logger.error(f"Error creating document version: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/versions/<version_id>', methods=['GET'])
@jwt_required()
def get_document_version(version_id):
    """Get a specific document version"""
    try:
        user_id = get_jwt_identity()
        
        version = DocumentService.get_document_version(version_id, user_id)
        
        if not version:
            return jsonify({'error': 'Version not found'}), 404
        
        return jsonify(version), 200
        
    except Exception as e:
        logger.error(f"Error getting document version: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/versions/<version_id>/set-current', methods=['POST'])
@jwt_required()
def set_current_version(version_id):
    """Set a specific version as current"""
    try:
        user_id = get_jwt_identity()
        
        version = DocumentService.set_current_version(version_id, user_id)
        
        if not version:
            return jsonify({'error': 'Version not found'}), 404
        
        return jsonify(version), 200
        
    except Exception as e:
        logger.error(f"Error setting current version: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/create-with-message', methods=['POST'])
@jwt_required()
def create_document_with_first_message():
    """Create a new document with AI-generated name and LaTeX from first message"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data or not data.get('message'):
            return jsonify({'error': 'Message is required'}), 400

        result = DocumentService.create_document_with_first_message(
            user_id=user_id,
            message_content=data['message'],
            folder_id=data.get('folder_id'),
            document_type=data.get('type', 'research_paper'),
            referenced_files=data.get('referenced_files', [])
        )

        return jsonify(result), 201

    except Exception as e:
        logger.error(f"Error creating document with first message: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@documents_bp.route('/<document_id>/chat', methods=['POST'])
@jwt_required()
def process_chat_message(document_id):
    """Process a chat message and update document"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data or not data.get('message'):
            return jsonify({'error': 'Message is required'}), 400

        print(f"=== CHAT MESSAGE DEBUG ===")
        print(f"Message: {data['message']}")
        print(f"Referenced files: {data.get('referenced_files', [])}")

        result = DocumentService.process_user_message_and_create_version(
            document_id=document_id,
            user_id=user_id,
            message_content=data['message'],
            referenced_files=data.get('referenced_files', [])
        )

        return jsonify(result), 200

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error processing chat message: {e}")
        return jsonify({'error': 'Internal server error'}), 500
