"""
Document sharing routes
"""

import secrets
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, jwt_required
from services.supabase_client import get_service_supabase
import logging

logger = logging.getLogger(__name__)

share_bp = Blueprint('share', __name__)

@share_bp.route('', methods=['POST'])
@jwt_required()
def create_share():
    """Create a new document share"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('document_id'):
            return jsonify({'error': 'Document ID is required'}), 400
        
        document_id = data['document_id']
        shared_with = data.get('shared_with')  # User ID or None for public
        permissions = data.get('permissions', 'view')
        expires_at = data.get('expires_at')
        
        # Validate permissions
        if permissions not in ['view', 'comment', 'edit']:
            return jsonify({'error': 'Invalid permissions'}), 400
        
        # Verify user owns the document
        supabase = get_service_supabase()
        doc_result = supabase.table('documents').select('id').eq('id', document_id).eq('user_id', user_id).execute()
        
        if not doc_result.data:
            return jsonify({'error': 'Document not found'}), 404
        
        # Generate share token
        share_token = secrets.token_urlsafe(32)
        
        # Parse expires_at if provided
        expires_at_dt = None
        if expires_at:
            try:
                expires_at_dt = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'error': 'Invalid expires_at format'}), 400
        
        # Create share record
        share_data = {
            'document_id': document_id,
            'shared_by': user_id,
            'shared_with': shared_with,
            'share_token': share_token,
            'permissions': permissions,
            'expires_at': expires_at_dt.isoformat() if expires_at_dt else None
        }
        
        result = supabase.table('shared_documents').insert(share_data).execute()
        share_record = result.data[0]
        
        return jsonify(share_record), 201
        
    except Exception as e:
        logger.error(f"Error creating share: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@share_bp.route('/<share_token>', methods=['GET'])
def get_shared_document(share_token):
    """Get a shared document by token"""
    try:
        supabase = get_service_supabase()
        
        # Get share record
        share_result = supabase.table('shared_documents').select('''
            *,
            documents!inner(*)
        ''').eq('share_token', share_token).execute()
        
        if not share_result.data:
            return jsonify({'error': 'Share not found'}), 404
        
        share_record = share_result.data[0]
        
        # Check if share is expired
        if share_record.get('expires_at'):
            expires_at = datetime.fromisoformat(share_record['expires_at'].replace('Z', '+00:00'))
            if datetime.utcnow().replace(tzinfo=expires_at.tzinfo) > expires_at:
                return jsonify({'error': 'Share has expired'}), 410
        
        # Get current document version
        version_result = supabase.table('document_versions').select('*').eq('document_id', share_record['document_id']).eq('is_current', True).execute()
        
        current_version = version_result.data[0] if version_result.data else None
        
        response_data = {
            'document': share_record['documents'],
            'current_version': current_version,
            'permissions': share_record['permissions'],
            'shared_by': share_record['shared_by'],
            'expires_at': share_record.get('expires_at')
        }
        
        return jsonify(response_data), 200
        
    except Exception as e:
        logger.error(f"Error getting shared document: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@share_bp.route('/document/<document_id>', methods=['GET'])
@jwt_required()
def list_document_shares(document_id):
    """List all shares for a document"""
    try:
        user_id = get_jwt_identity()
        supabase = get_service_supabase()
        
        # Verify user owns the document
        doc_result = supabase.table('documents').select('id').eq('id', document_id).eq('user_id', user_id).execute()
        
        if not doc_result.data:
            return jsonify({'error': 'Document not found'}), 404
        
        # Get all shares for the document
        shares_result = supabase.table('shared_documents').select('*').eq('document_id', document_id).order('created_at', desc=True).execute()
        
        return jsonify(shares_result.data), 200
        
    except Exception as e:
        logger.error(f"Error listing document shares: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@share_bp.route('/<share_id>', methods=['DELETE'])
@jwt_required()
def revoke_share(share_id):
    """Revoke a document share"""
    try:
        user_id = get_jwt_identity()
        supabase = get_service_supabase()
        
        # Verify user owns the share
        result = supabase.table('shared_documents').delete().eq('id', share_id).eq('shared_by', user_id).execute()
        
        if not result.data:
            return jsonify({'error': 'Share not found'}), 404
        
        return jsonify({'message': 'Share revoked successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error revoking share: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@share_bp.route('/<share_id>', methods=['PATCH'])
@jwt_required()
def update_share(share_id):
    """Update a document share"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        supabase = get_service_supabase()
        
        # Prepare update data
        update_data = {}
        
        if 'permissions' in data:
            if data['permissions'] not in ['view', 'comment', 'edit']:
                return jsonify({'error': 'Invalid permissions'}), 400
            update_data['permissions'] = data['permissions']
        
        if 'expires_at' in data:
            if data['expires_at']:
                try:
                    expires_at_dt = datetime.fromisoformat(data['expires_at'].replace('Z', '+00:00'))
                    update_data['expires_at'] = expires_at_dt.isoformat()
                except ValueError:
                    return jsonify({'error': 'Invalid expires_at format'}), 400
            else:
                update_data['expires_at'] = None
        
        if not update_data:
            return jsonify({'error': 'No valid fields to update'}), 400
        
        # Update share
        result = supabase.table('shared_documents').update(update_data).eq('id', share_id).eq('shared_by', user_id).execute()
        
        if not result.data:
            return jsonify({'error': 'Share not found'}), 404
        
        return jsonify(result.data[0]), 200
        
    except Exception as e:
        logger.error(f"Error updating share: {e}")
        return jsonify({'error': 'Internal server error'}), 500
