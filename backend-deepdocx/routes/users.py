"""
User management routes for settings and profile
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.auth_service import AuthService
import logging

logger = logging.getLogger(__name__)

users_bp = Blueprint('users', __name__)

@users_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get current user profile"""
    try:
        user_id = get_jwt_identity()
        user = AuthService.get_user_by_id(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify(user), 200
        
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/profile', methods=['PATCH'])
@jwt_required()
def update_profile():
    """Update user profile"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Remove sensitive fields that shouldn't be updated via this endpoint
        data.pop('password_hash', None)
        data.pop('email', None)  # Email changes should go through separate verification
        data.pop('oauth_provider', None)
        data.pop('google_id', None)
        data.pop('github_id', None)
        
        updated_user = AuthService.update_user(user_id, data)
        
        return jsonify(updated_user), 200
        
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/settings', methods=['GET'])
@jwt_required()
def get_settings():
    """Get user settings"""
    try:
        user_id = get_jwt_identity()
        user = AuthService.get_user_by_id(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify(user.get('settings', {})), 200
        
    except Exception as e:
        logger.error(f"Error getting user settings: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/settings', methods=['PATCH'])
@jwt_required()
def update_settings():
    """Update user settings"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Get current user to merge settings
        user = AuthService.get_user_by_id(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Merge new settings with existing ones
        current_settings = user.get('settings', {})
        current_settings.update(data)
        
        updated_user = AuthService.update_user(user_id, {'settings': current_settings})
        
        return jsonify(updated_user.get('settings', {})), 200
        
    except Exception as e:
        logger.error(f"Error updating user settings: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """Change user password"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('current_password') or not data.get('new_password'):
            return jsonify({'error': 'Current password and new password are required'}), 400
        
        # Get current user
        user = AuthService.get_user_by_id(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Check if user has a password (not OAuth-only)
        if not user.get('password_hash'):
            return jsonify({'error': 'Cannot change password for OAuth-only accounts'}), 400
        
        # Verify current password
        if not AuthService.verify_password(data['current_password'], user['password_hash']):
            return jsonify({'error': 'Current password is incorrect'}), 400
        
        # Hash new password
        new_password_hash = AuthService.hash_password(data['new_password'])
        
        # Update password
        AuthService.update_user(user_id, {'password_hash': new_password_hash})
        
        return jsonify({'message': 'Password changed successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error changing password: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/plan', methods=['GET'])
@jwt_required()
def get_plan():
    """Get user plan information"""
    try:
        user_id = get_jwt_identity()
        user = AuthService.get_user_by_id(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        plan_info = {
            'plan_type': user.get('plan_type', 'free'),
            'features': get_plan_features(user.get('plan_type', 'free'))
        }
        
        return jsonify(plan_info), 200
        
    except Exception as e:
        logger.error(f"Error getting user plan: {e}")
        return jsonify({'error': 'Internal server error'}), 500

def get_plan_features(plan_type):
    """Get features for a plan type"""
    plans = {
        'free': {
            'max_documents': 10,
            'max_file_size_mb': 10,
            'max_storage_gb': 1,
            'ai_requests_per_month': 100,
            'collaboration': False,
            'priority_support': False
        },
        'pro': {
            'max_documents': 100,
            'max_file_size_mb': 50,
            'max_storage_gb': 10,
            'ai_requests_per_month': 1000,
            'collaboration': True,
            'priority_support': False
        },
        'enterprise': {
            'max_documents': -1,  # Unlimited
            'max_file_size_mb': 100,
            'max_storage_gb': 100,
            'ai_requests_per_month': -1,  # Unlimited
            'collaboration': True,
            'priority_support': True
        }
    }
    
    return plans.get(plan_type, plans['free'])
