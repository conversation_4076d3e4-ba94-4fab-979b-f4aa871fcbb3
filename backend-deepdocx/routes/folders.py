"""
Folder management routes
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.folder_service import FolderService
import logging

logger = logging.getLogger(__name__)

folders_bp = Blueprint('folders', __name__)

@folders_bp.route('', methods=['GET'])
@jwt_required()
def get_folders():
    """Get folders for the current user"""
    try:
        user_id = get_jwt_identity()
        parent_folder_id = request.args.get('parent_id')
        
        folders = FolderService.get_user_folders(user_id, parent_folder_id)
        
        return jsonify(folders), 200
        
    except Exception as e:
        logger.error(f"Error getting folders: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('', methods=['POST'])
@jwt_required()
def create_folder():
    """Create a new folder"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('name'):
            return jsonify({'error': 'Folder name is required'}), 400
        
        folder = FolderService.create_folder(
            user_id=user_id,
            name=data['name'],
            parent_folder_id=data.get('parent_folder_id')
        )
        
        return jsonify(folder), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error creating folder: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('/<folder_id>', methods=['GET'])
@jwt_required()
def get_folder(folder_id):
    """Get a specific folder"""
    try:
        user_id = get_jwt_identity()
        
        folder = FolderService.get_folder(folder_id, user_id)
        
        if not folder:
            return jsonify({'error': 'Folder not found'}), 404
        
        return jsonify(folder), 200
        
    except Exception as e:
        logger.error(f"Error getting folder: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('/<folder_id>', methods=['PATCH'])
@jwt_required()
def update_folder(folder_id):
    """Update folder metadata"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updated_folder = FolderService.update_folder(folder_id, user_id, data)
        
        if not updated_folder:
            return jsonify({'error': 'Folder not found'}), 404
        
        return jsonify(updated_folder), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error updating folder: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('/<folder_id>', methods=['DELETE'])
@jwt_required()
def delete_folder(folder_id):
    """Delete a folder"""
    try:
        user_id = get_jwt_identity()
        
        success = FolderService.delete_folder(folder_id, user_id)
        
        if not success:
            return jsonify({'error': 'Folder not found'}), 404
        
        return jsonify({'message': 'Folder deleted successfully'}), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error deleting folder: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('/<folder_id>/contents', methods=['GET'])
@jwt_required()
def get_folder_contents(folder_id):
    """Get all contents of a folder"""
    try:
        user_id = get_jwt_identity()
        
        contents = FolderService.get_folder_contents(folder_id, user_id)
        
        return jsonify(contents), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error getting folder contents: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('/root/contents', methods=['GET'])
@jwt_required()
def get_root_contents():
    """Get contents of the root directory (no parent folder)"""
    try:
        user_id = get_jwt_identity()
        
        contents = FolderService.get_folder_contents(None, user_id)
        
        return jsonify(contents), 200
        
    except Exception as e:
        logger.error(f"Error getting root contents: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('/<folder_id>/path', methods=['GET'])
@jwt_required()
def get_folder_path(folder_id):
    """Get the full path to a folder"""
    try:
        user_id = get_jwt_identity()
        
        path = FolderService.get_folder_path(folder_id, user_id)
        
        return jsonify(path), 200
        
    except Exception as e:
        logger.error(f"Error getting folder path: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@folders_bp.route('/<folder_id>/move', methods=['POST'])
@jwt_required()
def move_folder(folder_id):
    """Move a folder to a new parent"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}
        
        moved_folder = FolderService.move_folder(
            folder_id=folder_id,
            user_id=user_id,
            new_parent_id=data.get('new_parent_id')
        )
        
        if not moved_folder:
            return jsonify({'error': 'Folder not found'}), 404
        
        return jsonify(moved_folder), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error moving folder: {e}")
        return jsonify({'error': 'Internal server error'}), 500
