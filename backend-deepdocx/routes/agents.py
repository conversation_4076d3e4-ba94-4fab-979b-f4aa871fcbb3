"""
Agent-based document processing routes
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.ai_service import AIService
from services.file_service import FileService
from services.document_service import DocumentService
from services.conversation_service import ConversationService
import logging

logger = logging.getLogger(__name__)

agents_bp = Blueprint('agents', __name__)

@agents_bp.route('/process-document', methods=['POST'])
@jwt_required()
def process_document_with_agents():
    """Process document creation/modification using the agent system"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('message'):
            return jsonify({'error': 'Message content is required'}), 400
        
        message_content = data['message']
        document_id = data.get('document_id')
        referenced_files = data.get('referenced_files', [])
        user_responses = data.get('user_responses', {})
        agent_context = data.get('agent_context')
        
        # Get current document and conversation if document_id provided
        current_latex = None
        conversation_history = []
        document_title = None
        
        if document_id:
            # Get existing document
            document = DocumentService.get_document(document_id, user_id)
            if not document:
                return jsonify({'error': 'Document not found'}), 404
            
            document_title = document['title']
            
            # Get current LaTeX from latest version
            versions = DocumentService.get_document_versions(document_id, user_id)
            if versions:
                current_latex = versions[0]['latex_code']
            
            # Get conversation history
            conversation = ConversationService.get_document_conversation(document_id, user_id)
            if conversation:
                conversation_history = ConversationService.get_conversation_messages(
                    conversation['id'], user_id, limit=20
                )
        
        # Process referenced files and get their content
        file_references = []
        if referenced_files:
            for file_ref in referenced_files:
                if isinstance(file_ref, str):
                    # File name reference - find the file
                    user_files = FileService.get_user_files(user_id)
                    for file_record in user_files:
                        if file_record['name'] == file_ref:
                            file_content = FileService.get_file_content(file_record['id'], user_id)
                            if file_content and file_content['content_available']:
                                file_references.append({
                                    'id': file_record['id'],
                                    'name': file_record['name'],
                                    'type': file_record['type'],
                                    'storage_path': file_record['storage_path'],
                                    'extracted_content': file_content['extracted_content'],
                                    'content_summary': file_content['content_summary']
                                })
                            else:
                                file_references.append({
                                    'id': file_record['id'],
                                    'name': file_record['name'],
                                    'type': file_record['type'],
                                    'storage_path': file_record['storage_path']
                                })
                            break
                elif isinstance(file_ref, dict) and file_ref.get('id'):
                    # File ID reference
                    file_content = FileService.get_file_content(file_ref['id'], user_id)
                    if file_content:
                        file_references.append({
                            'id': file_ref['id'],
                            'name': file_content['file_name'],
                            'type': file_content['file_type'],
                            'extracted_content': file_content['extracted_content'],
                            'content_summary': file_content['content_summary']
                        })
        
        # Process the message using the agent system
        result = AIService.process_document_message_with_agents(
            message_content=message_content,
            document_title=document_title,
            current_latex=current_latex,
            conversation_history=conversation_history,
            referenced_files=file_references,
            agent_context=agent_context,
            user_responses=user_responses
        )
        
        # Handle the result based on the agent processing outcome
        if result.get('needs_clarification'):
            # Return clarification questions without creating document/conversation
            return jsonify({
                'status': 'clarification_needed',
                'response': result['response'],
                'agent_result': result.get('agent_result', {}),
                'needs_clarification': True
            }), 200
        
        # If we have LaTeX code, create or update the document
        if result.get('latex_code'):
            if not document_id:
                # Create new document
                document_result = DocumentService.create_document_with_message(
                    user_id=user_id,
                    message_content=message_content,
                    folder_id=data.get('folder_id'),
                    document_type=data.get('document_type', 'research_paper')
                )
                
                document = document_result['document']
                conversation = document_result['conversation']
                document_id = document['id']
                
                # Update document title if suggested by agents
                if result.get('suggested_title'):
                    DocumentService.update_document(document_id, user_id, {
                        'title': result['suggested_title']
                    })
                
                # Create document version with LaTeX
                DocumentService.create_document_version(
                    document_id=document_id,
                    user_id=user_id,
                    latex_code=result['latex_code'],
                    version_notes="Initial version created by AI agents"
                )
                
                # Create user message
                ConversationService.create_message(
                    conversation_id=conversation['id'],
                    role='user',
                    content=message_content
                )
                
                # Create AI response message
                ConversationService.create_message(
                    conversation_id=conversation['id'],
                    role='assistant',
                    content=result['response']
                )
                
            else:
                # Update existing document
                conversation = ConversationService.get_document_conversation(document_id, user_id)
                
                if conversation:
                    # Create new version
                    DocumentService.create_document_version(
                        document_id=document_id,
                        user_id=user_id,
                        latex_code=result['latex_code'],
                        version_notes="Updated by AI agents"
                    )
                    
                    # Create user message
                    ConversationService.create_message(
                        conversation_id=conversation['id'],
                        role='user',
                        content=message_content
                    )
                    
                    # Create AI response message
                    ConversationService.create_message(
                        conversation_id=conversation['id'],
                        role='assistant',
                        content=result['response']
                    )
        
        return jsonify({
            'status': 'success',
            'response': result['response'],
            'document_id': document_id,
            'latex_code': result.get('latex_code'),
            'suggested_title': result.get('suggested_title'),
            'agent_result': result.get('agent_result', {}),
            'needs_clarification': result.get('needs_clarification', False)
        }), 200
        
    except Exception as e:
        logger.error(f"Error in agent document processing: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@agents_bp.route('/files-with-content', methods=['GET'])
@jwt_required()
def get_files_with_content():
    """Get files that have extracted content available"""
    try:
        user_id = get_jwt_identity()
        folder_id = request.args.get('folder_id')
        
        files = FileService.get_files_with_content(user_id, folder_id)
        
        return jsonify(files), 200
        
    except Exception as e:
        logger.error(f"Error getting files with content: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@agents_bp.route('/file-content/<file_id>', methods=['GET'])
@jwt_required()
def get_file_content(file_id):
    """Get extracted content for a specific file"""
    try:
        user_id = get_jwt_identity()
        
        file_content = FileService.get_file_content(file_id, user_id)
        
        if not file_content:
            return jsonify({'error': 'File not found'}), 404
        
        return jsonify(file_content), 200
        
    except Exception as e:
        logger.error(f"Error getting file content: {e}")
        return jsonify({'error': 'Internal server error'}), 500
