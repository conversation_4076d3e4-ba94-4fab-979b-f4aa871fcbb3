"""
File management routes
"""

import os
import tempfile
from flask import Blueprint, request, jsonify, send_file, current_app, Response
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.file_service import FileService
from services.supabase_client import get_service_supabase
import logging

logger = logging.getLogger(__name__)

files_bp = Blueprint('files', __name__)

@files_bp.route('', methods=['GET'])
@jwt_required()
def get_files():
    """Get all files for the current user"""
    try:
        user_id = get_jwt_identity()
        folder_id = request.args.get('folder_id')
        
        files = FileService.get_user_files(user_id, folder_id)
        
        return jsonify(files), 200
        
    except Exception as e:
        logger.error(f"Error getting files: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/upload', methods=['POST'])
@jwt_required()
def upload_file():
    """Upload a new file"""
    try:
        user_id = get_jwt_identity()
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        folder_id = request.form.get('folder_id')
        
        file_record = FileService.upload_file(file, user_id, folder_id)
        
        return jsonify(file_record), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/<file_id>', methods=['GET'])
@jwt_required()
def get_file(file_id):
    """Get file metadata"""
    try:
        user_id = get_jwt_identity()
        
        file_info = FileService.get_file(file_id, user_id)
        
        if not file_info:
            return jsonify({'error': 'File not found'}), 404
        
        return jsonify(file_info), 200
        
    except Exception as e:
        logger.error(f"Error getting file: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/<file_id>', methods=['PATCH'])
@jwt_required()
def update_file(file_id):
    """Update file metadata"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updated_file = FileService.update_file(file_id, user_id, data)
        
        if not updated_file:
            return jsonify({'error': 'File not found'}), 404
        
        return jsonify(updated_file), 200
        
    except Exception as e:
        logger.error(f"Error updating file: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/<file_id>', methods=['DELETE'])
@jwt_required()
def delete_file(file_id):
    """Delete a file"""
    try:
        user_id = get_jwt_identity()
        
        success = FileService.delete_file(file_id, user_id)
        
        if not success:
            return jsonify({'error': 'File not found'}), 404
        
        return jsonify({'message': 'File deleted successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/<file_id>/download', methods=['GET'])
@jwt_required()
def download_file(file_id):
    """Download a file from Supabase Storage"""
    try:
        user_id = get_jwt_identity()
        supabase = get_service_supabase()

        # Get file info
        file_info = FileService.get_file(file_id, user_id)
        if not file_info:
            return jsonify({'error': 'File not found'}), 404

        storage_path = file_info.get('storage_path')
        if not storage_path:
            return jsonify({'error': 'File storage path not found'}), 404

        # Download file from Supabase Storage
        result = supabase.storage.from_('files').download(storage_path)
        if result.error:
            logger.error(f"Error downloading file from storage: {result.error}")
            return jsonify({'error': 'Failed to download file'}), 500

        # Create response with file data
        response = Response(
            result.data,
            mimetype=FileService._get_content_type(file_info['type']),
            headers={
                'Content-Disposition': f'attachment; filename="{file_info["name"]}"'
            }
        )

        return response

    except Exception as e:
        logger.error(f"Error downloading file: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Legacy endpoint for backward compatibility (will be removed after migration)
@files_bp.route('/download/<filename>', methods=['GET'])
def download_file_legacy(filename):
    """Download a file (legacy endpoint for local files)"""
    try:
        upload_folder = current_app.config['UPLOAD_FOLDER']
        file_path = os.path.join(upload_folder, filename)

        if not os.path.exists(file_path):
            return jsonify({'error': 'File not found'}), 404

        return send_file(file_path)

    except Exception as e:
        logger.error(f"Error downloading file: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/<file_id>/content', methods=['GET'])
@jwt_required()
def get_file_content(file_id):
    """Get file content for text files from Supabase Storage"""
    try:
        user_id = get_jwt_identity()

        result = FileService.get_file_content_from_storage(file_id, user_id)

        if not result:
            return jsonify({'error': 'File not found or not readable'}), 404

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Error getting file content: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/search', methods=['GET'])
@jwt_required()
def search_files():
    """Search files by name"""
    try:
        user_id = get_jwt_identity()
        query = request.args.get('q', '')
        file_type = request.args.get('type')

        if not query:
            return jsonify({'error': 'Search query is required'}), 400

        files = FileService.search_files(user_id, query, file_type)

        return jsonify(files), 200

    except Exception as e:
        logger.error(f"Error searching files: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/images', methods=['GET'])
@jwt_required()
def get_user_images():
    """Get all image files for the current user"""
    try:
        user_id = get_jwt_identity()

        # Get all files for the user
        all_files = FileService.get_user_files(user_id)

        # Filter for image files
        image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp', 'tiff'}
        image_files = [
            file for file in all_files
            if file.get('type', '').lower() in image_extensions
        ]

        # Add LaTeX reference suggestions
        for file in image_files:
            file['latex_reference'] = f"@{file['name']}"
            if file.get('folder_id'):
                # TODO: Add folder path when folder hierarchy is implemented
                pass

        return jsonify(image_files), 200

    except Exception as e:
        logger.error(f"Error getting user images: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@files_bp.route('/debug/structure', methods=['GET'])
@jwt_required()
def debug_file_structure():
    """Debug endpoint to show user's file and folder structure"""
    try:
        user_id = get_jwt_identity()
        from services.folder_service import FolderService

        # Get all files and folders
        all_files = FileService.get_user_files(user_id)
        all_folders = FolderService.get_user_folders(user_id, 'all')

        # Build folder hierarchy
        folder_tree = {}
        for folder in all_folders:
            folder_tree[folder['id']] = {
                'name': folder['name'],
                'parent_id': folder.get('parent_folder_id'),
                'files': [],
                'subfolders': []
            }

        # Add files to folders
        for file in all_files:
            folder_id = file.get('folder_id')
            if folder_id and folder_id in folder_tree:
                folder_tree[folder_id]['files'].append({
                    'name': file['name'],
                    'type': file['type'],
                    'id': file['id']
                })

        # Build hierarchy
        root_folders = []
        for folder_id, folder_data in folder_tree.items():
            if not folder_data['parent_id']:
                root_folders.append({
                    'id': folder_id,
                    'name': folder_data['name'],
                    'files': folder_data['files']
                })

        # Add root files (files not in any folder)
        root_files = [
            {
                'name': file['name'],
                'type': file['type'],
                'id': file['id']
            }
            for file in all_files
            if not file.get('folder_id')
        ]

        return jsonify({
            'root_files': root_files,
            'folders': root_folders,
            'total_files': len(all_files),
            'total_folders': len(all_folders)
        }), 200

    except Exception as e:
        logger.error(f"Error getting file structure: {e}")
        return jsonify({'error': 'Internal server error'}), 500
