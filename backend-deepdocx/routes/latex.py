"""
LaTeX compilation routes
"""

from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.latex_service import LatexService
from services.document_service import DocumentService
import logging
import tempfile
import os

logger = logging.getLogger(__name__)

latex_bp = Blueprint('latex', __name__)

@latex_bp.route('/compile', methods=['POST'])
@jwt_required()
def compile_latex():
    """Compile LaTeX code to PDF"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('latex_code'):
            return jsonify({'error': 'LaTeX code is required'}), 400
        
        document_id = data.get('document_id')
        latex_code = data['latex_code']
        
        # Compile LaTeX
        result = LatexService.compile_latex(document_id, latex_code, user_id)
        
        # If compilation successful and we have a document_id, update the current version
        if result['success'] and document_id and result.get('pdf_url'):
            try:
                # Get current version of the document
                doc_info = DocumentService.get_document(document_id, user_id)
                if doc_info and doc_info.get('current_version'):
                    current_version = doc_info['current_version']
                    # Update the current version with the PDF URL
                    updated_version = LatexService.update_version_with_pdf_url(
                        current_version['id'],
                        result['pdf_url']
                    )
                    if updated_version:
                        logger.info(f"Updated version {current_version['id']} with PDF URL")
            except Exception as e:
                logger.warning(f"Failed to update version with PDF URL: {e}")
                # Don't fail the compilation if we can't update the version
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error compiling LaTeX: {e}")
        return jsonify({
            'success': False,
            'pdf_url': None,
            'errors': ['Internal server error during compilation']
        }), 500

@latex_bp.route('/download/<document_id>', methods=['GET'])
@jwt_required()
def download_pdf(document_id):
    """Download compiled PDF for a document"""
    try:
        user_id = get_jwt_identity()
        
        # Get document and check ownership
        doc_info = DocumentService.get_document(document_id, user_id)
        if not doc_info:
            return jsonify({'error': 'Document not found'}), 404
        
        current_version = doc_info.get('current_version')
        if not current_version or not current_version.get('compiled_pdf_url'):
            return jsonify({'error': 'No compiled PDF available'}), 404
        
        # For now, redirect to the PDF URL
        # In production, you might want to proxy the file for better security
        pdf_url = current_version['compiled_pdf_url']
        
        return jsonify({
            'download_url': pdf_url,
            'filename': f"{doc_info['document']['title']}.pdf"
        }), 200
        
    except Exception as e:
        logger.error(f"Error downloading PDF: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@latex_bp.route('/compile-version/<version_id>', methods=['POST'])
@jwt_required()
def compile_version(version_id):
    """Compile a specific document version"""
    try:
        user_id = get_jwt_identity()

        # Get version and check ownership
        version = DocumentService.get_document_version(version_id, user_id)
        if not version:
            return jsonify({'error': 'Version not found'}), 404

        # Compile the LaTeX code from this version
        result = LatexService.compile_latex(version['document_id'], version['latex_code'], user_id)

        # If compilation successful, update this version with PDF URL
        if result['success'] and result.get('pdf_url'):
            LatexService.update_version_with_pdf_url(version_id, result['pdf_url'])

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Error compiling version: {e}")
        return jsonify({
            'success': False,
            'pdf_url': None,
            'errors': ['Internal server error during compilation']
        }), 500

@latex_bp.route('/pdf/<document_id>/<filename>', methods=['GET'])
def serve_pdf(document_id, filename):
    """Serve locally stored PDF files"""
    try:
        # Construct file path
        pdf_path = os.path.join('uploads', 'pdfs', document_id, filename)

        # Check if file exists
        if not os.path.exists(pdf_path):
            return jsonify({'error': 'PDF not found'}), 404

        # Serve the file
        return send_file(
            pdf_path,
            mimetype='application/pdf',
            as_attachment=False,
            download_name=f"document_{document_id}.pdf"
        )

    except Exception as e:
        logger.error(f"Error serving PDF: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@latex_bp.route('/debug/images/<document_id>', methods=['GET'])
@jwt_required()
def debug_images(document_id):
    """Debug endpoint to check image resolution for a document"""
    try:
        user_id = get_jwt_identity()

        from services.file_service import FileService
        from services.folder_service import FolderService

        # Get all user files and folders
        all_files = FileService.get_user_files(user_id)
        all_folders = FolderService.get_user_folders(user_id, 'all')

        # Get document to check its LaTeX code
        doc_info = DocumentService.get_document(document_id, user_id)
        if not doc_info:
            return jsonify({'error': 'Document not found'}), 404

        current_version = doc_info.get('current_version')
        latex_code = current_version.get('latex_code', '') if current_version else ''

        # Find image references
        import re
        image_pattern = r'\\includegraphics(?:\[[^\]]*\])?\{([^}]+)\}'
        image_refs = re.findall(image_pattern, latex_code)

        return jsonify({
            'user_id': user_id,
            'document_id': document_id,
            'image_references': image_refs,
            'total_files': len(all_files),
            'total_folders': len(all_folders),
            'files': [{'name': f['name'], 'type': f.get('type'), 'folder_id': f.get('folder_id')} for f in all_files],
            'folders': [{'name': f['name'], 'id': f['id'], 'parent_id': f.get('parent_folder_id')} for f in all_folders],
            'latex_snippet': latex_code[:500] + '...' if len(latex_code) > 500 else latex_code
        }), 200

    except Exception as e:
        logger.error(f"Error debugging images: {e}")
        return jsonify({'error': 'Internal server error'}), 500
