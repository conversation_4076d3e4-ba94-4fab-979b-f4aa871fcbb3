"""
Authentication routes for sign up, sign in, OAuth, and user management
"""

import secrets
import requests
from urllib.parse import urlencode
from flask import Blueprint, request, jsonify, current_app, redirect
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.auth_service import AuthService
import logging

logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/signup', methods=['POST'])
def signup():
    """User registration endpoint"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Create user
        result = AuthService.create_user(
            email=data['email'],
            password=data['password'],
            first_name=data['first_name'],
            last_name=data['last_name']
        )
        
        return jsonify(result), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Signup error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/signin', methods=['POST'])
def signin():
    """User login endpoint"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400
        
        # Authenticate user
        result = AuthService.authenticate_user(
            email=data['email'],
            password=data['password']
        )
        
        return jsonify(result), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 401
    except Exception as e:
        logger.error(f"Signin error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/signout', methods=['POST'])
@jwt_required()
def signout():
    """User logout endpoint"""
    # In a stateless JWT system, logout is handled client-side
    # by removing the token from storage
    return jsonify({'message': 'Logged out successfully'}), 200

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """Get current user information"""
    try:
        user_id = get_jwt_identity()
        user = AuthService.get_user_by_id(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify(user), 200
        
    except Exception as e:
        logger.error(f"Get current user error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# Google OAuth routes
@auth_bp.route('/google', methods=['GET'])
def google_oauth():
    """Initiate Google OAuth flow"""
    try:
        # Create OAuth session
        state = AuthService.create_oauth_session('google')
        
        # Build Google OAuth URL
        params = {
            'client_id': current_app.config['GOOGLE_CLIENT_ID'],
            'redirect_uri': current_app.config.get('GOOGLE_REDIRECT_URI', 'http://localhost:5000/api/auth/google/callback'),
            'scope': 'openid email profile',
            'response_type': 'code',
            'state': state
        }
        
        auth_url = f"https://accounts.google.com/o/oauth2/auth?{urlencode(params)}"
        
        return jsonify({'auth_url': auth_url, 'state': state}), 200
        
    except Exception as e:
        logger.error(f"Google OAuth initiation error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/google/callback', methods=['GET'])
def google_oauth_callback():
    """Handle Google OAuth callback"""
    try:
        code = request.args.get('code')
        state = request.args.get('state')
        
        if not code or not state:
            return jsonify({'error': 'Missing code or state parameter'}), 400
        
        # Verify OAuth session
        if not AuthService.verify_oauth_session(state, 'google'):
            return jsonify({'error': 'Invalid or expired state'}), 400
        
        # Exchange code for access token
        token_data = {
            'client_id': current_app.config['GOOGLE_CLIENT_ID'],
            'client_secret': current_app.config['GOOGLE_CLIENT_SECRET'],
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': current_app.config.get('GOOGLE_REDIRECT_URI', 'http://localhost:5000/api/auth/google/callback')
        }
        
        token_response = requests.post('https://oauth2.googleapis.com/token', data=token_data)
        token_response.raise_for_status()
        token_info = token_response.json()
        
        # Get user info from Google
        user_info = AuthService.get_google_user_info(token_info['access_token'])
        
        # Find or create user
        result = AuthService.find_or_create_oauth_user(
            provider='google',
            oauth_id=user_info['id'],
            email=user_info['email'],
            first_name=user_info.get('given_name', ''),
            last_name=user_info.get('family_name', '')
        )
        
        # Redirect to frontend with token
        frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:3000')
        redirect_url = f"{frontend_url}/auth/callback?token={result['token']}"
        
        return redirect(redirect_url)
        
    except Exception as e:
        logger.error(f"Google OAuth callback error: {e}")
        frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:3000')
        return redirect(f"{frontend_url}/auth/signin?error=oauth_failed")

# GitHub OAuth routes
@auth_bp.route('/github', methods=['GET'])
def github_oauth():
    """Initiate GitHub OAuth flow"""
    try:
        # Create OAuth session
        state = AuthService.create_oauth_session('github')
        
        # Build GitHub OAuth URL
        params = {
            'client_id': current_app.config['GITHUB_CLIENT_ID'],
            'redirect_uri': current_app.config.get('GITHUB_REDIRECT_URI', 'http://localhost:5000/api/auth/github/callback'),
            'scope': 'user:email',
            'state': state
        }
        
        auth_url = f"https://github.com/login/oauth/authorize?{urlencode(params)}"
        
        return jsonify({'auth_url': auth_url, 'state': state}), 200
        
    except Exception as e:
        logger.error(f"GitHub OAuth initiation error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/github/callback', methods=['GET'])
def github_oauth_callback():
    """Handle GitHub OAuth callback"""
    try:
        code = request.args.get('code')
        state = request.args.get('state')
        
        if not code or not state:
            return jsonify({'error': 'Missing code or state parameter'}), 400
        
        # Verify OAuth session
        if not AuthService.verify_oauth_session(state, 'github'):
            return jsonify({'error': 'Invalid or expired state'}), 400
        
        # Exchange code for access token
        token_data = {
            'client_id': current_app.config['GITHUB_CLIENT_ID'],
            'client_secret': current_app.config['GITHUB_CLIENT_SECRET'],
            'code': code
        }
        
        token_response = requests.post(
            'https://github.com/login/oauth/access_token',
            data=token_data,
            headers={'Accept': 'application/json'}
        )
        token_response.raise_for_status()
        token_info = token_response.json()
        
        # Get user info from GitHub
        user_info = AuthService.get_github_user_info(token_info['access_token'])
        
        # Get user email (GitHub might not provide it in the user endpoint)
        email = user_info.get('email')
        if not email:
            email_response = requests.get(
                'https://api.github.com/user/emails',
                headers={'Authorization': f"token {token_info['access_token']}"}
            )
            emails = email_response.json()
            primary_email = next((e for e in emails if e['primary']), None)
            email = primary_email['email'] if primary_email else None
        
        if not email:
            raise ValueError("Unable to get email from GitHub")
        
        # Parse name
        name_parts = (user_info.get('name') or '').split(' ', 1)
        first_name = name_parts[0] if name_parts else user_info.get('login', '')
        last_name = name_parts[1] if len(name_parts) > 1 else ''
        
        # Find or create user
        result = AuthService.find_or_create_oauth_user(
            provider='github',
            oauth_id=str(user_info['id']),
            email=email,
            first_name=first_name,
            last_name=last_name
        )
        
        # Redirect to frontend with token
        frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:3000')
        redirect_url = f"{frontend_url}/auth/callback?token={result['token']}"
        
        return redirect(redirect_url)
        
    except Exception as e:
        logger.error(f"GitHub OAuth callback error: {e}")
        frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:3000')
        return redirect(f"{frontend_url}/auth/signin?error=oauth_failed")
