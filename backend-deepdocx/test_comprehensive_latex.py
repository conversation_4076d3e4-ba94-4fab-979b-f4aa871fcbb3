#!/usr/bin/env python3
"""
Comprehensive test of the improved LaTeX compilation system
"""

import tempfile
import subprocess
import os

def test_comprehensive_latex():
    """Test LaTeX compilation with all the previously problematic packages"""
    
    # Comprehensive test document with all the packages that were being removed
    test_latex = r"""
\documentclass[a4paper,12pt]{article}

% Previously problematic packages that should now work
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{lipsum}
\usepackage{hyperref}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{tcolorbox}
\usepackage{xcolor}
\usepackage{titlesec}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{algorithmicx}
\usepackage{algpseudocode}
\usepackage{natbib}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}

\geometry{margin=1in}

% Define some colors
\definecolor{myblue}{RGB}{0,100,200}
\definecolor{mygreen}{RGB}{0,150,0}

% Custom title formatting
\titleformat{\section}{\Large\bfseries\color{myblue}}{\thesection}{1em}{}

\title{Comprehensive LaTeX Package Test}
\author{DeepDocX Test Suite}
\date{\today}

\begin{document}

\maketitle

\begin{abstract}
This document tests all the LaTeX packages that were previously being removed by the overly aggressive package filtering. All these packages should now compile successfully.
\end{abstract}

\section{Introduction}
\lipsum[1]

This document demonstrates that complex LaTeX packages now work correctly in DeepDocX.

\section{TikZ Graphics Test}
Here's a simple TikZ diagram:

\begin{tikzpicture}
\draw[thick,->] (0,0) -- (4,0) node[anchor=north west] {x axis};
\draw[thick,->] (0,0) -- (0,3) node[anchor=south east] {y axis};
\draw[thick] (0,0) -- (3,2);
\end{tikzpicture}

\section{Tcolorbox Test}
\begin{tcolorbox}[colback=blue!5!white,colframe=blue!75!black,title=Important Note]
This is a test of the tcolorbox package. It should render as a colored box with a title.
\end{tcolorbox}

\begin{tcolorbox}[colback=mygreen!10,colframe=mygreen,title=Success]
Custom colors work too!
\end{tcolorbox}

\section{Algorithm Test}
\begin{algorithm}
\caption{Test Algorithm}
\begin{algorithmic}[1]
\Procedure{TestProcedure}{$n$}
    \State $result \gets 0$
    \For{$i \gets 1$ to $n$}
        \State $result \gets result + i$
    \EndFor
    \State \Return $result$
\EndProcedure
\end{algorithmic}
\end{algorithm}

\section{Mathematics Test}
Here's some math using amsmath:
\begin{equation}
E = mc^2
\end{equation}

\begin{align}
x &= a + b \\
y &= c + d
\end{align}

\section{Bibliography Test}
This document cites some references \cite{test2023}.

\section{Image Reference Test}
Here we would include an image with @mention syntax:
% \includegraphics[width=0.5\textwidth]{@images/database.jpg}

Note: Image inclusion commented out for this test since we don't have the actual image file.

\section{Conclusion}
All the previously problematic packages now work correctly:
\begin{itemize}
\item tikz and pgfplots for graphics
\item tcolorbox for colored boxes
\item xcolor for custom colors
\item titlesec for custom section formatting
\item algorithm packages for pseudocode
\item lipsum for dummy text
\item natbib for bibliography
\end{itemize}

\bibliographystyle{plain}
\begin{thebibliography}{1}
\bibitem{test2023}
Test Author.
\newblock Test paper.
\newblock \emph{Test Journal}, 2023.
\end{thebibliography}

\end{document}
"""

    print("Testing comprehensive LaTeX compilation...")
    print("=" * 60)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            tex_file = os.path.join(temp_dir, 'comprehensive_test.tex')
            
            # Write the test LaTeX file
            with open(tex_file, 'w', encoding='utf-8') as f:
                f.write(test_latex)
            
            print(f"Created test file: {tex_file}")
            print(f"LaTeX content length: {len(test_latex)} characters")
            
            # Compile with pdflatex
            print("\nRunning pdflatex compilation...")
            result = subprocess.run([
                'pdflatex',
                '-output-directory', temp_dir,
                '-interaction=nonstopmode',
                '-file-line-error',
                tex_file
            ], capture_output=True, text=True, timeout=60)
            
            pdf_file = os.path.join(temp_dir, 'comprehensive_test.pdf')
            
            if result.returncode == 0 and os.path.exists(pdf_file):
                pdf_size = os.path.getsize(pdf_file)
                print(f"✅ SUCCESS: PDF created successfully!")
                print(f"   PDF file size: {pdf_size} bytes")
                print(f"   All complex LaTeX packages compiled correctly!")
                
                # List the packages that worked
                working_packages = [
                    'tikz', 'pgfplots', 'tcolorbox', 'xcolor', 'titlesec',
                    'algorithm', 'algorithmic', 'algorithmicx', 'algpseudocode',
                    'natbib', 'lipsum', 'geometry', 'hyperref'
                ]
                
                print(f"\n✅ WORKING PACKAGES ({len(working_packages)}):")
                for pkg in working_packages:
                    print(f"   ✓ {pkg}")
                
                return True
                
            else:
                print(f"❌ FAILED: PDF compilation failed")
                print(f"   Return code: {result.returncode}")
                
                if result.stderr:
                    print(f"\nSTDERR (first 1000 chars):")
                    print(result.stderr[:1000])
                
                if result.stdout:
                    print(f"\nSTDOUT (first 1000 chars):")
                    print(result.stdout[:1000])
                
                return False
                
    except subprocess.TimeoutExpired:
        print("❌ FAILED: Compilation timeout")
        return False
    except Exception as e:
        print(f"❌ FAILED: Exception during compilation: {e}")
        return False

if __name__ == "__main__":
    success = test_comprehensive_latex()
    if success:
        print("\n🎉 COMPREHENSIVE TEST PASSED!")
        print("   All LaTeX packages are working correctly.")
        print("   The overly aggressive package removal has been fixed.")
    else:
        print("\n💥 COMPREHENSIVE TEST FAILED!")
        print("   Some packages may still have issues.")
    
    exit(0 if success else 1)
