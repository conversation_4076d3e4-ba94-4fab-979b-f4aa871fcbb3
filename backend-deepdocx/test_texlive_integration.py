#!/usr/bin/env python3
"""
Test script for TeX Live integration in DeepDocX
Tests the complete LaTeX compilation system with ALL packages
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.latex_service import LatexService

def test_texlive_detection():
    """Test TeX Live detection and environment setup"""
    print("=" * 60)
    print("TESTING TEX LIVE DETECTION")
    print("=" * 60)
    
    # Test TeX Live detection
    texlive_path = LatexService._detect_texlive_installation()
    print(f"TeX Live path: {texlive_path}")
    
    # Test environment setup
    env = LatexService._setup_texlive_environment()
    print(f"PATH: {env.get('PATH', 'Not set')[:100]}...")
    print(f"TEXMFHOME: {env.get('TEXMFHOME', 'Not set')}")
    
    return texlive_path is not None

def test_comprehensive_latex_compilation():
    """Test comprehensive LaTeX compilation with complex packages"""
    print("\n" + "=" * 60)
    print("TESTING COMPREHENSIVE LATEX COMPILATION")
    print("=" * 60)
    
    # Create a comprehensive test document with previously problematic packages
    test_latex = r"""
\documentclass[a4paper,12pt]{article}

% ALL PACKAGES - NO FILTERING
\usepackage{amsmath,amsfonts,amssymb,amsthm}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{tcolorbox}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{algorithmicx}
\usepackage{algpseudocode}
\usepackage{listings}
\usepackage{hyperref}
\usepackage{natbib}
\usepackage{lipsum}
\usepackage{titlesec}
\usepackage{fancyhdr}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{subcaption}
\usepackage{float}

\geometry{margin=1in}

% Custom colors and formatting
\definecolor{deepblue}{RGB}{0,50,150}
\definecolor{deepgreen}{RGB}{0,100,50}

\titleformat{\section}{\Large\bfseries\color{deepblue}}{\thesection}{1em}{}

\title{DeepDocX TeX Live Integration Test}
\author{Comprehensive Package Test}
\date{\today}

\begin{document}

\maketitle

\begin{abstract}
This document tests the complete TeX Live integration in DeepDocX, demonstrating support for ALL LaTeX packages without any filtering or removal.
\end{abstract}

\section{Mathematics and Symbols}
Here's some advanced mathematics using AMS packages:

\begin{align}
E &= mc^2 \\
\nabla \cdot \mathbf{E} &= \frac{\rho}{\epsilon_0} \\
\oint_{\partial S} \mathbf{B} \cdot d\mathbf{l} &= \mu_0 I_{enc}
\end{align}

\section{Graphics and TikZ}
\begin{tikzpicture}
\draw[thick,->] (0,0) -- (4,0) node[anchor=north west] {x};
\draw[thick,->] (0,0) -- (0,3) node[anchor=south east] {y};
\draw[thick,blue] (0,0) parabola (3,2);
\fill[red] (1,0.33) circle (2pt);
\end{tikzpicture}

\section{Colored Boxes with tcolorbox}
\begin{tcolorbox}[colback=blue!5!white,colframe=blue!75!black,title=Success!]
This tcolorbox environment works perfectly with the complete TeX Live installation. No package filtering means all advanced features are available.
\end{tcolorbox}

\begin{tcolorbox}[colback=deepgreen!10,colframe=deepgreen,title=Custom Colors]
Custom colors defined with xcolor package work seamlessly.
\end{tcolorbox}

\section{Algorithms}
\begin{algorithm}
\caption{Complete Package Support Algorithm}
\begin{algorithmic}[1]
\Procedure{CompileLatex}{$document$}
    \State $packages \gets$ DetectAllPackages($document$)
    \For{$package$ in $packages$}
        \If{$package$ not available}
            \State InstallPackage($package$)
        \EndIf
    \EndFor
    \State $result \gets$ CompileWithTexLive($document$)
    \State \Return $result$
\EndProcedure
\end{algorithmic}
\end{algorithm}

\section{Tables and Data}
\begin{table}[H]
\centering
\caption{Package Support Comparison}
\begin{tabular}{@{}lcc@{}}
\toprule
Feature & Before & After TeX Live \\
\midrule
Package Support & 62/66 & ALL \\
TikZ Graphics & ❌ & ✅ \\
tcolorbox & ❌ & ✅ \\
Algorithms & ❌ & ✅ \\
Auto-install & ❌ & ✅ \\
\bottomrule
\end{tabular}
\end{table}

\section{Code Listings}
\begin{lstlisting}[language=Python, caption=Python Code Example]
def compile_latex_with_texlive(document):
    # Compile LaTeX with full TeX Live support
    env = setup_texlive_environment()
    result = subprocess.run(['pdflatex', document], env=env)
    return result.returncode == 0
\end{lstlisting}

\section{Dummy Content}
\lipsum[1-2]

\section{Conclusion}
This document demonstrates that DeepDocX now supports:
\begin{itemize}
\item Complete TeX Live distribution
\item ALL LaTeX packages (no filtering)
\item Automatic package installation
\item Complex graphics with TikZ
\item Advanced formatting with tcolorbox
\item Professional algorithms
\item And much more!
\end{itemize}

The integration provides Overleaf-like capabilities with comprehensive package support.

\end{document}
"""

    print("Testing comprehensive LaTeX compilation...")
    result = LatexService.compile_latex('texlive-test', test_latex, 'test-user')
    
    print(f"Compilation result: {result['success']}")
    if result['success']:
        print(f"✅ SUCCESS: PDF URL: {result.get('pdf_url', 'N/A')}")
        return True
    else:
        print(f"❌ FAILED: Errors: {result.get('errors', [])}")
        return False

def main():
    """Run all TeX Live integration tests"""
    print("🚀 DEEPDOCX TEX LIVE INTEGRATION TEST")
    print("Testing complete LaTeX package support...")
    
    # Test 1: TeX Live Detection
    detection_success = test_texlive_detection()
    
    if not detection_success:
        print("\n❌ TeX Live not detected!")
        print("Please run: ./setup_texlive.sh")
        return False
    
    # Test 2: Comprehensive Compilation
    compilation_success = test_comprehensive_latex_compilation()
    
    # Final Results
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    
    if detection_success and compilation_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ TeX Live integration successful")
        print("✅ Complete package support available")
        print("✅ DeepDocX now supports ALL LaTeX packages like Overleaf")
        return True
    else:
        print("💥 SOME TESTS FAILED")
        print(f"   TeX Live Detection: {'✅' if detection_success else '❌'}")
        print(f"   Compilation Test: {'✅' if compilation_success else '❌'}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
