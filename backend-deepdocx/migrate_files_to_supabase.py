#!/usr/bin/env python3
"""
Migration script to move existing local files to Supabase Storage
Run this script after setting up Supabase Storage buckets
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables first
load_dotenv()

# Import Flask app and services
from app import create_app
from services.supabase_client import get_service_supabase
from services.file_service import FileService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Flask app instance
app = create_app()

def migrate_files():
    """Migrate all local files to Supabase Storage"""
    with app.app_context():
        try:
            supabase = get_service_supabase()

            # Get all files from database
            result = supabase.table('files').select('*').execute()
            files = result.data

            logger.info(f"Found {len(files)} files to migrate")

            migrated_count = 0
            error_count = 0

            for file_record in files:
                try:
                    file_id = file_record['id']
                    user_id = file_record['user_id']
                    storage_path = file_record.get('storage_path')
                    file_name = file_record['name']
                    file_type = file_record['type']

                    # Skip if already migrated (storage_path doesn't contain local path)
                    if storage_path and not storage_path.startswith(('uploads/', '/')) and 'users/' in storage_path:
                        logger.info(f"File {file_name} already migrated, skipping")
                        continue

                    # Check if local file exists
                    if not storage_path or not os.path.exists(storage_path):
                        logger.warning(f"Local file not found for {file_name}: {storage_path}")
                        continue

                    # Read local file
                    with open(storage_path, 'rb') as f:
                        file_data = f.read()

                    # Generate new storage path for Supabase
                    file_extension = file_name.split('.')[-1] if '.' in file_name else file_type
                    unique_filename = f"{file_record.get('metadata', {}).get('unique_name', file_id)}.{file_extension}"
                    new_storage_path = f"users/{user_id}/files/{unique_filename}"

                    # Upload to Supabase Storage
                    try:
                        upload_result = supabase.storage.from_('files').upload(
                            new_storage_path,
                            file_data,
                            file_options={
                                'content-type': FileService._get_content_type(file_type),
                                'cache-control': '3600'
                            }
                        )

                        if hasattr(upload_result, 'error') and upload_result.error:
                            logger.error(f"Failed to upload {file_name}: {upload_result.error}")
                            error_count += 1
                            continue

                    except Exception as upload_error:
                        logger.error(f"Failed to upload {file_name}: {upload_error}")
                        error_count += 1
                        continue

                    # Get public URL
                    try:
                        public_url_result = supabase.storage.from_('files').get_public_url(new_storage_path)
                        if hasattr(public_url_result, 'data') and public_url_result.data:
                            new_url = public_url_result.data.public_url
                        else:
                            # Fallback: construct URL manually
                            new_url = f"{supabase.supabase_url}/storage/v1/object/public/files/{new_storage_path}"

                        if not new_url:
                            logger.error(f"Failed to get public URL for {file_name}")
                            error_count += 1
                            continue

                    except Exception as url_error:
                        logger.warning(f"Error getting public URL for {file_name}, using fallback: {url_error}")
                        # Fallback: construct URL manually
                        new_url = f"{supabase.supabase_url}/storage/v1/object/public/files/{new_storage_path}"

                    # Update database record
                    update_data = {
                        'url': new_url,
                        'storage_path': new_storage_path,
                        'metadata': {
                            **file_record.get('metadata', {}),
                            'storage_bucket': 'files',
                            'migrated_from_local': True
                        }
                    }

                    update_result = supabase.table('files').update(update_data).eq('id', file_id).execute()

                    if hasattr(update_result, 'error') and update_result.error:
                        logger.error(f"Failed to update database for {file_name}: {update_result.error}")
                        error_count += 1
                        continue

                    logger.info(f"Successfully migrated {file_name}")
                    migrated_count += 1

                except Exception as e:
                    logger.error(f"Error migrating file {file_record.get('name', 'unknown')}: {e}")
                    error_count += 1

            logger.info(f"Migration completed: {migrated_count} files migrated, {error_count} errors")

            if error_count == 0:
                logger.info("All files migrated successfully!")
                logger.info("You can now safely remove the local uploads directory")
            else:
                logger.warning(f"Migration completed with {error_count} errors. Check logs above.")

        except Exception as e:
            logger.error(f"Migration failed: {e}")
            sys.exit(1)

def verify_migration():
    """Verify that all files have been migrated correctly"""
    with app.app_context():
        try:
            supabase = get_service_supabase()

            # Get all files from database
            result = supabase.table('files').select('*').execute()
            files = result.data

            logger.info(f"Verifying {len(files)} files...")

            local_files = 0
            migrated_files = 0

            for file_record in files:
                storage_path = file_record.get('storage_path', '')

                if storage_path.startswith(('uploads/', '/')):
                    local_files += 1
                    logger.warning(f"File still using local storage: {file_record['name']}")
                elif 'users/' in storage_path:
                    migrated_files += 1
                else:
                    logger.warning(f"File has unknown storage path: {file_record['name']} - {storage_path}")

            logger.info(f"Verification complete: {migrated_files} migrated, {local_files} still local")

            if local_files == 0:
                logger.info("✅ All files successfully migrated to Supabase Storage!")
            else:
                logger.warning(f"⚠️  {local_files} files still need migration")

        except Exception as e:
            logger.error(f"Verification failed: {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'verify':
        verify_migration()
    else:
        migrate_files()
