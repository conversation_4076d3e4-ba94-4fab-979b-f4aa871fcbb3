#!/usr/bin/env python3
"""
Manual migration script for the large PDF file
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables first
load_dotenv()

# Import Flask app and services
from app import create_app
from services.supabase_client import get_service_supabase
from services.file_service import FileService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app instance
app = create_app()

def migrate_pdf():
    """Manually migrate the PDF file"""
    with app.app_context():
        try:
            supabase = get_service_supabase()
            
            # Find the PDF file in database
            result = supabase.table('files').select('*').ilike('name', '%pdf%').execute()
            pdf_files = result.data
            
            logger.info(f"Found {len(pdf_files)} PDF files in database")
            
            for file_record in pdf_files:
                file_name = file_record['name']
                storage_path = file_record.get('storage_path')
                
                # Skip if already migrated
                if storage_path and 'users/' in storage_path:
                    logger.info(f"PDF {file_name} already migrated, skipping")
                    continue
                
                if not storage_path or not os.path.exists(storage_path):
                    logger.warning(f"PDF file not found: {file_name} at {storage_path}")
                    continue
                
                logger.info(f"Migrating PDF: {file_name}")
                logger.info(f"File size: {os.path.getsize(storage_path) / (1024*1024):.1f} MB")
                
                # Read file in chunks to avoid memory issues
                file_data = b''
                with open(storage_path, 'rb') as f:
                    while True:
                        chunk = f.read(1024 * 1024)  # 1MB chunks
                        if not chunk:
                            break
                        file_data += chunk
                
                logger.info(f"Read {len(file_data)} bytes")
                
                # Generate new storage path
                user_id = file_record['user_id']
                file_id = file_record['id']
                file_type = file_record['type']
                file_extension = file_name.split('.')[-1] if '.' in file_name else file_type
                unique_filename = f"{file_record.get('metadata', {}).get('unique_name', file_id)}.{file_extension}"
                new_storage_path = f"users/{user_id}/files/{unique_filename}"
                
                logger.info(f"Uploading to: {new_storage_path}")
                
                # Upload with longer timeout
                try:
                    upload_result = supabase.storage.from_('files').upload(
                        new_storage_path,
                        file_data,
                        file_options={
                            'content-type': 'application/pdf',
                            'cache-control': '3600'
                        }
                    )
                    
                    logger.info("Upload completed successfully")
                    
                    # Get public URL
                    try:
                        public_url_result = supabase.storage.from_('files').get_public_url(new_storage_path)
                        if hasattr(public_url_result, 'data') and public_url_result.data:
                            new_url = public_url_result.data.public_url
                        else:
                            new_url = f"{supabase.supabase_url}/storage/v1/object/public/files/{new_storage_path}"
                    except Exception as url_error:
                        logger.warning(f"Error getting public URL, using fallback: {url_error}")
                        new_url = f"{supabase.supabase_url}/storage/v1/object/public/files/{new_storage_path}"
                    
                    # Update database
                    update_data = {
                        'url': new_url,
                        'storage_path': new_storage_path,
                        'metadata': {
                            **file_record.get('metadata', {}),
                            'storage_bucket': 'files',
                            'migrated_from_local': True
                        }
                    }
                    
                    update_result = supabase.table('files').update(update_data).eq('id', file_id).execute()
                    
                    logger.info(f"✅ Successfully migrated PDF: {file_name}")
                    logger.info(f"   New URL: {new_url}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to migrate PDF {file_name}: {e}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            return False

if __name__ == '__main__':
    success = migrate_pdf()
    if not success:
        sys.exit(1)
