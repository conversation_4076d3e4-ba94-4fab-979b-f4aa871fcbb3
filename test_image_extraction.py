#!/usr/bin/env python3
"""
Test script for image extraction functionality
"""

import sys
import os
sys.path.append('backend-deepdocx')

from services.latex_service import LatexService
import tempfile
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_image_extraction():
    """Test the image extraction and copying functionality"""
    
    # Sample LaTeX code with different image reference styles
    latex_code = """
\\documentclass{article}
\\usepackage{graphicx}
\\begin{document}

\\section{Test Images}

% Direct filename reference
\\includegraphics{database.jpg}

% Folder path reference
\\includegraphics{images/database.jpg}

% @mention style reference
\\includegraphics{@images/database.jpg}

% Another folder structure
\\includegraphics{@figures/chart.png}

\\end{document}
"""
    
    print("Testing image extraction with LaTeX code:")
    print(latex_code)
    print("\n" + "="*50 + "\n")
    
    # Test the regex pattern
    import re
    image_pattern = r'\\includegraphics(?:\[[^\]]*\])?\{([^}]+)\}'
    matches = re.findall(image_pattern, latex_code)
    
    print("Found image references:")
    for i, match in enumerate(matches, 1):
        print(f"{i}. {match}")
    
    print("\n" + "="*50 + "\n")
    
    # Test path parsing
    for image_path in matches:
        print(f"Processing: {image_path}")
        
        filename = None
        folder_names = []
        
        if image_path.startswith('@'):
            # Handle @mention style references
            clean_path = image_path[1:]
            path_parts = clean_path.split('/')
            filename = path_parts[-1]
            if len(path_parts) > 1:
                folder_names = path_parts[:-1]
        elif '/' in image_path:
            # Handle folder/image.png style references
            path_parts = image_path.split('/')
            filename = path_parts[-1]
            folder_names = path_parts[:-1]
        else:
            # Handle direct filename references
            filename = image_path
        
        print(f"  Filename: {filename}")
        print(f"  Folder path: {'/'.join(folder_names) if folder_names else 'root'}")
        print()

def test_latex_enhancement():
    """Test the LaTeX enhancement for missing images"""
    
    latex_code = """
\\documentclass{article}
\\begin{document}
\\includegraphics{missing.jpg}
\\includegraphics[width=0.5\\textwidth]{another.png}
\\end{document}
"""
    
    print("Testing LaTeX enhancement:")
    print("Original:")
    print(latex_code)
    
    enhanced = LatexService._enhance_latex_for_missing_images(latex_code)
    
    print("\nEnhanced:")
    print(enhanced)

if __name__ == "__main__":
    print("Image Extraction Test")
    print("=" * 50)
    
    test_image_extraction()
    
    print("\n" + "="*50 + "\n")
    
    test_latex_enhancement()
