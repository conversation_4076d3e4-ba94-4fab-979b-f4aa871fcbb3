"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "@/components/ui/use-toast"
import { User, Bell, Shield, Palette, Globe, Download, Trash2, Camera, Loader2 } from "lucide-react"
import { useAuth } from "@/lib/contexts/auth-context"
import { apiClient } from "@/lib/api/client"

interface SettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SettingsDialog({ open, onOpenChange }: SettingsDialogProps) {
  const [activeTab, setActiveTab] = useState("profile")
  const [loading, setLoading] = useState(false)
  const [profileData, setProfileData] = useState({
    first_name: "",
    last_name: "",
    bio: "",
    company: "",
    role: "",
  })
  const [passwordData, setPasswordData] = useState({
    current_password: "",
    new_password: "",
    confirm_password: "",
  })
  const [settings, setSettings] = useState({
    theme: "dark",
    fontSize: "medium",
    language: "en",
    notifications: {
      documentShared: true,
      commentsAndMentions: true,
      documentUpdates: false,
      weeklySummary: false,
      realtimeCollaboration: true,
      documentCompilation: true,
    },
    preferences: {
      autoSave: true,
      showLineNumbers: true,
      defaultTemplate: "article",
      defaultExportFormat: "pdf",
      includeMetadata: true,
    },
  })
  const [passwordLoading, setPasswordLoading] = useState(false)
  const [settingsLoading, setSettingsLoading] = useState(false)
  const { user, refreshUser } = useAuth()

  useEffect(() => {
    if (user) {
      setProfileData({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        bio: user.bio || "",
        company: user.company || "",
        role: user.role || "",
      })
    }
  }, [user])

  useEffect(() => {
    if (open) {
      loadSettings()
    }
  }, [open])

  const loadSettings = async () => {
    try {
      const userSettings = await apiClient.users.getSettings()
      setSettings(prev => ({ ...prev, ...userSettings }))
    } catch (error) {
      console.error("Failed to load settings:", error)
    }
  }

  const handleProfileUpdate = async () => {
    try {
      setLoading(true)
      await apiClient.users.updateProfile(profileData)
      await refreshUser()
      toast({
        title: "Profile updated",
        description: "Your profile information has been saved successfully.",
      })
    } catch (error) {
      console.error("Failed to update profile:", error)
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordChange = async () => {
    if (passwordData.new_password !== passwordData.confirm_password) {
      toast({
        title: "Error",
        description: "New passwords do not match.",
        variant: "destructive",
      })
      return
    }

    if (passwordData.new_password.length < 8) {
      toast({
        title: "Error",
        description: "Password must be at least 8 characters long.",
        variant: "destructive",
      })
      return
    }

    try {
      setPasswordLoading(true)
      await apiClient.users.changePassword(passwordData.current_password, passwordData.new_password)
      setPasswordData({
        current_password: "",
        new_password: "",
        confirm_password: "",
      })
      toast({
        title: "Password changed",
        description: "Your password has been updated successfully.",
      })
    } catch (error) {
      console.error("Failed to change password:", error)
      toast({
        title: "Error",
        description: "Failed to change password. Please check your current password.",
        variant: "destructive",
      })
    } finally {
      setPasswordLoading(false)
    }
  }

  const handleSettingsUpdate = async (newSettings: any) => {
    try {
      setSettingsLoading(true)
      await apiClient.users.updateSettings(newSettings)
      setSettings(prev => ({ ...prev, ...newSettings }))
      toast({
        title: "Settings updated",
        description: "Your preferences have been saved successfully.",
      })
    } catch (error) {
      console.error("Failed to update settings:", error)
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSettingsLoading(false)
    }
  }

  const getUserInitials = () => {
    if (!user) return "U"
    return `${user.first_name?.[0] || ""}${user.last_name?.[0] || ""}`.toUpperCase()
  }

  const tabs = [
    { id: "profile", label: "Profile", icon: <User className="w-4 h-4" /> },
    { id: "appearance", label: "Appearance", icon: <Palette className="w-4 h-4" /> },
    { id: "notifications", label: "Notifications", icon: <Bell className="w-4 h-4" /> },
    { id: "security", label: "Security", icon: <Shield className="w-4 h-4" /> },
    { id: "preferences", label: "Preferences", icon: <Globe className="w-4 h-4" /> },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-foreground">Settings</DialogTitle>
          <DialogDescription className="text-muted-foreground">Manage your account settings and preferences</DialogDescription>
        </DialogHeader>

        <div className="flex h-[500px]">
          {/* Sidebar */}
          <div className="w-64 border-r border-border pr-6">
            <nav className="space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg ${
                    activeTab === tab.id
                      ? "bg-primary/20 text-primary border border-primary/30"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  }`}
                >
                  {tab.icon}
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 pl-6 overflow-y-auto">
            {activeTab === "profile" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Profile Information</h3>

                  <div className="flex items-center gap-6 mb-6">
                    <div className="relative">
                      <Avatar className="w-20 h-20">
                        <AvatarImage src={user?.avatar_url} />
                        <AvatarFallback className="bg-gradient-to-r from-primary to-secondary text-primary-foreground text-xl font-semibold">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                      <Button
                        size="icon"
                        className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full bg-primary hover:bg-primary/90 text-primary-foreground"
                      >
                        <Camera className="w-4 h-4" />
                      </Button>
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground">
                        {user ? `${user.first_name || ""} ${user.last_name || ""}`.trim() || user.email : "User"}
                      </h4>
                      <p className="text-sm text-muted-foreground">{user?.email}</p>
                      <p className="text-sm text-muted-foreground">{user?.plan_type || "Free"} Plan</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 border-border text-foreground hover:bg-muted"
                      >
                        Change Photo
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-foreground">
                        First Name
                      </Label>
                      <Input
                        id="firstName"
                        value={profileData.first_name}
                        onChange={(e) => setProfileData(prev => ({ ...prev, first_name: e.target.value }))}
                        className="mt-1 bg-background border-border text-foreground"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-foreground">
                        Last Name
                      </Label>
                      <Input
                        id="lastName"
                        value={profileData.last_name}
                        onChange={(e) => setProfileData(prev => ({ ...prev, last_name: e.target.value }))}
                        className="mt-1 bg-background border-border text-foreground"
                      />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="email" className="text-foreground">
                        Email
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        value={user?.email || ""}
                        disabled
                        className="mt-1 bg-muted border-border text-muted-foreground"
                      />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="bio" className="text-foreground">
                        Bio
                      </Label>
                      <Textarea
                        id="bio"
                        value={profileData.bio}
                        onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                        placeholder="Tell us about yourself..."
                        className="mt-1 bg-background border-border text-foreground placeholder:text-muted-foreground"
                        rows={3}
                      />
                    </div>
                  </div>
                </div>

                <Separator className="bg-border" />

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Organization</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="company" className="text-foreground">
                        Company
                      </Label>
                      <Input
                        id="company"
                        value={profileData.company}
                        onChange={(e) => setProfileData(prev => ({ ...prev, company: e.target.value }))}
                        placeholder="Your company"
                        className="mt-1 bg-background border-border text-foreground placeholder:text-muted-foreground"
                      />
                    </div>
                    <div>
                      <Label htmlFor="role" className="text-foreground">
                        Role
                      </Label>
                      <Input
                        id="role"
                        value={profileData.role}
                        onChange={(e) => setProfileData(prev => ({ ...prev, role: e.target.value }))}
                        placeholder="Your role"
                        className="mt-1 bg-background border-border text-foreground placeholder:text-muted-foreground"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={handleProfileUpdate}
                    disabled={loading}
                    className="bg-primary hover:bg-primary/90 text-primary-foreground"
                  >
                    {loading ? "Saving..." : "Save Changes"}
                  </Button>
                </div>
              </div>
            )}

            {activeTab === "appearance" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Theme</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-base font-medium text-foreground">Dark Mode</Label>
                        <p className="text-sm text-muted-foreground">Currently using dark theme</p>
                      </div>
                      <Switch checked={settings.theme === "dark"} disabled className="opacity-50" />
                    </div>
                  </div>
                </div>

                <Separator className="bg-border" />

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Display</h3>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="fontSize" className="text-foreground">
                        Font Size
                      </Label>
                      <Select
                        value={settings.fontSize}
                        onValueChange={(value) => handleSettingsUpdate({ fontSize: value })}
                      >
                        <SelectTrigger className="mt-1 bg-background border-border text-foreground">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-card border-border">
                          <SelectItem value="small">Small</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="large">Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="language" className="text-foreground">
                        Language
                      </Label>
                      <Select
                        value={settings.language}
                        onValueChange={(value) => handleSettingsUpdate({ language: value })}
                      >
                        <SelectTrigger className="mt-1 bg-background border-border text-foreground">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-card border-border">
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="es">Español</SelectItem>
                          <SelectItem value="fr">Français</SelectItem>
                          <SelectItem value="de">Deutsch</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "notifications" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Email Notifications</h3>
                  <div className="space-y-4">
                    {[
                      { key: "documentShared", label: "Document shared with me", description: "Get notified when someone shares a document" },
                      {
                        key: "commentsAndMentions",
                        label: "Comments and mentions",
                        description: "Get notified when someone comments or mentions you",
                      },
                      { key: "documentUpdates", label: "Document updates", description: "Get notified when a shared document is updated" },
                      { key: "weeklySummary", label: "Weekly summary", description: "Get a weekly summary of your activity" },
                    ].map((item) => (
                      <div key={item.key} className="flex items-center justify-between">
                        <div>
                          <Label className="text-base font-medium text-foreground">{item.label}</Label>
                          <p className="text-sm text-muted-foreground">{item.description}</p>
                        </div>
                        <Switch
                          checked={settings.notifications[item.key as keyof typeof settings.notifications]}
                          onCheckedChange={(checked) =>
                            handleSettingsUpdate({
                              notifications: { ...settings.notifications, [item.key]: checked }
                            })
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <Separator className="bg-border" />

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Push Notifications</h3>
                  <div className="space-y-4">
                    {[
                      { key: "realtimeCollaboration", label: "Real-time collaboration", description: "Get notified of live edits and comments" },
                      {
                        key: "documentCompilation",
                        label: "Document compilation",
                        description: "Get notified when document compilation completes",
                      },
                    ].map((item) => (
                      <div key={item.key} className="flex items-center justify-between">
                        <div>
                          <Label className="text-base font-medium text-foreground">{item.label}</Label>
                          <p className="text-sm text-muted-foreground">{item.description}</p>
                        </div>
                        <Switch
                          checked={settings.notifications[item.key as keyof typeof settings.notifications]}
                          onCheckedChange={(checked) =>
                            handleSettingsUpdate({
                              notifications: { ...settings.notifications, [item.key]: checked }
                            })
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === "security" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Password</h3>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="currentPassword" className="text-foreground">
                        Current Password
                      </Label>
                      <Input
                        id="currentPassword"
                        type="password"
                        value={passwordData.current_password}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, current_password: e.target.value }))}
                        className="mt-1 bg-background border-border text-foreground"
                      />
                    </div>
                    <div>
                      <Label htmlFor="newPassword" className="text-foreground">
                        New Password
                      </Label>
                      <Input
                        id="newPassword"
                        type="password"
                        value={passwordData.new_password}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, new_password: e.target.value }))}
                        className="mt-1 bg-background border-border text-foreground"
                      />
                    </div>
                    <div>
                      <Label htmlFor="confirmPassword" className="text-foreground">
                        Confirm New Password
                      </Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        value={passwordData.confirm_password}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, confirm_password: e.target.value }))}
                        className="mt-1 bg-background border-border text-foreground"
                      />
                    </div>
                    <Button
                      onClick={handlePasswordChange}
                      disabled={passwordLoading || !passwordData.current_password || !passwordData.new_password || !passwordData.confirm_password}
                      className="bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      {passwordLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        "Update Password"
                      )}
                    </Button>
                  </div>
                </div>

                <Separator className="bg-border" />

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Two-Factor Authentication</h3>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base font-medium text-foreground">Enable 2FA</Label>
                      <p className="text-sm text-muted-foreground">Add an extra layer of security to your account</p>
                    </div>
                    <Switch disabled />
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">Two-factor authentication will be available in a future update.</p>
                </div>

                <Separator className="bg-border" />

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Active Sessions</h3>
                  <div className="space-y-3">
                    {[
                      { device: "Current Browser", location: "Current Session", current: true },
                    ].map((session, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border border-border rounded-lg bg-muted/50"
                      >
                        <div>
                          <p className="font-medium text-foreground">{session.device}</p>
                          <p className="text-sm text-muted-foreground">{session.location}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          {session.current && (
                            <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded border border-primary/30">
                              Current
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === "preferences" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Editor Preferences</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-base font-medium text-foreground">Auto-save</Label>
                        <p className="text-sm text-muted-foreground">Automatically save changes as you type</p>
                      </div>
                      <Switch
                        checked={settings.preferences.autoSave}
                        onCheckedChange={(checked) =>
                          handleSettingsUpdate({
                            preferences: { ...settings.preferences, autoSave: checked }
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-base font-medium text-foreground">Show line numbers</Label>
                        <p className="text-sm text-muted-foreground">Display line numbers in the LaTeX editor</p>
                      </div>
                      <Switch
                        checked={settings.preferences.showLineNumbers}
                        onCheckedChange={(checked) =>
                          handleSettingsUpdate({
                            preferences: { ...settings.preferences, showLineNumbers: checked }
                          })
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="defaultTemplate" className="text-foreground">
                        Default Template
                      </Label>
                      <Select
                        value={settings.preferences.defaultTemplate}
                        onValueChange={(value) =>
                          handleSettingsUpdate({
                            preferences: { ...settings.preferences, defaultTemplate: value }
                          })
                        }
                      >
                        <SelectTrigger className="mt-1 bg-background border-border text-foreground">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-card border-border">
                          <SelectItem value="article">Article</SelectItem>
                          <SelectItem value="report">Report</SelectItem>
                          <SelectItem value="book">Book</SelectItem>
                          <SelectItem value="letter">Letter</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator className="bg-border" />

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-foreground">Export Settings</h3>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="defaultFormat" className="text-foreground">
                        Default Export Format
                      </Label>
                      <Select
                        value={settings.preferences.defaultExportFormat}
                        onValueChange={(value) =>
                          handleSettingsUpdate({
                            preferences: { ...settings.preferences, defaultExportFormat: value }
                          })
                        }
                      >
                        <SelectTrigger className="mt-1 bg-background border-border text-foreground">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-card border-border">
                          <SelectItem value="pdf">PDF</SelectItem>
                          <SelectItem value="docx">Word Document</SelectItem>
                          <SelectItem value="html">HTML</SelectItem>
                          <SelectItem value="tex">LaTeX Source</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-base font-medium text-foreground">Include metadata</Label>
                        <p className="text-sm text-muted-foreground">Include document metadata in exports</p>
                      </div>
                      <Switch
                        checked={settings.preferences.includeMetadata}
                        onCheckedChange={(checked) =>
                          handleSettingsUpdate({
                            preferences: { ...settings.preferences, includeMetadata: checked }
                          })
                        }
                      />
                    </div>
                  </div>
                </div>

                <Separator className="bg-border" />

                <div>
                  <h3 className="text-lg font-semibold mb-4 text-destructive">Danger Zone</h3>
                  <div className="space-y-4">
                    <div className="p-4 border border-destructive/30 rounded-lg bg-destructive/10">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-base font-medium text-destructive">Export Data</Label>
                          <p className="text-sm text-muted-foreground">Download all your documents and data</p>
                        </div>
                        <Button variant="outline" className="border-destructive text-destructive hover:bg-destructive/10" disabled>
                          <Download className="w-4 h-4 mr-2" />
                          Export
                        </Button>
                      </div>
                    </div>

                    <div className="p-4 border border-destructive/30 rounded-lg bg-destructive/10">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-base font-medium text-destructive">Delete Account</Label>
                          <p className="text-sm text-muted-foreground">Permanently delete your account and all data</p>
                        </div>
                        <Button variant="destructive" disabled>
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete Account
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">Data export and account deletion will be available in a future update.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-6 border-t border-border">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-border text-foreground hover:bg-muted"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
