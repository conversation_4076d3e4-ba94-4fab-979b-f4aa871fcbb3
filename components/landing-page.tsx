"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  FileText,
  Code,
  Share,
  Zap,
  Users,
  Shield,
  ArrowRight,
  CheckCircle,
  Star,
  Globe,
  Sparkles,
  Download,
  Eye,
  RotateCcw,
  Menu,
  X,
} from "lucide-react"
import Link from "next/link"

const features = [
  {
    icon: <Zap className="w-8 h-8" />,
    title: "AI-Powered Writing",
    description: "Generate professional documents with advanced AI assistance and intelligent content suggestions.",
  },
  {
    icon: <Code className="w-8 h-8" />,
    title: "LaTeX Support",
    description: "Professional typesetting with full LaTeX support, real-time compilation, and syntax highlighting.",
  },
  {
    icon: <Users className="w-8 h-8" />,
    title: "Real-time Collaboration",
    description: "Work together seamlessly with live editing, comments, and version control for teams.",
  },
  {
    icon: <Share className="w-8 h-8" />,
    title: "Easy Sharing",
    description: "Share documents instantly with customizable permissions and professional presentation modes.",
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: "Enterprise Security",
    description: "Bank-level encryption, SSO integration, and compliance with industry security standards.",
  },
  {
    icon: <Globe className="w-8 h-8" />,
    title: "Cloud Sync",
    description: "Access your documents anywhere with automatic cloud synchronization and offline support.",
  },
]

const testimonials = [
  {
    name: "Dr. Sarah Chen",
    role: "Research Director, MIT",
    content: "DeepDocX has revolutionized how our research team creates and collaborates on academic papers.",
    rating: 5,
  },
  {
    name: "Michael Rodriguez",
    role: "Technical Writer, Google",
    content: "The LaTeX integration is seamless. It's like having a professional typesetter built into the platform.",
    rating: 5,
  },
  {
    name: "Emily Johnson",
    role: "PhD Student, Stanford",
    content: "The AI assistance helped me write my thesis 3x faster while maintaining academic rigor.",
    rating: 5,
  },
]

const pricingPlans = [
  {
    name: "Free",
    price: "$0",
    period: "forever",
    description: "Perfect for students and individual researchers",
    features: ["5 documents per month", "Basic AI assistance", "PDF export", "Community support"],
    cta: "Get Started",
    popular: false,
  },
  {
    name: "Pro",
    price: "$19",
    period: "per month",
    description: "Ideal for professionals and small teams",
    features: [
      "Unlimited documents",
      "Advanced AI features",
      "LaTeX editor",
      "Real-time collaboration",
      "Priority support",
      "Custom templates",
    ],
    cta: "Start Free Trial",
    popular: true,
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "contact us",
    description: "For large organizations with advanced needs",
    features: [
      "Everything in Pro",
      "SSO integration",
      "Advanced security",
      "Custom integrations",
      "Dedicated support",
      "SLA guarantee",
    ],
    cta: "Contact Sales",
    popular: false,
  },
]

export function LandingPage() {
  const [email, setEmail] = useState("")
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="bg-background/95 backdrop-blur-xl border-b border-border sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <img src="/deepdocx_full.png" alt="deepdocx logo" className="h-10 w-auto mt-2" />
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-foreground hover:text-primary font-medium transition-colors">
                Features
              </a>
              <a href="#pricing" className="text-foreground hover:text-primary font-medium transition-colors">
                Pricing
              </a>
              <a href="#testimonials" className="text-foreground hover:text-primary font-medium transition-colors">
                Testimonials
              </a>
              <Link href="/auth/signin">
                <Button variant="outline" className="border-border text-foreground hover:bg-primary hover:text-primary-foreground">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  Get Started
                </Button>
              </Link>
            </div>

            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="text-foreground hover:text-white"
              >
                {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile menu */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 border-t border-border watercolor-wash">
              <div className="flex flex-col space-y-4">
                <a href="#features" className="text-foreground hover:text-primary font-medium handwritten-heading">
                  Features
                </a>
                <a href="#pricing" className="text-foreground hover:text-primary font-medium handwritten-heading">
                  Pricing
                </a>
                <a href="#testimonials" className="text-foreground hover:text-primary font-medium handwritten-heading">
                  Testimonials
                </a>
                <Link href="/auth/signin">
                  <Button variant="outline" className="w-full border-primary text-foreground hover:bg-primary hover:text-primary-foreground hand-drawn-border">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/signin">
                  <Button className="w-full organic-button">Get Started</Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-32 watercolor-wash">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="mb-4">
              <Badge className="bg-primary/20 text-primary border-primary hand-drawn-border">
                <Sparkles className="w-4 h-4 mr-2" />
                AI-Powered Document Creation
              </Badge>
            </div>

            <h1 className="text-6xl md:text-7xl font-bold text-foreground mb-8 leading-tight handwritten-heading">
              Professional Documents
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent block">
                Made Simple
              </span>
            </h1>

            <p className="text-xl text-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
              Create, edit, and collaborate on professional documents with AI assistance. From research papers to
              business reports, streamline your workflow with intelligent document generation and LaTeX support.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Link href="/auth/signin">
                <Button className="organic-button px-6 py-3 h-auto shadow-xl">
                  Start Creating Free
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
              <Button
                variant="outline"
                className="px-6 py-3 h-auto border-primary text-foreground hover:bg-primary hover:text-primary-foreground hand-drawn-border"
              >
                <Eye className="w-4 h-4 mr-2" />
                Watch Demo
              </Button>
            </div>

            {/* Hero Image/Demo */}
            <div className="relative max-w-5xl mx-auto">
              <div className="paper-card rounded-3xl shadow-2xl border border-border overflow-hidden">
                <div className="bg-background px-6 py-4 border-b border-border hand-drawn-border">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-destructive rounded-full"></div>
                    <div className="w-3 h-3 bg-chart-1 rounded-full"></div>
                    <div className="w-3 h-3 bg-primary rounded-full"></div>
                    <div className="ml-4 text-sm text-muted-foreground handwritten-heading">deepdocx - Professional Document Editor</div>
                  </div>
                </div>
                <div className="p-8">
                  <div className="grid grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-4 bg-muted rounded w-full"></div>
                      <div className="h-4 bg-muted rounded w-2/3"></div>
                    </div>
                    <div className="paper-card rounded-xl p-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-foreground mb-2 handwritten-heading">Research Paper</div>
                        <div className="text-muted-foreground mb-4">AI-Generated Content</div>
                        <div className="space-y-2">
                          <div className="h-3 bg-muted rounded"></div>
                          <div className="h-3 bg-muted rounded w-4/5"></div>
                          <div className="h-3 bg-muted rounded w-3/4"></div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <RotateCcw className="w-4 h-4 text-primary" />
                        <span className="text-sm text-foreground">Recompile</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Download className="w-4 h-4 text-primary" />
                        <span className="text-sm text-foreground">Export PDF</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Share className="w-4 h-4 text-primary" />
                        <span className="text-sm text-foreground">Share</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl font-bold text-foreground mb-4 handwritten-heading">Everything you need to create</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Powerful features designed for professionals, researchers, and teams who demand excellence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="paper-card border-border bg-background hover:bg-card transition-all duration-300 hand-drawn-border">
                <CardHeader>
                  <div className="w-16 h-16 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center mb-4 text-primary">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl text-foreground handwritten-heading">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-muted-foreground leading-relaxed">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-24 bg-background watercolor-wash">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl font-bold text-foreground mb-4 handwritten-heading">Trusted by professionals worldwide</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              See what researchers, writers, and teams are saying about deepdocx.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="paper-card border-border bg-card hand-drawn-border">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-chart-1 fill-current" />
                    ))}
                  </div>
                  <p className="text-foreground mb-6 leading-relaxed">"{testimonial.content}"</p>
                  <div>
                    <div className="font-semibold text-foreground handwritten-heading">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl font-bold text-foreground mb-4 handwritten-heading">Simple, transparent pricing</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Choose the plan that's right for you. Upgrade or downgrade at any time.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`paper-card border-border bg-background relative hand-drawn-border ${plan.popular ? "ring-2 ring-primary shadow-xl" : ""}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground px-4 py-1 hand-drawn-border">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl text-foreground handwritten-heading">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-foreground">{plan.price}</span>
                    <span className="text-muted-foreground ml-2">/{plan.period}</span>
                  </div>
                  <CardDescription className="mt-4 text-muted-foreground">{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-primary mr-3 flex-shrink-0" />
                        <span className="text-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link href="/auth/signin">
                    <Button
                      className={`w-full h-12 ${
                        plan.popular
                          ? "organic-button"
                          : "bg-muted hover:bg-primary hover:text-primary-foreground text-foreground hand-drawn-border"
                      }`}
                    >
                      {plan.cta}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-primary to-secondary watercolor-wash">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-primary-foreground mb-4 handwritten-heading">Ready to transform your document workflow?</h2>
          <p className="text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            Join thousands of professionals who trust deepdocx for their document creation needs.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <Input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="organic-input bg-background/90 border-primary text-foreground placeholder:text-muted-foreground"
            />
            <Link href="/auth/signin">
              <Button className="bg-background text-primary hover:bg-background/90 px-8 whitespace-nowrap hand-drawn-border">
                Get Started Free
              </Button>
            </Link>
          </div>

          <p className="text-sm text-primary-foreground/70 mt-4">No credit card required • 14-day free trial</p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-background text-foreground py-16 border-t border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <img src="/deepdocx_full.png" alt="deepdocx logo" className="w-40 h-150 mt-2 ml-8" />
              <p className="text-muted-foreground leading-relaxed">
                Professional document creation with AI assistance and LaTeX support.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-4 handwritten-heading">Product</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Templates
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Integrations
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4 handwritten-heading">Company</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Contact
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4 handwritten-heading">Support</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    API Reference
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Status
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-border mt-12 pt-8 flex flex-col sm:flex-row justify-between items-center">
            <p className="text-muted-foreground text-sm">© 2025 deepdocx. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 sm:mt-0">
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
