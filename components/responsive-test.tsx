"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function ResponsiveTest() {
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 })
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      setScreenSize({ width, height })
      setIsMobile(width < 768)
      setIsTablet(width >= 768 && width < 1024)
    }

    updateScreenSize()
    window.addEventListener('resize', updateScreenSize)
    return () => window.removeEventListener('resize', updateScreenSize)
  }, [])

  return (
    <div className="p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Responsive Dashboard Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-100 rounded">
              <h3 className="font-semibold">Screen Size</h3>
              <p>{screenSize.width} x {screenSize.height}</p>
            </div>
            <div className="p-4 bg-green-100 rounded">
              <h3 className="font-semibold">Device Type</h3>
              <p>{isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}</p>
            </div>
            <div className="p-4 bg-purple-100 rounded">
              <h3 className="font-semibold">Breakpoint</h3>
              <p className="block sm:hidden">XS (< 640px)</p>
              <p className="hidden sm:block md:hidden">SM (640px+)</p>
              <p className="hidden md:block lg:hidden">MD (768px+)</p>
              <p className="hidden lg:block xl:hidden">LG (1024px+)</p>
              <p className="hidden xl:block">XL (1280px+)</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-semibold">Responsive Features Test</h3>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button className="w-full sm:w-auto">Mobile: Full Width</Button>
              <Button variant="outline" className="w-full sm:w-auto">Desktop: Auto Width</Button>
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
            {Array.from({ length: 12 }, (_, i) => (
              <div key={i} className="aspect-square bg-gray-200 rounded flex items-center justify-center text-sm">
                {i + 1}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
