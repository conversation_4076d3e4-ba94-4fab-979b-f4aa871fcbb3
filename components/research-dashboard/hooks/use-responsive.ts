"use client"

import { useState, useEffect } from "react"
import type { ResponsiveState } from "../types"

export function useResponsive() {
  const [state, setState] = useState<ResponsiveState>({
    isMobile: false,
    isTablet: false,
    sidebarCollapsed: false,
    mobileMenuOpen: false,
    chatPanelCollapsed: false,
    chatPanelWidth: 510,
  })

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      const isMobile = width < 768
      const isTablet = width >= 768 && width < 1024

      setState(prev => ({
        ...prev,
        isMobile,
        isTablet,
        // Auto-collapse on mobile
        sidebarCollapsed: isMobile ? true : prev.sidebarCollapsed,
        chatPanelCollapsed: isMobile ? true : prev.chatPanelCollapsed,
        chatPanelWidth: isMobile ? 300 : isTablet ? 400 : 510,
      }))
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  const toggleMobileMenu = () => {
    setState(prev => ({ ...prev, mobileMenuOpen: !prev.mobileMenuOpen }))
  }

  const closeMobileMenu = () => {
    setState(prev => ({ ...prev, mobileMenuOpen: false }))
  }

  const toggleChatPanel = () => {
    setState(prev => ({ ...prev, chatPanelCollapsed: !prev.chatPanelCollapsed }))
  }

  const setChatPanelWidth = (width: number) => {
    setState(prev => ({ ...prev, chatPanelWidth: width }))
  }

  const toggleSidebar = () => {
    setState(prev => ({ ...prev, sidebarCollapsed: !prev.sidebarCollapsed }))
  }

  return {
    ...state,
    toggleMobileMenu,
    closeMobileMenu,
    toggleChatPanel,
    setChatPanelWidth,
    toggleSidebar,
  }
}
