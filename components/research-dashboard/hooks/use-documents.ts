"use client"

import { useState, useEffect } from "react"
import { toast } from "@/components/ui/use-toast"
import { apiClient } from "@/lib/api/client"
import type { Document, DocumentOperations, CompilationOperations, VersionOperations } from "../types"

export function useDocuments(): {
  documents: Document[]
  documentsLoading: boolean
  documentOperations: DocumentOperations
  compilationOperations: CompilationOperations
  versionOperations: VersionOperations
} {
  const [documents, setDocuments] = useState<Document[]>([])
  const [documentsLoading, setDocumentsLoading] = useState(true)
  const [versions, setVersions] = useState<any[]>([])
  const [versionsLoading, setVersionsLoading] = useState(false)
  const [isCompiling, setIsCompiling] = useState(false)
  const [compilationResult, setCompilationResult] = useState<any>(null)

  // Load documents on mount
  useEffect(() => {
    loadDocuments()
  }, [])

  const loadDocuments = async () => {
    try {
      setDocumentsLoading(true)
      const response = await apiClient.documents.list()
      setDocuments(response)
    } catch (error) {
      console.error("Error loading documents:", error)
      toast({
        title: "Error",
        description: "Failed to load documents",
        variant: "destructive",
      })
    } finally {
      setDocumentsLoading(false)
    }
  }

  // Real document operations
  const documentOperations: DocumentOperations = {
    createDocument: async (data: Partial<Document>) => {
      try {
        const response = await apiClient.documents.create({
          title: data.title,
          description: data.description,
          folder_id: data.folder_id,
          type: data.type || "research_paper",
        })

        const newDoc = response.document
        setDocuments(prev => [newDoc, ...prev])

        toast({
          title: "Success",
          description: "Document created successfully",
        })

        return newDoc
      } catch (error) {
        console.error("Error creating document:", error)
        toast({
          title: "Error",
          description: "Failed to create document",
          variant: "destructive",
        })
        throw error
      }
    },

    createDocumentWithMessage: async (data: {
      message: string;
      folder_id?: string;
      type?: string;
      referenced_files?: string[]
    }) => {
      try {
        const response = await apiClient.documents.createWithMessage(data)

        const newDoc = response.document
        setDocuments(prev => [newDoc, ...prev])

        toast({
          title: "Success",
          description: "Document created with AI assistance",
        })

        return response
      } catch (error) {
        console.error("Error creating document with message:", error)
        toast({
          title: "Error",
          description: "Failed to create document with AI assistance",
          variant: "destructive",
        })
        throw error
      }
    },

    updateDocument: async (id: string, data: Partial<Document>) => {
      try {
        const updatedDoc = await apiClient.documents.update(id, data)
        setDocuments(prev => prev.map(doc => doc.id === id ? updatedDoc : doc))

        toast({
          title: "Success",
          description: "Document updated successfully",
        })

        return updatedDoc
      } catch (error) {
        console.error("Error updating document:", error)
        toast({
          title: "Error",
          description: "Failed to update document",
          variant: "destructive",
        })
        throw error
      }
    },

    deleteDocument: async (id: string) => {
      try {
        await apiClient.documents.delete(id)
        setDocuments(prev => prev.filter(doc => doc.id !== id))

        toast({
          title: "Success",
          description: "Document deleted successfully",
        })
      } catch (error) {
        console.error("Error deleting document:", error)
        toast({
          title: "Error",
          description: "Failed to delete document",
          variant: "destructive",
        })
        throw error
      }
    },

    shareDocument: async (documentId: string, options: any) => {
      try {
        const shareData = await apiClient.share.create(documentId, {
          permissions: options.permissions || "view",
          expires_at: options.expires_at,
          shared_with: options.shared_with,
        })

        const shareUrl = `${window.location.origin}/share/${shareData.share_token}`

        toast({
          title: "Success",
          description: "Document shared successfully",
        })

        return { shareUrl, shareToken: shareData.share_token }
      } catch (error) {
        console.error("Error sharing document:", error)
        toast({
          title: "Error",
          description: "Failed to share document",
          variant: "destructive",
        })
        throw error
      }
    },
  }

  // Real version operations
  const versionOperations: VersionOperations = {
    versions,
    versionsLoading,
    createVersion: async (documentId: string, latexCode: string, description?: string) => {
      try {
        const newVersion = await apiClient.documents.createVersion(documentId, {
          latex_code: latexCode,
          description,
        })

        setVersions(prev => [newVersion, ...prev])

        toast({
          title: "Success",
          description: "Version created successfully",
        })

        return newVersion
      } catch (error) {
        console.error("Error creating version:", error)
        toast({
          title: "Error",
          description: "Failed to create version",
          variant: "destructive",
        })
        throw error
      }
    },
    switchToVersion: async (versionId: string) => {
      try {
        await apiClient.documents.setCurrentVersion(versionId)

        toast({
          title: "Success",
          description: "Switched to selected version",
        })
      } catch (error) {
        console.error("Error switching version:", error)
        toast({
          title: "Error",
          description: "Failed to switch version",
          variant: "destructive",
        })
        throw error
      }
    },
    loadVersions: async (documentId: string) => {
      try {
        setVersionsLoading(true)
        const documentVersions = await apiClient.documents.getVersions(documentId)
        setVersions(documentVersions)
      } catch (error) {
        console.error("Error loading versions:", error)
        toast({
          title: "Error",
          description: "Failed to load versions",
          variant: "destructive",
        })
      } finally {
        setVersionsLoading(false)
      }
    },
  }

  // Real compilation operations (handled by document chat)
  const compilationOperations: CompilationOperations = {
    isCompiling,
    compilationResult,
    compileLatex: async (documentId: string, latexCode: string) => {
      try {
        setIsCompiling(true)

        // LaTeX compilation is now handled through the document chat endpoint
        // This creates a new version and compiles it
        const result = await versionOperations.createVersion(documentId, latexCode, "Manual compilation")

        setCompilationResult({ success: true, version: result })

        toast({
          title: "Success",
          description: "Document compiled successfully",
        })

        return { success: true }
      } catch (error) {
        console.error("Error compiling LaTeX:", error)
        setCompilationResult({ success: false, error: error.message })

        toast({
          title: "Error",
          description: "Failed to compile document",
          variant: "destructive",
        })

        return { success: false }
      } finally {
        setIsCompiling(false)
      }
    },
    downloadPdf: async (documentId: string) => {
      try {
        // Get the current document version
        const docResponse = await apiClient.documents.get(documentId)
        const currentVersion = docResponse.current_version

        if (currentVersion?.compiled_pdf_url) {
          // Create download link
          const link = document.createElement('a')
          link.href = currentVersion.compiled_pdf_url
          link.download = `${docResponse.document.title}.pdf`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          toast({
            title: "Success",
            description: "PDF download started",
          })
        } else {
          toast({
            title: "Error",
            description: "No compiled PDF available",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error("Error downloading PDF:", error)
        toast({
          title: "Error",
          description: "Failed to download PDF",
          variant: "destructive",
        })
      }
    },
  }

  return {
    documents,
    documentsLoading,
    documentOperations,
    compilationOperations,
    versionOperations,
  }
}
