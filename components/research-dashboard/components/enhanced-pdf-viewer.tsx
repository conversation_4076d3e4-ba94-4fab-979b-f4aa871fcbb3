"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'
import {
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Download,
  Printer,
  Maximize2,
  Minimize2,
  CheckCircle,
  AlertCircle,
  Loader2,
  Search,
  FileText,
  ExternalLink
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

// Set up PDF.js worker - use local worker file that matches react-pdf version
if (typeof window !== 'undefined') {
  try {
    // Use local worker file that matches pdfjs-dist 4.8.69 (used by react-pdf 9.2.1)
    pdfjs.GlobalWorkerOptions.workerSrc = '/pdf-worker/pdf.worker.min.js'
  } catch (error) {
    // Fallback to CDN if local worker fails
    console.warn('Local PDF worker failed, falling back to CDN:', error)
    pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@4.8.69/build/pdf.worker.min.js`
  }
}

interface EnhancedPDFViewerProps {
  pdfUrl: string
  documentTitle: string
  onDownload: () => void
}

export const EnhancedPDFViewer: React.FC<EnhancedPDFViewerProps> = ({
  pdfUrl,
  documentTitle,
  onDownload
}) => {
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number>(1.0)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false)
  const [searchText, setSearchText] = useState<string>('')
  const [showSearch, setShowSearch] = useState<boolean>(false)
  const [retryCount, setRetryCount] = useState<number>(0)
  
  const containerRef = useRef<HTMLDivElement>(null)
  const documentRef = useRef<HTMLDivElement>(null)

  // Handle document load success
  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages)
    setIsLoading(false)
    setError(null)
    console.log('PDF loaded successfully with', numPages, 'pages')
  }, [])

  // Handle document load error
  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('PDF load error:', error)
    setIsLoading(false)

    // Provide user-friendly error messages
    let errorMessage = 'Failed to load PDF'
    if (error.message.includes('worker')) {
      errorMessage = 'PDF worker failed to load. Please refresh the page.'
    } else if (error.message.includes('network')) {
      errorMessage = 'Network error loading PDF. Please check your connection.'
    } else if (error.message.includes('Invalid PDF')) {
      errorMessage = 'Invalid PDF format. The file may be corrupted.'
    }

    setError(errorMessage)
  }, [])

  // Navigation functions with scroll-to-page
  const scrollToPage = (pageNum: number) => {
    const pageElement = document.querySelector(`[data-page-number="${pageNum}"]`)
    if (pageElement && containerRef.current) {
      pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  const goToPrevPage = () => {
    const newPage = Math.max(pageNumber - 1, 1)
    setPageNumber(newPage)
    scrollToPage(newPage)
  }

  const goToNextPage = () => {
    const newPage = Math.min(pageNumber + 1, numPages)
    setPageNumber(newPage)
    scrollToPage(newPage)
  }

  const goToPage = (page: number) => {
    const newPage = Math.max(1, Math.min(page, numPages))
    setPageNumber(newPage)
    scrollToPage(newPage)
  }

  // Zoom functions
  const zoomIn = () => setScale(prev => Math.min(prev + 0.25, 3.0))
  const zoomOut = () => setScale(prev => Math.max(prev - 0.25, 0.5))
  const resetZoom = () => setScale(1.0)
  const fitToWidth = () => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.clientWidth - 40 // padding
      setScale(containerWidth / 595) // A4 width in points
    }
  }

  // Fullscreen functions
  const toggleFullscreen = () => {
    if (!isFullscreen && documentRef.current) {
      if (documentRef.current.requestFullscreen) {
        documentRef.current.requestFullscreen()
      }
    } else if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }

  // Print function
  const handlePrint = () => {
    window.open(pdfUrl, '_blank')?.print()
  }

  // Retry function
  const handleRetry = () => {
    setError(null)
    setIsLoading(true)
    setRetryCount(prev => prev + 1)

    // If this is the second retry, try using CDN worker
    if (retryCount >= 1) {
      pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@4.8.69/build/pdf.worker.min.js`
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return // Don't interfere with input fields

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault()
          goToPrevPage()
          break
        case 'ArrowRight':
          e.preventDefault()
          goToNextPage()
          break
        case '+':
        case '=':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            zoomIn()
          }
          break
        case '-':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            zoomOut()
          }
          break
        case '0':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            resetZoom()
          }
          break
        case 'f':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            setShowSearch(!showSearch)
          }
          break
        case 'F11':
          e.preventDefault()
          toggleFullscreen()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [numPages, showSearch])

  // Fullscreen change listener
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // Scroll listener to update current page
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return

      const container = containerRef.current
      const pages = container.querySelectorAll('[data-page-number]')
      const containerTop = container.scrollTop
      const containerHeight = container.clientHeight

      // Find the page that's most visible in the viewport
      let mostVisiblePage = 1
      let maxVisibleArea = 0

      pages.forEach((page) => {
        const pageElement = page as HTMLElement
        const pageTop = pageElement.offsetTop - container.offsetTop
        const pageBottom = pageTop + pageElement.offsetHeight

        // Calculate visible area of this page
        const visibleTop = Math.max(containerTop, pageTop)
        const visibleBottom = Math.min(containerTop + containerHeight, pageBottom)
        const visibleArea = Math.max(0, visibleBottom - visibleTop)

        if (visibleArea > maxVisibleArea) {
          maxVisibleArea = visibleArea
          mostVisiblePage = parseInt(pageElement.getAttribute('data-page-number') || '1')
        }
      })

      setPageNumber(mostVisiblePage)
    }

    const container = containerRef.current
    if (container) {
      container.addEventListener('scroll', handleScroll)
      return () => container.removeEventListener('scroll', handleScroll)
    }
  }, [numPages])

  return (
    <div className="organic-pdf-viewer" ref={documentRef}>
      {/* Simplified Header - Just document info */}
      <div className="paper-card !p-2 mb-1">
        <div className="flex items-center gap-3">
          <div className="compilation-status success">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm font-medium">PDF Ready</span>
          </div>
          <div className="text-xs text-muted-foreground truncate">
            📄 {documentTitle}.pdf
          </div>
          {numPages > 0 && (
            <div className="text-xs text-muted-foreground">
              {numPages} page{numPages !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>

      {/* Main PDF Viewer Layout - PDF + Right side controls */}
      <div className="flex gap-3 h-[calc(100vh-8rem)]">
        {/* PDF Document Container - Main area */}
        <div className="flex-1">
          <div
            ref={containerRef}
            className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-auto shadow-inner border-2 border-sage-green/20 h-full"
          >

            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center pdf-loading-overlay z-10">
                <div className="text-center paper-card !p-6">
                  <div className="hand-drawn-spinner w-10 h-10 mx-auto mb-4" />
                  <h3 className="handwritten-heading text-lg mb-2">Loading your document...</h3>
                  <p className="text-sm text-muted-foreground">
                    Preparing professional PDF with LaTeX typesetting ✨
                  </p>
                </div>
              </div>
            )}

            {error && (
              <div className="absolute inset-0 flex items-center justify-center pdf-loading-overlay z-10">
                <div className="text-center paper-card !p-6">
                  <AlertCircle className="w-12 h-12 text-destructive mx-auto mb-4" />
                  <h3 className="handwritten-heading text-lg mb-2 text-destructive">Failed to load PDF</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    {error}
                  </p>
                  <div className="flex gap-3 justify-center">
                    <Button
                      onClick={handleRetry}
                      className="organic-button"
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      {retryCount >= 1 ? 'Try with CDN' : 'Try Again'}
                    </Button>
                    <Button
                      onClick={() => window.open(pdfUrl, '_blank')}
                      variant="outline"
                      className="organic-button"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Open in New Tab
                    </Button>
                  </div>
                  {retryCount >= 1 && (
                    <p className="text-xs text-muted-foreground mt-3">
                      Switching to CDN worker for better compatibility
                    </p>
                  )}
                </div>
              </div>
            )}

            {!error && (
              <div className="flex justify-center p-4 overflow-y-auto h-full">
                <div className="w-full max-w-4xl">
                  <Document
                    file={pdfUrl}
                    onLoadSuccess={onDocumentLoadSuccess}
                    onLoadError={onDocumentLoadError}
                    loading={
                      <div className="flex items-center justify-center py-20">
                        <div className="text-center">
                          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-3 text-primary" />
                          <p className="text-sm text-muted-foreground">Loading PDF...</p>
                        </div>
                      </div>
                    }
                    error={
                      <div className="flex items-center justify-center py-20">
                        <div className="text-center">
                          <FileText className="w-12 h-12 mx-auto mb-3 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">Unable to load PDF</p>
                        </div>
                      </div>
                    }
                    className="pdf-document"
                  >
                    {/* Render all pages for scrollable view */}
                    <div className="space-y-6">
                      {Array.from(new Array(numPages), (el, index) => (
                        <div key={`page_${index + 1}`} data-page-number={index + 1} className="flex flex-col items-center">
                          <Page
                            pageNumber={index + 1}
                            scale={scale}
                            loading={
                              <div className="flex items-center justify-center py-10">
                                <div className="hand-drawn-spinner w-6 h-6" />
                              </div>
                            }
                            error={
                              <div className="flex items-center justify-center py-10 text-destructive">
                                <AlertCircle className="w-6 h-6 mr-2" />
                                <span>Failed to load page {index + 1}</span>
                              </div>
                            }
                            className="pdf-page shadow-lg"
                          />
                          {/* Page number indicator */}
                          <div className="text-center mt-3">
                            <span className="text-xs text-muted-foreground bg-background px-2 py-1 rounded">
                              Page {index + 1} of {numPages}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Document>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Side Compact Controls */}
        <div className="flex-shrink-0 w-16">
          <div className="pdf-side-controls !p-2 space-y-2 sticky top-4 h-fit">
            {/* Page Navigation */}
            {numPages > 1 && (
              <div className="space-y-1">
                <div className="flex flex-col gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={goToPrevPage}
                    disabled={pageNumber <= 1}
                    className="pdf-control-button !p-1 !h-8 w-full"
                    title="Previous Page"
                  >
                    <ChevronLeft className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={goToNextPage}
                    disabled={pageNumber >= numPages}
                    className="pdf-control-button !p-1 !h-8 w-full"
                    title="Next Page"
                  >
                    <ChevronRight className="w-3 h-3" />
                  </Button>
                </div>
                <div className="text-center">
                  <Input
                    type="number"
                    min={1}
                    max={numPages}
                    value={pageNumber}
                    onChange={(e) => goToPage(parseInt(e.target.value) || 1)}
                    className="w-full h-6 text-center text-xs !p-1"
                  />
                  <span className="text-xs text-muted-foreground block mt-1">
                    /{numPages}
                  </span>
                </div>
              </div>
            )}

            {/* Zoom Controls */}
            <div className="space-y-1">
              <div className="flex flex-col gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={zoomIn}
                  disabled={scale >= 3.0}
                  className="pdf-control-button !p-1 !h-8 w-full"
                  title="Zoom In (Ctrl++)"
                >
                  <ZoomIn className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={zoomOut}
                  disabled={scale <= 0.5}
                  className="pdf-control-button !p-1 !h-8 w-full"
                  title="Zoom Out (Ctrl+-)"
                >
                  <ZoomOut className="w-3 h-3" />
                </Button>
              </div>
              <div className="text-center">
                <span className="text-xs text-muted-foreground block">
                  {Math.round(scale * 100)}%
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={resetZoom}
                className="pdf-control-button !p-1 !h-8 w-full"
                title="Reset Zoom (Ctrl+0)"
              >
                <RotateCcw className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={fitToWidth}
                className="pdf-control-button !p-1 !h-8 w-full text-xs"
                title="Fit to Width"
              >
                Fit
              </Button>
            </div>

            {/* Action Buttons */}
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSearch(!showSearch)}
                className="pdf-control-button !p-1 !h-8 w-full"
                title="Search (Ctrl+F)"
              >
                <Search className="w-3 h-3" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handlePrint}
                className="pdf-control-button !p-1 !h-8 w-full"
                title="Print PDF"
              >
                <Printer className="w-3 h-3" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={onDownload}
                className="pdf-control-button !p-1 !h-8 w-full"
                title="Download PDF"
              >
                <Download className="w-3 h-3" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                className="pdf-control-button !p-1 !h-8 w-full"
                title="Fullscreen (F11)"
              >
                {isFullscreen ? <Minimize2 className="w-3 h-3" /> : <Maximize2 className="w-3 h-3" />}
              </Button>
            </div>

            {/* Search Bar */}
            {showSearch && (
              <div className="space-y-1">
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className="w-full h-6 text-xs !p-1"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSearch(false)}
                  className="pdf-control-button !p-1 !h-6 w-full text-xs"
                >
                  ✕
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>


    </div>
  )
}
