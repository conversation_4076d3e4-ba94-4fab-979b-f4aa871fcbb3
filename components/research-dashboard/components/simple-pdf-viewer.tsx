"use client"

import React, { useState, useRef, useEffect } from 'react'
import { 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Download, 
  Printer, 
  Maximize2, 
  Minimize2,
  CheckCircle,
  AlertCircle,
  Search,
  ExternalLink
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface SimplePDFViewerProps {
  pdfUrl: string
  documentTitle: string
  onDownload: () => void
}

export const SimplePDFViewer: React.FC<SimplePDFViewerProps> = ({
  pdfUrl,
  documentTitle,
  onDownload
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<boolean>(false)
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false)
  const [showSearch, setShowSearch] = useState<boolean>(false)
  const [searchText, setSearchText] = useState<string>('')
  
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Fullscreen functions
  const toggleFullscreen = () => {
    if (!isFullscreen && containerRef.current) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen()
      }
    } else if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }

  // Print function
  const handlePrint = () => {
    window.open(pdfUrl, '_blank')
  }

  // Open in new tab
  const openInNewTab = () => {
    window.open(pdfUrl, '_blank')
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return

      switch (e.key) {
        case 'f':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            setShowSearch(!showSearch)
          }
          break
        case 'p':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            handlePrint()
          }
          break
        case 'F11':
          e.preventDefault()
          toggleFullscreen()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [showSearch])

  // Fullscreen change listener
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  return (
    <div className="organic-pdf-viewer" ref={containerRef}>
      {/* PDF Viewer Header */}
      <div className="paper-card !p-4 mb-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Document Info */}
          <div className="flex items-center gap-3 min-w-0">
            <div className="compilation-status success">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">PDF Ready</span>
            </div>
            <div className="text-sm text-muted-foreground truncate">
              📄 {documentTitle}.pdf
            </div>
          </div>

          {/* Controls */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSearch(!showSearch)}
                className="pdf-control-button"
                title="Search (Ctrl+F)"
              >
                <Search className="w-4 h-4" />
                <span className="hidden sm:inline ml-1">Search</span>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePrint}
                className="pdf-control-button"
                title="Print PDF (Ctrl+P)"
              >
                <Printer className="w-4 h-4" />
                <span className="hidden sm:inline ml-1">Print</span>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onDownload}
                className="pdf-control-button"
                title="Download PDF"
              >
                <Download className="w-4 h-4" />
                <span className="hidden sm:inline ml-1">Download</span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={openInNewTab}
                className="pdf-control-button"
                title="Open in New Tab"
              >
                <ExternalLink className="w-4 h-4" />
                <span className="hidden sm:inline ml-1">New Tab</span>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                className="pdf-control-button"
                title="Fullscreen (F11)"
              >
                {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                <span className="hidden sm:inline ml-1">
                  {isFullscreen ? 'Exit' : 'Fullscreen'}
                </span>
              </Button>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        {showSearch && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center gap-2 max-w-md">
              <Search className="w-4 h-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search in document... (use browser's built-in search)"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="organic-input"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && searchText) {
                    // Trigger browser's find functionality
                    if (iframeRef.current?.contentWindow) {
                      iframeRef.current.contentWindow.find(searchText)
                    }
                  }
                }}
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSearch(false)}
                className="text-muted-foreground"
              >
                ✕
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              💡 Tip: Use Ctrl+F for browser's native search, or type here and press Enter
            </p>
          </div>
        )}
      </div>

      {/* PDF Document Container */}
      <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden shadow-inner border-2 border-sage-green/20"
           style={{ height: isFullscreen ? '100vh' : '70vh' }}>
        
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center pdf-loading-overlay z-10">
            <div className="text-center paper-card !p-6">
              <div className="hand-drawn-spinner w-10 h-10 mx-auto mb-4" />
              <h3 className="handwritten-heading text-lg mb-2">Loading your document...</h3>
              <p className="text-sm text-muted-foreground">
                Preparing professional PDF with LaTeX typesetting ✨
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center pdf-loading-overlay z-10">
            <div className="text-center paper-card !p-6">
              <AlertCircle className="w-12 h-12 text-destructive mx-auto mb-4" />
              <h3 className="handwritten-heading text-lg mb-2 text-destructive">Failed to load PDF</h3>
              <p className="text-sm text-muted-foreground mb-4">
                The PDF might be processing or there was a network issue.
              </p>
              <div className="flex gap-2 justify-center">
                <Button 
                  onClick={() => {
                    setError(false)
                    setIsLoading(true)
                  }}
                  className="organic-button"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button 
                  onClick={openInNewTab}
                  variant="outline"
                  className="organic-button"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open in New Tab
                </Button>
              </div>
            </div>
          </div>
        )}

        <iframe
          ref={iframeRef}
          src={`${pdfUrl}#toolbar=1&navpanes=1&scrollbar=1&view=FitH&zoom=page-width`}
          className="pdf-iframe w-full h-full border-0 bg-white"
          title="Document PDF Preview"
          onLoad={() => {
            setIsLoading(false)
            setError(false)
            console.log("PDF iframe loaded successfully")
          }}
          onError={() => {
            setIsLoading(false)
            setError(true)
            console.error("PDF iframe failed to load")
          }}
        />
      </div>

      {/* PDF Viewer Footer */}
      <div className="mt-6 space-y-3">
        <div className="paper-card !p-4 !transform-none">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+F</kbd>
              <span>Search</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+P</kbd>
              <span>Print</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">F11</kbd>
              <span>Fullscreen</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Scroll</kbd>
              <span>Navigate</span>
            </div>
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-xs text-muted-foreground italic">
            📄 Professional PDF • 
            🔍 Native browser controls • 
            ⚡ LaTeX compiled • 
            🖥️ Optimized viewing
          </div>
        </div>
      </div>
    </div>
  )
}
