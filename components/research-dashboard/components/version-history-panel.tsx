"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/client"
import { DocumentVersion } from "@/lib/types"
import { 
  Clock, 
  CheckCircle, 
  RotateCcw, 
  FileText, 
  Loader2,
  ChevronRight,
  ChevronDown,
  MessageSquare
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface VersionHistoryPanelProps {
  documentId: string
  currentVersion: DocumentVersion | null
  onVersionSwitch: (version: DocumentVersion) => void
  onVersionSelect?: (version: DocumentVersion) => void
  className?: string
}

export function VersionHistoryPanel({
  documentId,
  currentVersion,
  onVersionSwitch,
  onVersionSelect,
  className = ""
}: VersionHistoryPanelProps) {
  const [versions, setVersions] = useState<DocumentVersion[]>([])
  const [loading, setLoading] = useState(true)
  const [switchingVersion, setSwitchingVersion] = useState<string | null>(null)
  const [expandedVersions, setExpandedVersions] = useState<Set<string>>(new Set())

  useEffect(() => {
    loadVersions()
  }, [documentId])

  // Reload versions when current version changes
  useEffect(() => {
    if (currentVersion) {
      loadVersions()
    }
  }, [currentVersion?.id])

  const loadVersions = async () => {
    try {
      setLoading(true)
      const versionList = await apiClient.documents.getVersions(documentId)
      setVersions(versionList)
    } catch (error) {
      console.error("Error loading versions:", error)
      toast({
        title: "Error",
        description: "Failed to load version history",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSwitchVersion = async (version: DocumentVersion) => {
    if (version.id === currentVersion?.id) return

    try {
      setSwitchingVersion(version.id)
      await apiClient.documents.setCurrentVersion(version.id)
      
      // Update local state
      setVersions(prev => prev.map(v => ({
        ...v,
        is_current: v.id === version.id
      })))

      onVersionSwitch(version)
      
      toast({
        title: "Success",
        description: `Switched to version ${version.version_number}`,
      })
    } catch (error) {
      console.error("Error switching version:", error)
      toast({
        title: "Error",
        description: "Failed to switch version",
        variant: "destructive",
      })
    } finally {
      setSwitchingVersion(null)
    }
  }

  const toggleVersionExpanded = (versionId: string) => {
    setExpandedVersions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(versionId)) {
        newSet.delete(versionId)
      } else {
        newSet.add(versionId)
      }
      return newSet
    })
  }

  const getVersionDescription = (version: DocumentVersion) => {
    if (version.description) return version.description
    if (version.created_by_message_id) return "Created by AI message"
    return "Manual save"
  }

  const getVersionIcon = (version: DocumentVersion) => {
    if (version.created_by_message_id) {
      return <MessageSquare className="w-4 h-4 text-blue-500" />
    }
    return <FileText className="w-4 h-4 text-gray-500" />
  }

  if (loading) {
    return (
      <div className={`bg-card border-l border-border ${className}`}>
        <div className="p-4 border-b border-border">
          <h3 className="text-lg font-semibold">Version History</h3>
        </div>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-card border-l border-border flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-border flex-shrink-0">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Version History</h3>
          <Badge variant="outline" className="text-xs">
            {versions.length} version{versions.length !== 1 ? 's' : ''}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          Track changes and switch between versions
        </p>
      </div>

      {/* Version List */}
      <ScrollArea className="flex-1 h-0">
        <div className="p-4 space-y-3">
          {versions.map((version) => {
            const isExpanded = expandedVersions.has(version.id)
            const isCurrent = version.is_current || version.id === currentVersion?.id
            const isSwitching = switchingVersion === version.id

            return (
              <div
                key={version.id}
                className={`border rounded-lg p-3 transition-all ${
                  isCurrent 
                    ? "border-primary bg-primary/5" 
                    : "border-border hover:border-primary/50"
                }`}
              >
                {/* Version Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getVersionIcon(version)}
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          Version {version.version_number}
                        </span>
                        {isCurrent && (
                          <Badge variant="default" className="text-xs">
                            Current
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(version.created_at), { addSuffix: true })}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {version.compiled_pdf_url && (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleVersionExpanded(version.id)}
                      className="p-1"
                    >
                      {isExpanded ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Expanded Content */}
                {isExpanded && (
                  <div className="mt-3 pt-3 border-t border-border">
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">
                        {getVersionDescription(version)}
                      </div>
                      
                      {version.latex_code && (
                        <div className="text-xs text-muted-foreground">
                          {version.latex_code.length} characters
                        </div>
                      )}

                      <div className="flex gap-2 pt-2">
                        {!isCurrent && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleSwitchVersion(version)}
                            disabled={isSwitching}
                            className="text-xs"
                          >
                            {isSwitching ? (
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                            ) : (
                              <RotateCcw className="w-3 h-3 mr-1" />
                            )}
                            Switch to this version
                          </Button>
                        )}
                        
                        {onVersionSelect && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onVersionSelect(version)}
                            className="text-xs"
                          >
                            View Details
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )
          })}

          {versions.length === 0 && (
            <div className="text-center py-8">
              <Clock className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <h4 className="text-lg font-medium mb-2">No Version History</h4>
              <p className="text-sm text-muted-foreground">
                Versions will appear here as you make changes to your document.
              </p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
