"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Sheet, SheetContent, Sheet<PERSON>eader, SheetTitle } from "@/components/ui/sheet"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  ArrowLeft,
  Plus,
  Clock,
  FolderOpen,
  FileText,
  Loader2,
} from "lucide-react"
import type { AppState, Document } from "../types"

interface MobileMenuProps {
  isOpen: boolean
  onClose: () => void
  currentPage: AppState
  onNavigate: (state: AppState) => void
  onBackToWelcome: () => void
  documents: Document[]
  documentsLoading?: boolean
  onDocumentClick: (doc: Document) => void
  title?: string
}

export function MobileMenu({
  isOpen,
  onClose,
  currentPage,
  onNavigate,
  onBackToWelcome,
  documents,
  documentsLoading = false,
  onDocumentClick,
  title = "Navigation Menu"
}: MobileMenuProps) {
  const handleNavigation = (state: AppState) => {
    onNavigate(state)
    onClose()
  }

  const handleBackToWelcome = () => {
    onBackToWelcome()
    onClose()
  }

  const handleDocumentClick = (doc: Document) => {
    onDocumentClick(doc)
    onClose()
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="left" className="w-80 bg-card-dash border-gray-800 p-0">
        <SheetHeader className="sr-only">
          <SheetTitle>{title}</SheetTitle>
        </SheetHeader>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-gray-800">
            <div className="flex items-center gap-3">
              {currentPage !== "welcome" && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleBackToWelcome}
                  className="text-gray-400 hover:text-white"
                >
                  <ArrowLeft className="w-5 h-5" />
                </Button>
              )}
              <img src="/deepdocx_full.png" alt="deepdocx logo" className="w-32 h-auto" />
            </div>
            <p className="text-sm text-gray-400 mt-2">Professional Document Suite</p>
          </div>

          {/* Navigation */}
          <div className="p-4 space-y-2">
            {currentPage === "document" ? (
              <div className="w-full h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg mb-4">
                <FileText className="w-6 h-6 text-white mr-3" />
                <span className="text-white font-medium">Document Editor</span>
              </div>
            ) : (
              <Button
                onClick={() => handleNavigation("welcome")}
                className={`w-full justify-start h-12 font-medium ${
                  currentPage === "welcome"
                    ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg"
                    : "variant-ghost text-gray-300 hover:text-white hover:bg-gray-800"
                }`}
              >
                <Plus className="w-5 h-5 mr-3" />
                New Document
              </Button>
            )}

            <Button
              onClick={() => handleNavigation("generated-documents")}
              variant={currentPage === "generated-documents" ? "default" : "ghost"}
              className={`w-full justify-start h-12 font-medium ${
                currentPage === "generated-documents"
                  ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white"
                  : "text-gray-300 hover:text-white hover:bg-gray-800"
              }`}
            >
              <Clock className="w-5 h-5 mr-3" />
              Generated Documents
            </Button>

            <Button
              onClick={() => handleNavigation("files-management")}
              variant={currentPage === "files-management" ? "default" : "ghost"}
              className={`w-full justify-start h-12 font-medium ${
                currentPage === "files-management"
                  ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white"
                  : "text-gray-300 hover:text-white hover:bg-gray-800"
              }`}
            >
              <FolderOpen className="w-5 h-5 mr-3" />
              Files Management
            </Button>
          </div>

          {/* Recent Documents */}
          {currentPage !== "document" && (
            <div className="flex-1 px-4">
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-white uppercase tracking-wider">Recent Documents</h3>
              </div>
              <ScrollArea className="space-y-2 max-h-64">
                {documentsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                  </div>
                ) : (
                  documents.slice(0, 3).map((doc) => (
                    <div
                      key={doc.id}
                      onClick={() => handleDocumentClick(doc)}
                      className="flex items-center gap-3 p-3 rounded-xl hover:bg-gray-800 cursor-pointer transition-all duration-200 group border border-transparent hover:border-gray-700"
                    >
                      <div className="w-8 h-8 bg-gray-800 rounded-lg flex items-center justify-center group-hover:bg-purple-900/50 transition-colors">
                        <FileText className="w-4 h-4 text-gray-400 group-hover:text-purple-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-white truncate">{doc.title}</div>
                        <div className="text-xs text-gray-400">{new Date(doc.updated_at).toLocaleDateString()}</div>
                      </div>
                      <Badge variant={doc.status === "completed" ? "default" : "secondary"} className="text-xs">
                        {doc.status}
                      </Badge>
                    </div>
                  ))
                )}
              </ScrollArea>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  )
}
