"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Loader2 } from "lucide-react"
import {
  FileText,
  MoreVertical,
  Edit,
  Copy,
  Download,
  Trash2,
  Calendar,
  Clock,
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import type { Document, DocumentOperations } from "../types"

interface DocumentGridProps {
  documents: Document[]
  documentsLoading: boolean
  onDocumentClick: (doc: Document) => void
  documentOperations: DocumentOperations
}

export function DocumentGrid({
  documents,
  documentsLoading,
  onDocumentClick,
  documentOperations,
}: DocumentGridProps) {
  const handleDownload = async (e: React.MouseEvent, docId: string) => {
    e.stopPropagation()
    try {
      // Mock download functionality
      toast({
        title: "Success",
        description: "Document downloaded successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download document",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (e: React.MouseEvent, docId: string) => {
    e.stopPropagation()
    try {
      await documentOperations.deleteDocument(docId)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete document",
        variant: "destructive",
      })
    }
  }

  const handleDuplicate = async (e: React.MouseEvent, doc: Document) => {
    e.stopPropagation()
    try {
      await documentOperations.createDocument({
        title: `${doc.title} (Copy)`,
        description: doc.description,
        type: doc.type,
      })
      toast({
        title: "Success",
        description: "Document duplicated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate document",
        variant: "destructive",
      })
    }
  }

  if (documentsLoading) {
    return (
      <div className="flex items-center justify-center py-16">
        <Loader2 className="w-6 h-6 lg:w-8 lg:h-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
      {documents.map((doc) => (
        <Card
          key={doc.id}
          className="hover:shadow-xl cursor-pointer group border-border bg-card hover:border-primary/50 hover:shadow-primary/20"
          onClick={() => onDocumentClick(doc)}
        >
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                  <FileText className="w-6 h-6 text-primary-foreground" />
                </div>
                <Badge
                  variant={
                    doc.status === "completed" ? "default" : doc.status === "draft" ? "secondary" : "outline"
                  }
                  className={`text-xs ${
                    doc.status === "completed"
                      ? "bg-primary/20 text-primary border-primary/30"
                      : doc.status === "draft"
                        ? "bg-chart-1/20 text-chart-1 border-chart-1/30"
                        : "bg-secondary/20 text-secondary border-secondary/30"
                  }`}
                >
                  {doc.status}
                </Badge>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="opacity-0 group-hover:opacity-100 text-muted-foreground hover:text-primary"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-card border-border">
                  <DropdownMenuItem
                    className="text-foreground hover:text-primary hover:bg-muted"
                    onClick={(e) => {
                      e.stopPropagation()
                      onDocumentClick(doc)
                    }}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-foreground hover:text-primary hover:bg-muted"
                    onClick={(e) => handleDuplicate(e, doc)}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-foreground hover:text-primary hover:bg-muted"
                    onClick={(e) => handleDownload(e, doc.id)}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-destructive hover:text-destructive/80 hover:bg-muted"
                    onClick={(e) => handleDelete(e, doc.id)}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <CardTitle className="text-lg group-hover:text-primary leading-tight text-foreground">
              {doc.title}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">{doc.type}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-foreground mb-4 line-clamp-3 leading-relaxed">
              {doc.description || "No description available"}
            </p>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {new Date(doc.created_at).toLocaleDateString()}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {new Date(doc.updated_at).toLocaleDateString()}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
