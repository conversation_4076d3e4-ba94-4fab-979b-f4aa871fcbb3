"use client"

import React from 'react'
import { AlertCircle, RotateCcw, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface PDFErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface PDFErrorBoundaryProps {
  children: React.ReactNode
  pdfUrl: string
  onRetry?: () => void
}

export class PDFErrorBoundary extends React.Component<PDFErrorBoundaryProps, PDFErrorBoundaryState> {
  constructor(props: PDFErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): PDFErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('PDF Viewer Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="organic-pdf-viewer">
          <div className="paper-card !p-8 text-center">
            <AlertCircle className="w-16 h-16 text-destructive mx-auto mb-4" />
            <h3 className="handwritten-heading text-xl mb-4 text-destructive">
              PDF Viewer Error
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              The PDF viewer encountered an error. This might be due to browser compatibility 
              or the PDF format. You can try refreshing or open the PDF in a new tab.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={() => {
                  this.setState({ hasError: false })
                  this.props.onRetry?.()
                }}
                className="organic-button"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
              
              <Button
                onClick={() => window.open(this.props.pdfUrl, '_blank')}
                variant="outline"
                className="organic-button"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open in New Tab
              </Button>
            </div>

            {this.state.error && (
              <details className="mt-6 text-left">
                <summary className="text-sm text-muted-foreground cursor-pointer">
                  Technical Details
                </summary>
                <pre className="mt-2 text-xs bg-muted p-3 rounded overflow-auto">
                  {this.state.error.message}
                </pre>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
