export type AppState = "welcome" | "document" | "generated-documents" | "files-management"
export type ViewMode = "pdf" | "latex"

export interface ChatMessage {
  id: string
  user: string
  avatar: string
  content: string
  timestamp: string
}

export interface FileItem {
  id: string
  name: string
  type: "pdf" | "docx" | "png" | "jpeg" | "csv" | "folder"
  size?: string
  dateAdded: string
  parentFolder?: string
}

export interface Folder {
  id: string
  name: string
  files: FileItem[]
  subfolders: Folder[]
  parentId?: string
}

export interface Document {
  id: string
  user_id: string
  title: string
  description?: string
  type: string
  status: "draft" | "completed" | "processing"
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ResponsiveState {
  isMobile: boolean
  isTablet: boolean
  sidebarCollapsed: boolean
  mobileMenuOpen: boolean
  chatPanelCollapsed: boolean
  chatPanelWidth: number
}

export interface DocumentOperations {
  createDocument: (data: Partial<Document>) => Promise<Document>
  createDocumentWithMessage: (data: {
    message: string;
    folder_id?: string;
    type?: string;
    referenced_files?: string[]
  }) => Promise<any>
  updateDocument: (id: string, data: Partial<Document>) => Promise<Document>
  deleteDocument: (id: string) => Promise<void>
  shareDocument: (documentId: string, options: any) => Promise<{ shareUrl: string; shareToken: string }>
}

export interface CompilationResult {
  success: boolean
  errors?: string[]
}

export interface VersionOperations {
  versions: any[]
  versionsLoading: boolean
  createVersion: (latexCode: string, description?: string) => Promise<void>
  switchToVersion: (versionId: string) => Promise<void>
}

export interface CompilationOperations {
  isCompiling: boolean
  compilationResult: CompilationResult | null
  compileLatex: (documentId: string, latexCode: string) => Promise<CompilationResult>
  downloadPdf: (documentId: string) => Promise<void>
}
