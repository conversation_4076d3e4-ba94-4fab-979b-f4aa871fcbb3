"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { WelcomeScreen } from "./pages/welcome-screen"
import { DocumentsPage } from "./pages/documents-page"
import { DocumentEditor } from "./pages/document-editor"
import { FilesManagement } from "./pages/files-management"
import { useResponsive } from "./hooks/use-responsive"
import { useDocuments } from "./hooks/use-documents"
import type { AppState, Document } from "./types"

export function ResearchDashboard() {
  const [appState, setAppState] = useState<AppState>("welcome")
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null)
  const router = useRouter()

  const responsive = useResponsive()
  const { documents, documentsLoading, documentOperations } = useDocuments()

  const handleStateChange = (newState: AppState) => {
    setAppState(newState)
    responsive.closeMobileMenu()
  }

  const handleDocumentClick = (doc: Document) => {
    // Navigate to document-specific URL
    router.push(`/dashboard/${doc.id}`)
  }

  const handleBackToWelcome = () => {
    setAppState("welcome")
    setSelectedDocument(null)
  }

  const handleShowSettings = () => {
    // Settings are now handled within each component
  }

  if (appState === "document") {
    return (
      <DocumentEditor
        document={selectedDocument!}
        onStateChange={handleStateChange}
        onBackToWelcome={handleBackToWelcome}
        onShowSettings={handleShowSettings}
        isMobile={responsive.isMobile}
        mobileMenuOpen={responsive.mobileMenuOpen}
        onToggleMobileMenu={responsive.toggleMobileMenu}
        onCloseMobileMenu={responsive.closeMobileMenu}
      />
    )
  }

  if (appState === "files-management") {
    return (
      <FilesManagement
        onStateChange={handleStateChange}
        onBackToWelcome={handleBackToWelcome}
        onShowSettings={handleShowSettings}
        isMobile={responsive.isMobile}
        mobileMenuOpen={responsive.mobileMenuOpen}
        onToggleMobileMenu={responsive.toggleMobileMenu}
        onCloseMobileMenu={responsive.closeMobileMenu}
      />
    )
  }

  if (appState === "welcome") {
    return (
      <WelcomeScreen
        documents={documents}
        documentsLoading={documentsLoading}
        documentOperations={documentOperations}
        onStateChange={handleStateChange}
        onDocumentClick={handleDocumentClick}
        onShowSettings={handleShowSettings}
        isMobile={responsive.isMobile}
        mobileMenuOpen={responsive.mobileMenuOpen}
        onToggleMobileMenu={responsive.toggleMobileMenu}
        onCloseMobileMenu={responsive.closeMobileMenu}
      />
    )
  }

  if (appState === "generated-documents") {
    return (
      <DocumentsPage
        documents={documents}
        documentsLoading={documentsLoading}
        documentOperations={documentOperations}
        onStateChange={handleStateChange}
        onDocumentClick={handleDocumentClick}
        onBackToWelcome={handleBackToWelcome}
        onShowSettings={handleShowSettings}
        isMobile={responsive.isMobile}
        mobileMenuOpen={responsive.mobileMenuOpen}
        onToggleMobileMenu={responsive.toggleMobileMenu}
        onCloseMobileMenu={responsive.closeMobileMenu}
      />
    )
  }

  return null
}
