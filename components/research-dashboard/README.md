# Research Dashboard - Organized Component Structure

This directory contains the refactored and organized Research Dashboard components, split into smaller, more manageable pieces for better maintainability and code organization.

## 📁 Directory Structure

```
components/research-dashboard/
├── index.tsx                    # Main dashboard orchestrator
├── hooks/
│   ├── use-responsive.ts        # Responsive state management
│   └── use-documents.ts         # Document operations and state
├── pages/
│   ├── welcome-screen.tsx       # Welcome/landing page
│   ├── documents-page.tsx       # Generated documents page
│   ├── document-editor.tsx      # Document editor with LaTeX and PDF preview
│   └── files-management.tsx     # File and folder management page
├── components/
│   ├── mobile-menu.tsx         # Reusable mobile navigation menu
│   └── document-grid.tsx       # Document cards grid component
├── types/
│   └── index.ts                # TypeScript type definitions
├── constants/
│   └── mock-data.ts            # Mock data and constants
└── README.md                   # This documentation
```

## 🔧 Components Overview

### **Main Orchestrator**
- **`index.tsx`**: Main dashboard component that manages state and routing between different pages

### **Custom Hooks**
- **`use-responsive.ts`**: Manages responsive breakpoints, mobile menu state, and screen size detection
- **`use-documents.ts`**: Handles document operations, mock data, and document-related state

### **Page Components**
- **`welcome-screen.tsx`**: Landing page with hero section, features, and document creation
- **`documents-page.tsx`**: Generated documents listing with search and filtering
- **`document-editor.tsx`**: Full-featured document editor with LaTeX editing, PDF preview, AI chat assistant, and compilation tools
- **`files-management.tsx`**: Complete file and folder management system with upload, download, and organization features

### **Reusable Components**
- **`mobile-menu.tsx`**: Responsive mobile navigation menu used across all pages
- **`document-grid.tsx`**: Grid layout for displaying document cards with actions

### **Types & Constants**
- **`types/index.ts`**: All TypeScript interfaces and type definitions
- **`constants/mock-data.ts`**: Mock data for documents, folders, and chat messages

## 🎯 Key Features

### **Responsive Design**
- Mobile-first approach with adaptive layouts
- Breakpoint detection: Mobile (<768px), Tablet (768-1024px), Desktop (>1024px)
- Touch-friendly interfaces and proper sizing

### **State Management**
- Centralized responsive state with custom hooks
- Document operations abstracted into reusable hooks
- Clean separation of concerns

### **Component Reusability**
- Mobile menu component shared across all pages
- Document grid component for consistent document display
- Modular design for easy maintenance

## 🚀 Usage

### **Adding New Pages**
1. Create new page component in `pages/` directory
2. Add route handling in `index.tsx`
3. Update `AppState` type in `types/index.ts`

### **Adding New Components**
1. Create component in `components/` directory
2. Export and import where needed
3. Follow existing patterns for props and styling

### **Extending Hooks**
1. Add new functionality to existing hooks
2. Create new hooks in `hooks/` directory for new features
3. Follow the pattern of returning state and actions

## 📱 Responsive Features

### **Mobile Navigation**
- Slide-out menu with Sheet component
- Touch-friendly navigation
- Recent documents quick access

### **Adaptive Layouts**
- Grid systems that adapt to screen size
- Responsive typography and spacing
- Mobile-optimized interactions

### **Touch Optimization**
- Minimum 44px touch targets
- Proper spacing for mobile interactions
- Swipe-friendly interfaces

## 🔄 Migration Status

### **✅ Completed - Migration Successful!**
- Welcome Screen (fully responsive)
- Generated Documents Page (fully responsive)
- Mobile Menu Component
- Document Grid Component
- Responsive Hooks
- Type Definitions

### **✅ Completed**
- Files Management Page (fully functional with file upload, folder creation, and management)
- Document Editor Page (LaTeX editor, PDF preview, AI chat, compilation tools)
- **Old monolithic research-dashboard.tsx removed**
- **All functionality migrated to modular components**

### **📋 TODO**
- Chat Panel Component
- Document Viewer Component
- Floating Actions Component
- Settings Dialog Integration
- Complete Files Management refactor
- Complete Document Editor refactor

## 🛠 Development Guidelines

### **Code Organization**
- Keep components small and focused
- Use TypeScript for all new code
- Follow existing naming conventions
- Add proper JSDoc comments

### **Styling**
- Use Tailwind CSS classes
- Follow responsive design patterns
- Maintain consistent spacing and colors
- Use CSS custom properties for themes

### **State Management**
- Use custom hooks for complex state
- Keep component state minimal
- Pass data down through props
- Avoid prop drilling with context when needed

## 🧪 Testing

The refactored components maintain the same functionality as the original while providing:
- Better code organization
- Improved maintainability
- Enhanced responsive design
- Cleaner separation of concerns

## 📚 Next Steps

1. **Complete Migration**: Finish refactoring remaining pages
2. **Add Tests**: Implement unit tests for components and hooks
3. **Performance**: Optimize bundle size and loading
4. **Accessibility**: Enhance ARIA labels and keyboard navigation
5. **Documentation**: Add more detailed component documentation
