"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import {
  ArrowLeft,
  Eye,
  Code,
  RotateCcw,
  Download,
  Share,
  Send,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  Maximize2,
  Minimize2,
  Menu,
  Settings,
  Clock,
  FileText,
  ZoomIn,
  ZoomOut,
  Printer,
  Save,
} from "lucide-react"
import { MobileMenu } from "../components/mobile-menu"
import { apiClient } from "@/lib/api/client"
import { useLatexCompilation } from "@/lib/hooks/useLatexCompilation"
import { EnhancedPDFViewer } from "../components/enhanced-pdf-viewer"
import { PDFErrorBoundary } from "../components/pdf-error-boundary"
import { VersionHistoryPanel } from "../components/version-history-panel"
import { useAuth } from "@/lib/contexts/auth-context"
import { useRouter } from "next/navigation"


import type { Document, AppState, ViewMode } from "../types"
import type { Message, DocumentVersion } from "@/lib/types"

interface DocumentEditorProps {
  document: Document
  onStateChange: (state: AppState) => void
  onBackToWelcome: () => void
  onShowSettings: () => void
  isMobile: boolean
  mobileMenuOpen: boolean
  onToggleMobileMenu: () => void
  onCloseMobileMenu: () => void
}

export function DocumentEditor({
  document,
  onStateChange,
  onBackToWelcome,
  onShowSettings,
  isMobile,
  mobileMenuOpen,
  onToggleMobileMenu,
  onCloseMobileMenu,
}: DocumentEditorProps) {
  const [viewMode, setViewMode] = useState<ViewMode>("pdf")
  const [latexCode, setLatexCode] = useState("")
  const [message, setMessage] = useState("")
  const [messages, setMessages] = useState<Message[]>([])
  const [isSendingMessage, setIsSendingMessage] = useState(false)
  const [chatPanelCollapsed, setChatPanelCollapsed] = useState(false)
  const [chatPanelWidth, setChatPanelWidth] = useState(510)
  const [isThinking, setIsThinking] = useState(false)
  const [versionPanelCollapsed, setVersionPanelCollapsed] = useState(false)
  const [versionPanelWidth, setVersionPanelWidth] = useState(350)
  const [isSaving, setIsSaving] = useState(false)
  const [messageStartTime, setMessageStartTime] = useState<number | null>(null)

  // Use the enhanced LaTeX compilation hook
  const { isCompiling, compilationResult, compileLatex } = useLatexCompilation()

  // Get user information for chat display
  const { user } = useAuth()

  // Router for navigation
  const router = useRouter()
  const [currentVersion, setCurrentVersion] = useState<DocumentVersion | null>(null)
  const [conversation, setConversation] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  const chatPanelRef = useRef<HTMLDivElement>(null)

  // Load document data on mount
  useEffect(() => {
    loadDocumentData()
  }, [document.id])

  const loadDocumentData = async () => {
    try {
      setLoading(true)
      const response = await apiClient.documents.get(document.id)

      setCurrentVersion(response.current_version)
      setConversation(response.conversation)

      // Set LaTeX code from current version or default
      let latexToSet = ""
      if (response.current_version?.latex_code) {
        latexToSet = response.current_version.latex_code
        setLatexCode(latexToSet)
      } else {
        // Default LaTeX template
        setLatexCode(`\\documentclass[11pt,a4paper]{article}
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{geometry}
\\usepackage{amsmath}
\\usepackage{amsfonts}
\\usepackage{amssymb}
\\usepackage{graphicx}
\\usepackage{hyperref}

\\geometry{margin=1in}

\\title{${document.title}}
\\author{Author Name\\\\Institution Name\\\\<EMAIL>}
\\date{\\today}

\\begin{document}

\\maketitle

\\begin{abstract}
This is the abstract of your research paper. It should provide a brief summary of the research, including the problem, methodology, results, and conclusion.
\\end{abstract}

\\section{Introduction}
Your introduction content here...

\\section{Methodology}
Your methodology content here...

\\section{Results}
Your results content here...

\\section{Conclusion}
Your conclusion content here...

\\end{document}`)
        latexToSet = `\\documentclass[11pt,a4paper]{article}
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{geometry}
\\usepackage{amsmath}
\\usepackage{amsfonts}
\\usepackage{amssymb}
\\usepackage{graphicx}
\\usepackage{hyperref}

\\geometry{margin=1in}

\\title{${document.title}}
\\author{Author Name\\\\Institution Name\\\\<EMAIL>}
\\date{\\today}

\\begin{document}

\\maketitle

\\begin{abstract}
This is the abstract of your research paper. It should provide a brief summary of the research, including the problem, methodology, results, and conclusion.
\\end{abstract}

\\section{Introduction}
Your introduction content here...

\\section{Methodology}
Your methodology content here...

\\section{Results}
Your results content here...

\\section{Conclusion}
Your conclusion content here...

\\end{document}`
      }

      // Auto-compile the document when it loads
      if (latexToSet) {
        console.log('Auto-compiling document on load...')
        setTimeout(() => {
          compileLatex(document.id, latexToSet)
        }, 1000) // Small delay to ensure everything is loaded
      }

      // Load conversation messages if conversation exists
      if (response.conversation) {
        const messagesResponse = await apiClient.conversations.getMessages(response.conversation.id)
        setMessages(messagesResponse)
      } else {
        // Set initial AI message
        setMessages([
          {
            id: "initial",
            conversation_id: "",
            role: "assistant",
            content: `I'm ready to help you with "${document.title}". What would you like to work on?`,
            metadata: {},
            created_at: new Date().toISOString(),
          }
        ])
      }
    } catch (error) {
      console.error("Error loading document data:", error)
      toast({
        title: "Error",
        description: "Failed to load document data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Helper function to parse @mentions from message
  const parseFileMentions = async (messageText: string): Promise<string[]> => {
    console.log('=== PARSING FILE MENTIONS ===')
    console.log('Message text:', messageText)

    // Extract @mention patterns like @Papers/AIevo.pdf or @image.png
    const mentionPattern = /@([^\s]+)/g
    const mentions = []
    let match

    while ((match = mentionPattern.exec(messageText)) !== null) {
      mentions.push(match[1]) // Extract the file path without @
    }

    console.log('Found mentions:', mentions)

    if (mentions.length === 0) {
      console.log('No mentions found')
      return []
    }

    try {
      // Get user files to resolve mentions
      console.log('Fetching user files and folders...')
      const userFiles = await apiClient.files.list()
      const userFolders = await apiClient.folders.list()

      console.log('User files:', userFiles.map(f => ({ name: f.name, folder_id: f.folder_id })))
      console.log('User folders:', userFolders.map(f => ({ name: f.name, id: f.id, parent_id: f.parent_id })))

      const resolvedFiles: string[] = []

      for (const mention of mentions) {
        console.log(`Processing mention: @${mention}`)

        // Parse the mention path
        const pathParts = mention.split('/')
        const fileName = pathParts[pathParts.length - 1]
        const folderPath = pathParts.slice(0, -1)

        console.log('Path parts:', pathParts)
        console.log('File name:', fileName)
        console.log('Folder path:', folderPath)

        // Find the file
        let targetFile = null

        if (folderPath.length === 0) {
          // Direct file reference like @AIevo.pdf
          console.log('Looking for direct file reference')
          console.log('Available files:', userFiles.map(f => ({ name: f.name, id: f.id, folder_id: f.folder_id })))
          targetFile = userFiles.find(f => f.name === fileName)
          console.log('Direct file search result:', targetFile)
        } else {
          // Folder path reference like @Papers/AIevo.pdf
          console.log('Looking for folder path reference')
          // Find the target folder
          let currentFolderId = null
          for (const folderName of folderPath) {
            console.log(`Looking for folder: ${folderName} with parent: ${currentFolderId}`)
            console.log('Available folders:', userFolders.map(f => ({ name: f.name, id: f.id, parent_id: f.parent_id })))

            const folder = userFolders.find(f => {
              const nameMatch = f.name === folderName
              const parentMatch = f.parent_id === currentFolderId
              console.log(`Checking folder ${f.name}: nameMatch=${nameMatch}, parentMatch=${parentMatch} (parent_id=${f.parent_id}, looking for=${currentFolderId})`)
              return nameMatch && parentMatch
            })

            if (!folder) {
              console.log(`Folder ${folderName} not found`)
              console.log('Available folders for debugging:', userFolders)
              break
            }
            console.log(`Found folder: ${folder.name} (ID: ${folder.id})`)
            currentFolderId = folder.id
          }

          if (currentFolderId) {
            console.log(`Looking for file ${fileName} in folder ${currentFolderId}`)
            console.log('Available files:', userFiles.map(f => ({ name: f.name, id: f.id, folder_id: f.folder_id })))
            targetFile = userFiles.find(f => {
              const nameMatch = f.name === fileName
              const folderMatch = f.folder_id === currentFolderId
              console.log(`Checking file ${f.name}: nameMatch=${nameMatch}, folderMatch=${folderMatch} (folder_id=${f.folder_id}, looking for=${currentFolderId})`)
              return nameMatch && folderMatch
            })
            console.log('File search result:', targetFile)
          } else {
            console.log('No target folder found, cannot search for file')
          }
        }

        if (targetFile) {
          console.log(`Found target file: ${targetFile.name} (ID: ${targetFile.id})`)
          resolvedFiles.push(targetFile.id)
        } else {
          console.warn(`File not found for mention: @${mention}`)
        }
      }

      console.log('Resolved file IDs:', resolvedFiles)
      return resolvedFiles
    } catch (error) {
      console.error('Error resolving file mentions:', error)
      return []
    }
  }

  const handleSendMessage = async () => {
    if (!message.trim() || isSendingMessage) return

    const userMessage = message.trim()
    console.log('=== HANDLE SEND MESSAGE ===')
    console.log('User message:', userMessage)
    setMessage("") // Clear input immediately

    try {
      setIsSendingMessage(true)
      setIsThinking(true)

      // Track start time for response calculation
      const startTime = Date.now()
      setMessageStartTime(startTime)

      // Parse file mentions from the message
      console.log('About to parse file mentions...')

      // TEMPORARY: Hardcode the file ID for testing
      let referencedFiles: string[] = []
      if (userMessage.includes('@Papers/AIevo.pdf')) {
        console.log('Detected @Papers/AIevo.pdf mention, hardcoding file ID')
        referencedFiles = ['6c3416de-33ec-4f89-bdee-efea411d094b'] // The correct file ID from database
      } else {
        referencedFiles = await parseFileMentions(userMessage)
      }

      console.log('Parsed referenced files:', referencedFiles)

      // Add user message immediately to the conversation
      const tempUserMessage = {
        id: `temp-user-${Date.now()}`,
        conversation_id: "",
        role: "user" as const,
        content: userMessage,
        metadata: {},
        created_at: new Date().toISOString(),
      }

      // Add thinking indicator
      const thinkingMessage = {
        id: `temp-thinking-${Date.now()}`,
        conversation_id: "",
        role: "assistant" as const,
        content: "Thinking...",
        metadata: { isThinking: true, startTime },
        created_at: new Date().toISOString(),
      }

      setMessages(prev => [...prev, tempUserMessage, thinkingMessage])

      // Send message to AI chat endpoint with referenced files
      const response = await apiClient.documents.chat(document.id, userMessage, referencedFiles)

      // Calculate response time
      const endTime = Date.now()
      const responseTime = Math.round((endTime - startTime) / 1000)

      // Remove thinking message and add real messages with response time
      setMessages(prev => {
        const withoutThinking = prev.filter(msg => !msg.metadata?.isThinking)
        const aiMessageWithTime = {
          ...response.ai_message,
          metadata: {
            ...response.ai_message.metadata,
            responseTime
          }
        }
        return [...withoutThinking.slice(0, -1), response.user_message, aiMessageWithTime]
      })

      // Update current version if a new one was created
      if (response.new_version) {
        setCurrentVersion(response.new_version)
        setLatexCode(response.new_version.latex_code)

        // Auto-compile after LaTeX code update
        console.log('Auto-compiling after message...')
        setTimeout(() => {
          compileLatex(document.id, response.new_version.latex_code)
        }, 500)
      }

      // Update document data
      if (response.updated_document) {
        // Document title might have been updated by AI
        // You might want to update the parent component here
      }

    } catch (error) {
      console.error("Error sending message:", error)

      // Remove thinking message on error
      setMessages(prev => prev.filter(msg => !msg.metadata?.isThinking))

      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      })
    } finally {
      setIsSendingMessage(false)
      setIsThinking(false)
    }
  }

  const handleRecompile = async () => {
    try {
      // Use server-side LaTeX compilation for professional PDF generation
      const result = await compileLatex(document.id, latexCode)

      console.log("Compilation result:", result) // Debug log

      if (result.success && result.pdf_url) {
        // Wait a moment for the backend to update the version
        setTimeout(async () => {
          await loadDocumentData()
        }, 1000)

        toast({
          title: "Success",
          description: "Document compiled successfully! PDF is ready for viewing.",
        })
      } else {
        toast({
          title: "Compilation Error",
          description: result.errors?.[0] || "Failed to compile document",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error during compilation:", error)
      toast({
        title: "Error",
        description: "Failed to compile document",
        variant: "destructive",
      })
    }
  }

  // DISABLED: Auto-save functionality - only create versions on AI messages
  // This was creating too many versions. Now versions are only created when:
  // 1. AI responds to user messages
  // 2. User manually saves (if we add that feature later)

  // useEffect(() => {
  //   if (!latexCode || !currentVersion) return
  //   const timeoutId = setTimeout(async () => {
  //     try {
  //       if (latexCode !== currentVersion.latex_code) {
  //         await apiClient.documents.createVersion(document.id, {
  //           latex_code: latexCode,
  //           description: "Auto-saved"
  //         })
  //       }
  //     } catch (error) {
  //       console.error("Auto-save failed:", error)
  //     }
  //   }, 5000)
  //   return () => clearTimeout(timeoutId)
  // }, [latexCode, document.id, currentVersion])

  const handleDownload = async () => {
    try {
      const pdfUrl = currentVersion?.compiled_pdf_url || compilationResult?.pdf_url

      if (pdfUrl) {
        // Create download link
        const link = document.createElement('a')
        link.href = pdfUrl
        link.download = `${document.title}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        toast({
          title: "Success",
          description: "PDF download started",
        })
      } else {
        toast({
          title: "Error",
          description: "No compiled PDF available. Please compile the document first.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error downloading PDF:", error)
      toast({
        title: "Error",
        description: "Failed to download PDF",
        variant: "destructive",
      })
    }
  }

  const handleShare = async () => {
    try {
      // First, try to copy the direct document URL
      const documentUrl = `${window.location.origin}/dashboard/${document.id}`
      await navigator.clipboard.writeText(documentUrl)

      toast({
        title: "Success",
        description: "Document URL copied to clipboard",
      })
    } catch (error) {
      console.error("Error copying document URL:", error)

      // Fallback: try to create a share link
      try {
        const shareData = await apiClient.share.create(document.id, {
          permissions: "view",
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        })

        const shareUrl = `${window.location.origin}/share/${shareData.share_token}`

        // Copy to clipboard
        await navigator.clipboard.writeText(shareUrl)

        toast({
          title: "Success",
          description: "Share link copied to clipboard",
        })
      } catch (shareError) {
        console.error("Error creating share link:", shareError)
        toast({
          title: "Error",
          description: "Failed to copy document URL",
          variant: "destructive",
        })
      }
    }
  }

  const handleVersionSwitch = async (version: DocumentVersion) => {
    try {
      console.log('Switching to version:', version.version_number, 'with LaTeX length:', version.latex_code?.length)

      // Update local state immediately
      setCurrentVersion(version)
      setLatexCode(version.latex_code || '')

      // Auto-compile the switched version
      console.log('Auto-compiling switched version...')
      setTimeout(() => {
        compileLatex(document.id, version.latex_code || '')
      }, 500)

      toast({
        title: "Success",
        description: `Switched to version ${version.version_number}`,
      })
    } catch (error) {
      console.error("Error handling version switch:", error)
      toast({
        title: "Error",
        description: "Failed to switch version",
        variant: "destructive",
      })
    }
  }

  const handleManualSave = async () => {
    if (!latexCode || !currentVersion || latexCode === currentVersion.latex_code) {
      toast({
        title: "No Changes",
        description: "No changes to save",
      })
      return
    }

    try {
      setIsSaving(true)
      const newVersion = await apiClient.documents.createVersion(document.id, {
        latex_code: latexCode,
        description: "Manual save"
      })

      setCurrentVersion(newVersion)

      toast({
        title: "Success",
        description: "Document saved successfully",
      })
    } catch (error) {
      console.error("Manual save failed:", error)
      toast({
        title: "Error",
        description: "Failed to save document",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Mobile Menu */}
      <MobileMenu
        isOpen={mobileMenuOpen}
        onClose={onCloseMobileMenu}
        currentPage="document"
        onNavigate={onStateChange}
        onBackToWelcome={() => router.push("/dashboard")}
        documents={[]}
        onDocumentClick={() => {}}
      />

      {/* Chat Panel */}
      <div
        ref={chatPanelRef}
        style={{ width: isMobile ? '100%' : `${chatPanelWidth}px` }}
        className={`bg-card border-r border-border shadow-lg flex flex-col relative flex-shrink-0 ${
          isMobile ? (chatPanelCollapsed ? 'hidden' : 'absolute inset-0 z-50') : ''
        }`}
      >
        {/* Mobile Chat Header */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b border-border bg-card">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setChatPanelCollapsed(true)}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="w-5 h-5" />
              </Button>
              <div>
                <div className="text-lg font-medium">DeepDocX Assistant</div>
                <div className="text-xs text-muted-foreground">Professional collaboration</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-muted-foreground">Online</span>
            </div>
          </div>
        )}

        {/* Desktop Chat Header */}
        {!isMobile && (
          <div className="p-4 lg:p-6 border-b border-border bg-card">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg lg:text-xl font-medium text-foreground">DeepDocX Assistant</div>
                <div className="text-xs lg:text-sm text-muted-foreground mt-0.5">
                  Professional document collaboration
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-muted-foreground">Online</span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setChatPanelCollapsed(!chatPanelCollapsed)}
                  className="text-muted-foreground hover:text-foreground w-7 h-7 lg:w-8 lg:h-8"
                >
                  {chatPanelCollapsed ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Chat Messages */}
        <ScrollArea className="flex-1 p-4 lg:p-6">
          <div className="space-y-6 lg:space-y-8">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
              </div>
            ) : (
              messages.map((msg) => (
                <div key={msg.id} className="flex gap-4 lg:gap-5">
                  <Avatar className="w-9 h-9 lg:w-11 lg:h-11 flex-shrink-0 mt-1">
                    <AvatarFallback className={`text-sm lg:text-base font-medium ${
                      msg.role === "assistant" ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                    }`}>
                      {msg.role === "assistant" ? "AI" : "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-sm lg:text-base font-bold text-foreground">
                        {msg.role === "assistant"
                          ? "DeepDocX Assistant"
                          : user
                            ? `${user.first_name} ${user.last_name}`
                            : "You"
                        }
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(msg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                      {msg.metadata?.responseTime && (
                        <span className="text-xs text-muted-foreground italic">
                          Thought for {msg.metadata.responseTime} second{msg.metadata.responseTime !== 1 ? 's' : ''}
                        </span>
                      )}
                    </div>
                    <div className="text-sm lg:text-base text-foreground leading-relaxed whitespace-pre-wrap">
                      {msg.metadata?.isThinking ? (
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span className="italic">Thinking...</span>
                        </div>
                      ) : (
                        <div className="prose prose-sm lg:prose-base max-w-none text-foreground">
                          {msg.content}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Chat Input */}
        <div className="p-4 lg:p-6 border-t border-border bg-card">
          <div className="flex gap-3">
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Ask AI to help with your document..."
              className="flex-1 text-sm lg:text-base bg-background border-border text-foreground focus:ring-2 focus:ring-primary/20"
              onKeyDown={(e) => e.key === "Enter" && !e.shiftKey && handleSendMessage()}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!message.trim() || isSendingMessage}
              className="px-4 lg:px-5 bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-200"
            >
              {isSendingMessage ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
          <div className="mt-3 text-xs text-muted-foreground">
            Press Enter to send • Shift+Enter for new line
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Document Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Header */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleMobileMenu}
              className="text-muted-foreground hover:text-foreground"
            >
              <Menu className="w-6 h-6" />
            </Button>
            <div className="text-lg font-semibold truncate">{document.title}</div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onShowSettings}
              className="text-muted-foreground hover:text-foreground"
            >
              <Settings className="w-6 h-6" />
            </Button>
          </div>
        )}

        {/* Document Header */}
        <div className="p-4 lg:p-6 border-b border-border bg-background">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 lg:gap-4 min-w-0 flex-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push("/dashboard")}
                className="text-muted-foreground hover:text-foreground flex-shrink-0"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>

              <h1 className="text-lg lg:text-2xl font-semibold truncate">
                {document.title}
              </h1>
              {compilationResult && (
                <div className="flex items-center gap-1">
                  {compilationResult.success ? (
                    <div className="compilation-status success">
                      <CheckCircle className="w-3 h-3" />
                    </div>
                  ) : (
                    <div className="compilation-status error">
                      <AlertCircle className="w-3 h-3" />
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center gap-3 lg:gap-4 flex-shrink-0">
              {/* Action Buttons */}

              {/* Save Button */}
              <Button
                onClick={handleManualSave}
                disabled={isSaving || !latexCode || !currentVersion || latexCode === currentVersion.latex_code}
                variant="outline"
                className="h-8 lg:h-10 px-3 border-border text-xs lg:text-sm"
              >
                {isSaving ? (
                  <Loader2 className="w-3 h-3 lg:w-4 lg:h-4 animate-spin" />
                ) : (
                  <Save className="w-3 h-3 lg:w-4 lg:h-4" />
                )}
              </Button>

              {/* Compile Button */}
              <Button
                onClick={handleRecompile}
                disabled={isCompiling}
                className="h-8 lg:h-10 px-3 bg-primary hover:bg-primary/90 text-primary-foreground text-xs lg:text-sm"
              >
                {isCompiling ? (
                  <Loader2 className="w-3 h-3 lg:w-4 lg:h-4 animate-spin" />
                ) : (
                  <RotateCcw className="w-3 h-3 lg:w-4 lg:h-4" />
                )}
              </Button>

              <Button
                onClick={handleDownload}
                variant="outline"
                className="h-8 lg:h-10 px-3 border-border text-xs lg:text-sm"
              >
                <Download className="w-3 h-3 lg:w-4 lg:h-4" />
              </Button>

              <Button
                onClick={handleShare}
                variant="outline"
                className="h-8 lg:h-10 px-3 border-border text-xs lg:text-sm"
              >
                <Share className="w-3 h-3 lg:w-4 lg:h-4" />
              </Button>

              {/* View Mode Buttons */}
              <Button
                variant={viewMode === "pdf" ? "default" : "outline"}
                onClick={() => setViewMode("pdf")}
                className={`h-8 lg:h-10 px-2 lg:px-4 text-xs lg:text-sm ${
                  viewMode === "pdf" ? "bg-primary text-primary-foreground" : "border-border"
                }`}
              >
                <Eye className="w-3 h-3 lg:w-4 lg:h-4 lg:mr-2" />
                <span className="hidden lg:inline">Preview</span>
              </Button>
              <Button
                variant={viewMode === "latex" ? "default" : "outline"}
                onClick={() => setViewMode("latex")}
                className={`h-8 lg:h-10 px-2 lg:px-4 text-xs lg:text-sm ${
                  viewMode === "latex" ? "bg-primary text-primary-foreground" : "border-border"
                }`}
              >
                <Code className="w-3 h-3 lg:w-4 lg:h-4 lg:mr-2" />
                <span className="hidden lg:inline">LaTeX Code</span>
              </Button>

              {/* Version History Toggle */}
              {!isMobile && (
                <Button
                  variant="outline"
                  onClick={() => setVersionPanelCollapsed(!versionPanelCollapsed)}
                  className="h-8 lg:h-10 px-2 lg:px-4 border-border text-xs lg:text-sm"
                >
                  <Clock className="w-3 h-3 lg:w-4 lg:h-4 lg:mr-2" />
                  <span className="hidden lg:inline">
                    {versionPanelCollapsed ? "Show" : "Hide"} History
                  </span>
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Document Content */}
        <div className="flex-1 p-4 lg:p-8 overflow-auto bg-background">
          {viewMode === "pdf" ? (
            <div className="max-w-4xl mx-auto">
              {(() => {
                const pdfUrl = currentVersion?.compiled_pdf_url || compilationResult?.pdf_url;
                console.log("PDF URL check:", {
                  currentVersionPdfUrl: currentVersion?.compiled_pdf_url,
                  compilationResultPdfUrl: compilationResult?.pdf_url,
                  finalPdfUrl: pdfUrl,
                  compilationResult: compilationResult
                });
                return pdfUrl;
              })() ? (
                <PDFErrorBoundary
                  pdfUrl={currentVersion?.compiled_pdf_url || compilationResult?.pdf_url || ''}
                  onRetry={() => window.location.reload()}
                >
                  <EnhancedPDFViewer
                    pdfUrl={currentVersion?.compiled_pdf_url || compilationResult?.pdf_url || ''}
                    documentTitle={document.title}
                    onDownload={handleDownload}
                  />
                </PDFErrorBoundary>
              ) : (
                <div className="bg-card border border-border rounded-lg min-h-[600px] lg:min-h-[800px] flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-muted-foreground mb-4">
                      <FileText className="w-16 h-16 mx-auto mb-4" />
                    </div>
                    <h3 className="text-2xl font-semibold mb-4 text-foreground">No Document Preview Available</h3>
                    <p className="text-muted-foreground mb-6 max-w-md mx-auto leading-relaxed">
                      {latexCode ?
                        "Click 'Compile Document' to generate a PDF from your LaTeX code with professional typesetting, advanced graphics, and mathematical formulas." :
                        "Start a conversation with AI to generate your document content and watch it come to life."
                      }
                    </p>
                    {latexCode && (
                      <Button
                        onClick={handleRecompile}
                        disabled={isCompiling}
                        className="bg-primary hover:bg-primary/90 text-primary-foreground"
                      >
                        {isCompiling ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Compiling...
                          </>
                        ) : (
                          <>
                            <RotateCcw className="w-4 h-4 mr-2" />
                            Compile Document
                          </>
                        )}
                      </Button>
                    )}

                    {/* Compilation status */}
                    {compilationResult && (
                      <div className={`compilation-status mt-6 ${
                        compilationResult.success ? 'success' : 'error'
                      }`}>
                        {compilationResult.success ? (
                          <>
                            <CheckCircle className="w-5 h-5" />
                            <span>Compilation successful!</span>
                          </>
                        ) : (
                          <>
                            <AlertCircle className="w-5 h-5" />
                            <span>Compilation failed: {compilationResult.errors?.[0]}</span>
                          </>
                        )}
                      </div>
                    )}

                    {isCompiling && (
                      <div className="compilation-status compiling mt-6">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        <span>Compiling your document with professional LaTeX engine...</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="max-w-6xl mx-auto">
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4 text-foreground">LaTeX Editor</h3>
                <p className="text-muted-foreground mb-4 text-sm">
                  Write your LaTeX code below. The enhanced compiler supports advanced packages for graphics, mathematics, and professional typesetting.
                </p>
                <Textarea
                  value={latexCode}
                  onChange={(e) => setLatexCode(e.target.value)}
                  className="w-full h-[600px] lg:h-[800px] font-mono text-sm resize-none bg-background border-border text-foreground"
                  placeholder="% Professional LaTeX Template - Ready for complex documents
% This template includes comprehensive packages for advanced features

\documentclass[11pt,a4paper]{article}

% Essential packages
\usepackage{amsmath,amsfonts,amssymb,amsthm}  % Advanced mathematics
\usepackage{graphicx,float,caption}           % Figures and captions
\usepackage{geometry,fancyhdr}                % Page layout and headers
\usepackage{xcolor,tcolorbox}                 % Colors and decorative boxes
\usepackage{booktabs,array,longtable}         % Professional tables
\usepackage{tikz,pgfplots}                    % Advanced graphics and plots
\usepackage{listings,verbatim}                % Code listings
\usepackage{hyperref,url}                     % Links and references
\usepackage{natbib}                           % Bibliography
\usepackage{enumitem}                         % Enhanced lists

% Page setup
\geometry{margin=1in}
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{\leftmark}
\fancyfoot[C]{\thepage}

% TikZ libraries for advanced graphics
\usetikzlibrary{shapes,arrows,positioning,calc}
\pgfplotsset{compat=1.18}

% Document information
\title{Professional Document Template}
\author{Your Name}
\date{\today}

\begin{document}
\maketitle
\tableofcontents
\newpage

\section{Introduction}
This template supports advanced LaTeX features including:
\begin{itemize}
    \item Advanced mathematics with \texttt{amsmath}
    \item Professional figures with TikZ and PGFPlots
    \item Decorative boxes with \texttt{tcolorbox}
    \item Professional tables with \texttt{booktabs}
    \item Code listings with syntax highlighting
\end{itemize}

\section{Mathematics}
Display equations:
\begin{equation}
    E = mc^2
\end{equation}

\section{Graphics Example}
\begin{figure}[H]
    \centering
    \begin{tikzpicture}
        \draw[->] (0,0) -- (3,0) node[right] {$x$};
        \draw[->] (0,0) -- (0,2) node[above] {$y$};
        \draw[domain=0:2.5] plot (\x,{0.5*\x*\x});
    \end{tikzpicture}
    \caption{A simple parabola}
\end{figure}

\section{Conclusion}
This template provides a solid foundation for professional documents.

\end{document}"
                />
                <div className="mt-4 space-y-2">
                  <div className="text-xs text-muted-foreground">
                    <strong>Tips:</strong> Your code is automatically saved as you type. Use the compile button to generate a professional PDF.
                  </div>
                  <div className="text-xs text-foreground bg-primary/10 p-3 rounded-lg border border-primary/20">
                    <strong>Comprehensive Package Support:</strong> The server supports advanced LaTeX packages including:
                    TikZ/PGFPlots (graphics), tcolorbox (decorative boxes), booktabs (tables), listings (code),
                    amsmath (mathematics), natbib (bibliography), and many more professional typesetting packages.
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <strong>Advanced features:</strong> Complex mathematics, scientific diagrams, professional tables,
                    code syntax highlighting, decorative boxes, bibliography management, and publication-quality typography.
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <strong>Note:</strong> Some specialized packages (like algorithm.sty) may not be available.
                    Use algorithmicx or pseudocode alternatives instead.
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        </div>

        {/* Version History Panel */}
        {!isMobile && !versionPanelCollapsed && (
          <VersionHistoryPanel
            documentId={document.id}
            currentVersion={currentVersion}
            onVersionSwitch={handleVersionSwitch}
            className="w-80 flex-shrink-0 h-full"
          />
        )}
      </div>
    </div>
  )
}
