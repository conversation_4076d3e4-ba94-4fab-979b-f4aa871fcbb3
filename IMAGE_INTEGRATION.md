# Image Integration in DeepDocX

## Overview
DeepDocX now supports seamless integration of images into LaTeX documents with automatic file resolution and error handling.

## Features

### 1. Enhanced File Management
- **Nested Folder Support**: Create folders within folders for better organization
- **Breadcrumb Navigation**: See your current location and navigate easily
- **Image File Indicators**: Visual indicators for image files with special styling

### 2. Image Reference System
- **@mention Style**: Reference images using `@folder/image.png` syntax
- **Direct Reference**: Use filename directly like `image.png`
- **Copy LaTeX Reference**: Right-click on image files to copy LaTeX reference

### 3. LaTeX Compilation Enhancements
- **Automatic Image Copying**: Referenced images are automatically copied to compilation directory
- **Missing Image Handling**: Graceful handling of missing images with placeholder generation
- **Enhanced Error Messages**: Clear error messages for missing or problematic images

## How to Use

### Uploading Images
1. Navigate to Files Management
2. Create folders to organize your images (optional)
3. Upload image files (PNG, JPG, JPEG, GIF, SVG, WebP supported)

### Referencing Images in LaTeX
1. **Method 1 - Copy Reference**:
   - Right-click on any image file
   - Select "Copy LaTeX Reference"
   - Paste in your LaTeX document

2. **Method 2 - Manual Reference**:
   ```latex
   \begin{figure}[h]
       \centering
       \includegraphics[width=0.8\textwidth]{image.png}
       \caption{Your image caption}
       \label{fig:your-label}
   \end{figure}
   ```

3. **Method 3 - Folder Structure**:
   ```latex
   \includegraphics{@folder/subfolder/image.png}
   ```

### Supported Image Formats
- PNG (.png)
- JPEG (.jpg, .jpeg)
- GIF (.gif)
- SVG (.svg)
- WebP (.webp)
- BMP (.bmp)
- TIFF (.tiff)

## Error Handling

### Missing Images
If an image is referenced but not found:
1. A placeholder will be generated automatically
2. Compilation will continue with a warning box
3. Error message will indicate the missing file

### Invalid Paths
- The system searches multiple paths for images
- Folder structure is respected when using @mention syntax
- Direct filename references search all user files

## Best Practices

1. **Organize with Folders**: Use folders to keep images organized by project or type
2. **Descriptive Names**: Use clear, descriptive filenames for your images
3. **Consistent Format**: Stick to common formats (PNG, JPG) for best compatibility
4. **Size Optimization**: Optimize image sizes before upload for faster compilation

## API Endpoints

### Get User Images
```
GET /api/files/images
```
Returns all image files for the current user with LaTeX reference suggestions.

### Upload Files
```
POST /api/files/upload
```
Upload files to a specific folder (optional).

## Technical Details

### Image Resolution Process
1. Parse LaTeX code for `\includegraphics` commands
2. Extract image paths and filenames
3. Search user's files for matching images
4. Copy found images to compilation directory
5. Generate placeholders for missing images

### LaTeX Enhancement
The system automatically adds these packages to your LaTeX documents:
- `\usepackage{graphicx}` - Core graphics support
- `\usepackage{grffile}` - Better filename handling
- `\DeclareGraphicsExtensions{.pdf,.png,.jpg,.jpeg,.gif}` - Supported formats
- `\graphicspath{{./}{images/}{figures/}}` - Search paths

### Error Recovery
Missing images are replaced with:
```latex
\IfFileExists{filename}{%
    \includegraphics[options]{filename}%
}{%
    \fbox{\parbox{5cm}{\centering Missing Image:\\texttt{filename}}}%
}
```

This ensures compilation continues even with missing images.
